# tests/test_category_export.py
from unittest.mock import MagicMock

from src.es_export_run import main


class TestCategoryExportMode:
    """
    Tests for the category export mode.
    """

    def test_placeholder(self):
        """
        Placeholder test to ensure the test suite is set up correctly.
        """
        assert True

    def test_category_export_mode_triggers_use_case(self, monkeypatch):
        """
        Tests that running with `--mode category_export` triggers the correct use case.
        """
        # Mock the use case
        mock_use_case_instance = MagicMock()
        mock_use_case_class = MagicMock(return_value=mock_use_case_instance)
        monkeypatch.setattr(
            "src.es_export_run.ICategoryExportUseCase", mock_use_case_class
        )

        # Run the script with the specified mode
        main(["--mode", "category_export"])

        # Assert that the use case was called
        mock_use_case_instance.execute.assert_called_once()

import json

if __name__ == "__main__":
    with open("data/categories_2024.json", encoding="utf-8") as f:
        data = json.load(f)

    all_main_categories = []
    all_subcategories = []
    all_main_sub_categories = []
    for main_cat, sublist in data.items():
        all_subcategories.extend(sublist)
        all_main_categories.append(main_cat)
        for sub_cat in sublist:
            all_main_sub_categories.append(f"{main_cat}_{sub_cat}")

    print(f"Total main categories: {len(all_main_categories)}")
    print(f"Total sub categories: {len(all_subcategories)}")

    # with open("subcategories_2024.json", "w", encoding="utf-8") as out_f:
    #     json.dump(all_subcategories, out_f, ensure_ascii=False, indent=2)

    with open("data/main_sub_categories_2025.json", encoding="utf-8") as f:
        all_main_sub_categories_2025 = json.load(f)
    all_main_sub_categories_2025 = set(all_main_sub_categories_2025)

    for main_sub_category in all_main_sub_categories:
        if main_sub_category not in all_main_sub_categories_2025:
            print(f"Missing sub category: {main_sub_category}")

import datetime
import sys
from math import ceil


def get_start_and_end_date_from_calendar_week(year: str, calendar_week: str):
    """
    Get the start and end dates of a given calendar week.

    Args:
        year (str): The year of the calendar week.
        calendar_week (str): The calendar week number.

    Returns:
        tuple: A tuple containing the start date and end date of the calendar week.
              The start date is in the format "%Y/%m/%d" and the end date is also in the same format.
    """
    monday = datetime.datetime.strptime(f"{year}-{calendar_week}-1", "%Y-%W-%w").date()
    return monday.strftime("%Y/%m/%d"), (
        monday + datetime.timedelta(days=6.9)
    ).strftime("%Y/%m/%d")


def week_number_of_month(date_value: datetime.datetime):
    """
    Calculate the week number of a given date within the month.

    Args:
        date_value (datetime.datetime): The date for which the week number is to be calculated.

    Returns:
        int: The week number of the given date within the month.
    """

    # # First day of the month
    # first_day = date_value.replace(day=1)

    # # First week number of the month
    # first_week = first_day.isocalendar()[1]

    # # Week number of the current date
    # current_week = date_value.isocalendar()[1]

    # # Handle the case when January 1st is in the last week of the previous year
    # if first_week == 52 or first_week == 53:
    #     first_week = 0

    # # Calculate the week of the month
    # return current_week - first_week + 1

    # first_day = date_value.replace(day=1)
    # first_day_weekday = first_day.weekday()  # Monday is 0 and Sunday is 6

    # # Calculate the week number by adjusting for the first day of the week
    # adjusted_day = date_value.day + first_day_weekday
    # week_number = (adjusted_day - 1) // 7 + 1

    # return week_number

    # return date_value.isocalendar()[1] - date_value.replace(day=1).isocalendar()[1] + 1


def calculate_dates_by_start_and_end(
    dct_vars: dict, str_start_date: str, str_end_date: str
):
    lst_fetch_dates = []
    if dct_vars["lst_fetch_week"]:
        for calendar_week in dct_vars["lst_fetch_week"]:
            t_today_date = datetime.datetime.today()
            str_start_date, str_end_date = get_start_and_end_date_from_calendar_week(
                t_today_date.year, calendar_week
            )
            week_in_month = week_number_of_month(
                datetime.datetime.strptime(str_start_date, "%Y/%m/%d")
            )
            lst_fetch_dates.append(
                {
                    "str_start_date": str_start_date,
                    "str_end_date": str_end_date,
                    "week_in_year": calendar_week,
                    "week_in_month": week_in_month,
                }
            )

    elif str_start_date and str_end_date:
        date_format = "%Y/%m/%d"
        try:
            t_start_date = datetime.datetime.strptime(str_start_date, date_format)
            t_end_date = datetime.datetime.strptime(str_end_date, date_format)
            if t_end_date < t_start_date:
                raise ValueError("End time must be greater than start time")
        except ValueError as e:
            print(f"{e}")
            sys.exit(1)

        start_year = t_start_date.year
        end_year = t_end_date.year

        start_calendar_week = t_start_date.isocalendar().week
        end_calendar_week = t_end_date.isocalendar().week
        # for calendar_week in range(start_calendar_week, end_calendar_week + 1):
        #     str_start_date, str_end_date = get_start_and_end_date_from_calendar_week(
        #         t_start_date.year, calendar_week
        #     )
        #     week_in_month = week_number_of_month(t_start_date)
        #     lst_fetch_dates.append(
        #         {
        #             "str_start_date": str_start_date,
        #             "str_end_date": str_end_date,
        #             "week_in_year": calendar_week,
        #             "week_in_month": week_in_month,
        #         }
        #     )
        for year in range(start_year, end_year + 1):
            if year == start_year:
                start_week = start_calendar_week
            else:
                start_week = 1

            if year == end_year:
                end_week = end_calendar_week
            else:
                end_week = datetime.datetime(year, 12, 31).isocalendar().week
                # Adjust for the case when the last day of the year is part of week 1 of the next year
                if end_week == 1:
                    end_week = datetime.datetime(year, 12, 24).isocalendar().week

            for calendar_week in range(start_week, end_week + 1):
                str_week_start_date, str_week_end_date = (
                    get_start_and_end_date_from_calendar_week(year, calendar_week)
                )
                week_in_month = week_number_of_month(
                    datetime.datetime.strptime(str_week_start_date, date_format)
                )
                lst_fetch_dates.append(
                    {
                        "str_start_date": str_week_start_date,
                        "str_end_date": str_week_end_date,
                        "week_in_year": calendar_week,
                        "week_in_month": week_in_month,
                    }
                )

    else:  # not str_start_date or not str_end_date
        t_today_date = datetime.datetime.today()
        calendar_week = t_today_date.isocalendar().week
        str_start_date, str_end_date = get_start_and_end_date_from_calendar_week(
            t_today_date.year, calendar_week
        )
        week_in_month = week_number_of_month(t_today_date)
        lst_fetch_dates.append(
            {
                "str_start_date": str_start_date,
                "str_end_date": str_end_date,
                "week_in_year": calendar_week,
                "week_in_month": week_in_month,
            }
        )

    return lst_fetch_dates


if __name__ == "__main__":
    lst_fetch_dates = calculate_dates_by_start_and_end(
        {"lst_fetch_week": []}, "2022/11/01", "2023/01/10"
    )

    print(f"lst_fetch_dates:{lst_fetch_dates}")

import datetime
import logging
import re
from typing import Dict, List, Union

import settings


def camel_to_snake(name):
    name = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", name).lower()


# Function to change dictionary keys
def change_keys(dict_obj: dict, key_dict: dict = {}):
    # return {key_dict.get(k, k): v for k, v in dict_obj.items()}
    return {camel_to_snake(k): v for k, v in dict_obj.items()}


# Function to change datetime format
def convert_time_format_and_key_names(
    dict_list: Union[List[dict], dict], lst_datetime_fields
):
    if isinstance(dict_list, list):
        new_list = []
        for _dict in dict_list:
            new_dict = {}
            for k, v in _dict.items():
                # Convert key names
                new_key = camel_to_snake(k)

                # Convert time format if key name contains 'time'
                if "time" in new_key or new_key in lst_datetime_fields:
                    if v:
                        try:
                            dt = datetime.datetime.strptime(v, "%Y/%m/%d/%H/%M")
                            new_v = dt.strftime("%Y/%m/%d %H:%M:%S")
                        except:
                            new_v = v
                    else:
                        new_v = None
                    new_dict[new_key] = new_v
                elif new_key == "qn_feed_back":
                    new_dict["qn_feedback"] = v
                else:
                    new_dict[new_key] = v
            new_list.append(new_dict)
        return new_list
    elif isinstance(dict_list, dict):
        new_dict = {}
        for k, v in dict_list.items():
            # Convert key names
            new_key = camel_to_snake(k)

            # Convert time format if key name contains 'time'
            if "time" in new_key or new_key in lst_datetime_fields:
                if v:
                    try:
                        dt = datetime.datetime.strptime(v, "%Y/%m/%d/%H/%M")
                        new_v = dt.strftime("%Y/%m/%d %H:%M:%S")
                    except:
                        new_v = v
                else:
                    new_v = None
                new_dict[new_key] = new_v
            elif new_key == "qn_feed_back":
                new_dict["qn_feedback"] = v
            else:
                new_dict[new_key] = v
        return new_dict


def initLogger(logDirPath, level):
    logger = logging.getLogger(settings.logger_name)
    logger.setLevel(level)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s - %(funcName)s() - %(lineno)s] %(message)s"
    )

    # if not os.path.exists(logDirPath):
    #     os.makedirs(logDirPath)
    # strToday = datetime.date.today()
    # strToday = strToday.strftime("%Y-%m-%d")
    # file_handler = logging.FileHandler(f'{logDirPath}/{strToday}.log')
    # file_handler.setLevel(level)
    # file_handler.setFormatter(formatter)

    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(formatter)

    # logger.addHandler(file_handler)
    logger.addHandler(stream_handler)
    logger.propagate = False

    return logger

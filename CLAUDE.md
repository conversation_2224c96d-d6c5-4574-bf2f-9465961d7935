# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# Coding Guidelines

@.claude/coding_guidelines.md

## Project Overview

This is a data ETL pipeline focused around Elasticsearch for AI-powered analysis of citizen petition cases. The system consists of two main components:

1. **NER Worker**: Performs Named Entity Recognition on petition content to enrich data dimensions
2. **GPT Summarizer**: Generates weekly summary reports and statistics by category using LLM

## Key Commands

### Environment Setup

```bash
# Install dependencies using Poetry
poetry install

# Set up environment variables
# Copy .env.example to .env (if exists) or create manually
# Key variables: TYCG_BE_RUNTIME_ENV, ELASTICSEARCH_*, AZURE_OPENAI_*, OPENAI_API_KEY, LINE_NOTIFY_TOKEN_*
```

### Running NER Worker

```bash
# Default run (uses env vars DATA_API_START_DATE, DATA_API_END_DATE, DATA_API_DATE)
poetry run python src/ner_worker_run.py

# With date range parameters (overrides env vars)
poetry run python src/ner_worker_run.py -s 2024/01/01 -e 2024/01/31
```

### Running GPT Summarizer

```bash
# Default run (processes last week automatically)
poetry run python src/gpt_summary_run.py

# With specific date range
poetry run python src/gpt_summary_run.py -s 2024/12/01 -e 2024/12/31

# Process specific weeks (set DATA_API_WEEK in .env as JSON array)
# Example: DATA_API_WEEK='[49, 51]'
poetry run python src/gpt_summary_run.py

# Update statistics only without regenerating summaries (set UPDATE_STATS_ONLY="True" in .env)
poetry run python src/gpt_summary_run.py
```

### Testing

```bash
# Run test files (no framework specified, check individual test files)
poetry run python tests/test.py
poetry run python tests/line_notify_test.py
poetry run python src/tools_test.py
```

## Architecture

The project follows Repository Pattern with clear separation between data access and business logic:

-   **Repository Layer**: `src/repository/` contains data access classes
    -   `ElasticsearchRepository`: Core CRUD operations for petition cases
    -   `ElasticsearchSummarizer`: Summary-specific queries and aggregations
    -   `connection_handler.py`: Elasticsearch connection management
-   **Business Logic**: Main runner scripts orchestrate the workflows
-   **Infrastructure**: Elasticsearch settings/mappings stored as JSON files in `src/es/`

### Data Flow

1. **NER Worker**: Queries unprocessed cases → CkipTransformer NER → Bulk update ES with NER results
2. **GPT Summarizer**: Query cases by category → Recursive summarization via GPT → Store summaries with statistics

### Key Design Patterns

-   **Repository Pattern**: Data access abstraction in `repository/` classes
-   **Facade Pattern**: `GPTReportWriter` simplifies LLM interaction complexity
-   **Infrastructure as Code**: ES index configurations in version-controlled JSON files

## Dependencies

-   **Python**: ^3.10 (managed via Poetry)
-   **Elasticsearch**: ^8.13.1 for data storage and search
-   **OpenAI**: ^1.30.5 for GPT summarization
-   **opencc-python-reimplemented**: ^0.1.7 for Chinese text processing
-   **CkipTransformer**: NER model (not in pyproject.toml, likely installed separately)

## Environment Configuration

The system supports multiple runtime environments via `TYCG_BE_RUNTIME_ENV` (PROD/DEV/LOCAL). Critical environment variables include Elasticsearch connection details, Azure OpenAI/OpenAI API keys, and LINE notification tokens.

## File Structure Notes

-   Generated summaries stored in `src/gpt_summary/YYYY/WXX/` hierarchy
-   Elasticsearch configurations in `src/es/`
-   Test data and examples in various test directories
-   Docker support via `Dockerfile_ner_worker` and `Dockerfile_gpt_summarizer`

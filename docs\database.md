# 資料模型 (Data Models)

本專案主要圍繞兩個核心的 Elasticsearch 索引進行操作：`petition-case` 用於儲存原始及擴充後的案件資料，`petition-summary` 用於儲存 AI 生成的摘要與統計數據。

## 1. 索引命名規則

為了便於管理和擴展，索引採用了基於年份的命名策略：

-   **案件索引**: `petition-case-YYYY` (例如: `petition-case-2024`)
-   **案件索引別名**: `petition-case-YYYY-alias`
-   **摘要索引**: `petition-summary-YYYY` (例如: `petition-summary-2024`)
-   **摘要索引別名**: `petition-summary-YYYY-alias`

所有程式碼操作都應針對**別名 (Alias)** 進行，而不是直接操作索引名稱。這為未來的索引重建 (Re-indexing) 和無縫切換提供了靈活性。

## 2. `petition-case` 索引

此索引是系統的原始資料來源，儲存每一筆市民陳情案件的詳細資訊。

### Mapping 設定：

```json
{
	"dynamic": "strict",
	"properties": {
		"case_id": {
			"type": "keyword"
		},
		"case_source": {
			"type": "keyword"
		},
		"case_status": {
			"type": "keyword",
			"index": false
		},
		"client_age": {
			"type": "keyword",
			"index": false
		},
		"client_name": {
			"type": "keyword",
			"doc_values": false
		},
		"client_sex": {
			"type": "keyword",
			"index": false
		},
		"coordinate": {
			"type": "geo_point"
		},
		"current_process_type": {
			"type": "keyword",
			"index": false
		},
		"custom_tag_list": {
			"type": "keyword"
		},
		"geo_occur_addr": {
			"type": "geo_point"
		},
		"hash_digest": {
			"type": "keyword",
			"doc_values": false
		},
		"main_category": {
			"type": "keyword"
		},
		"occur_addr": {
			"type": "text",
			"analyzer": "han_bigrams"
		},
		"occur_addr_1": {
			"type": "keyword"
		},
		"occur_addr_2": {
			"type": "keyword"
		},
		"petition_content": {
			"type": "text",
			"analyzer": "han_bigrams"
		},
		"petition_content_pos": {
			"properties": {
				"adj": {
					"type": "keyword",
					"index": false
				},
				"adv": {
					"type": "keyword",
					"index": false
				},
				"event": {
					"type": "keyword",
					"index": false
				},
				"fac": {
					"type": "keyword",
					"index": false
				},
				"fw": {
					"type": "keyword",
					"index": false
				},
				"gpe": {
					"type": "keyword",
					"index": false
				},
				"loc": {
					"type": "keyword",
					"index": false
				},
				"n": {
					"type": "keyword",
					"index": false
				},
				"neu": {
					"type": "keyword",
					"index": false
				},
				"norp": {
					"type": "keyword",
					"index": false
				},
				"org": {
					"type": "keyword",
					"index": false
				},
				"person": {
					"type": "keyword",
					"index": false
				},
				"product": {
					"type": "keyword",
					"index": false
				},
				"v": {
					"type": "keyword",
					"index": false
				},
				"work_of_art": {
					"type": "keyword",
					"index": false
				}
			}
		},
		"petition_subject": {
			"type": "text",
			"analyzer": "han_bigrams"
		},
		"petition_subject_pos": {
			"properties": {
				"adj": {
					"type": "keyword",
					"index": false
				},
				"adv": {
					"type": "keyword",
					"index": false
				},
				"event": {
					"type": "keyword",
					"index": false
				},
				"fac": {
					"type": "keyword",
					"index": false
				},
				"fw": {
					"type": "keyword",
					"index": false
				},
				"gpe": {
					"type": "keyword",
					"index": false
				},
				"loc": {
					"type": "keyword",
					"index": false
				},
				"n": {
					"type": "keyword",
					"index": false
				},
				"neu": {
					"type": "keyword",
					"index": false
				},
				"norp": {
					"type": "keyword",
					"index": false
				},
				"org": {
					"type": "keyword",
					"index": false
				},
				"person": {
					"type": "keyword",
					"index": false
				},
				"product": {
					"type": "keyword",
					"index": false
				},
				"v": {
					"type": "keyword",
					"index": false
				},
				"work_of_art": {
					"type": "keyword",
					"index": false
				}
			}
		},
		"process_list": {
			"type": "object",
			"enabled": false
		},
		"recent_process": {
			"properties": {
				"assign_time": {
					"type": "date",
					"index": false,
					"format": "yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis"
				},
				"close_method": {
					"type": "keyword",
					"index": false
				},
				"deadline": {
					"type": "date",
					"index": false,
					"format": "yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis"
				},
				"extend_days": {
					"type": "integer",
					"index": false
				},
				"extend_type": {
					"type": "keyword",
					"index": false
				},
				"is_extend": {
					"type": "keyword",
					"index": false,
					"doc_values": false
				},
				"is_over_due": {
					"type": "keyword",
					"index": false,
					"doc_values": false
				},
				"over_due_days": {
					"type": "float",
					"index": false
				},
				"over_due_type": {
					"type": "keyword",
					"index": false
				},
				"owner_unit": {
					"type": "keyword"
				},
				"owner_unit_1": {
					"type": "keyword"
				},
				"owner_unit_2": {
					"type": "keyword"
				},
				"owner_unit_3": {
					"type": "keyword"
				},
				"owner_unit_4": {
					"type": "keyword"
				},
				"owner_unit_5": {
					"type": "keyword"
				},
				"process_id": {
					"type": "keyword",
					"doc_values": false
				},
				"process_type": {
					"type": "keyword",
					"index": false
				},
				"qn_answer": {
					"type": "keyword",
					"index": false
				},
				"qn_feedback": {
					"type": "text",
					"analyzer": "han_bigrams"
				},
				"qn_other_reason": {
					"type": "text",
					"analyzer": "han_bigrams"
				},
				"qn_reason_list": {
					"type": "keyword",
					"index": false
				},
				"receive_time": {
					"type": "date",
					"index": false,
					"format": "yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis"
				},
				"reply_content": {
					"type": "text",
					"analyzer": "han_bigrams"
				},
				"reply_time": {
					"type": "date",
					"index": false,
					"format": "yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis"
				}
			}
		},
		"record_channel": {
			"type": "keyword",
			"index": false
		},
		"record_time": {
			"type": "date",
			"index": false,
			"format": "yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis"
		},
		"sub_category": {
			"type": "keyword",
			"index": false
		}
	}
}
```

### 欄位詳解

| 欄位名稱               | 資料類型  | 說明                                                            |
| :--------------------- | :-------- | :-------------------------------------------------------------- |
| `case_id`              | `keyword` | 案件的唯一識別碼，通常由來源系統提供。                          |
| `record_time`          | `date`    | 案件記錄時間，格式為 `yyyy/MM/dd`。是所有時間範圍查詢的基礎。   |
| `main_category`        | `keyword` | 案件主分類，例如「噪音、污染及環境維護」。                      |
| `sub_category`         | `keyword` | 案件次分類，例如「住家、改裝車噪音」。                          |
| `petition_subject`     | `text`    | 陳情主旨。使用 `standard` 分析器。                              |
| `petition_content`     | `text`    | 陳情內容。使用 `standard` 分析器。                              |
| `petition_subject_pos` | `object`  | **(由 NER Worker 新增)** 儲存從主旨中提取的命名實體。           |
| `...GPE`               | `keyword` | 地理政治實體 (Geopolitical Entity)，如城市、地區。              |
| `...PERSON`            | `keyword` | 人名。                                                          |
| `...ORG`               | `keyword` | 組織名。                                                        |
| `...DATE`              | `keyword` | 日期。                                                          |
| `...TIME`              | `keyword` | 時間。                                                          |
| `...MONEY`             | `keyword` | 金額。                                                          |
| `petition_content_pos` | `object`  | **(由 NER Worker 新增)** 儲存從內容中提取的命名實體，結構同上。 |

---

## 3. `petition-summary` 索引

此索引儲存由 `GPT Summarizer` Worker 生成的分析結果，是最終應用的主要資料來源。

### Mapping 設定

# petition-summary Index Mapping

```json
{
	"dynamic": "strict",
	"properties": {
		"main_category": {
			"type": "keyword",
			"index": false
		},
		"sub_category": {
			"type": "keyword",
			"index": false
		},
		"summary": {
			"type": "text",
			"analyzer": "han_bigrams"
		},
		"year": {
			"type": "integer",
			"index": false
		},
		"month": {
			"type": "integer",
			"index": false
		},
		"week_in_year": {
			"type": "integer",
			"index": false
		},
		"start_date": {
			"type": "date",
			"format": "yyyy/MM/dd",
			"index": false
		},
		"end_date": {
			"type": "date",
			"format": "yyyy/MM/dd",
			"index": false
		},
		"case_count_current_period": {
			"type": "integer",
			"index": false
		},
		"case_count_previous_period": {
			"type": "integer",
			"index": false
		},
		"case_count_growth_rate": {
			"type": "float",
			"index": false
		},
		"case_count_percentage": {
			"type": "float",
			"index": false
		},
		"report_name": {
			"type": "text",
			"analyzer": "han_bigrams"
		},
		"report_type": {
			"type": "keyword"
		},
		"created_time": {
			"type": "date",
			"format": "yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis",
			"index": false
		},
		"updated_time": {
			"type": "date",
			"format": "yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis",
			"index": false
		}
	}
}
```

### 欄位詳解

| 欄位名稱                     | 資料類型  | 說明                                                                                                                   |
| :--------------------------- | :-------- | :--------------------------------------------------------------------------------------------------------------------- |
| `_id`                        | `keyword` | 由 `main_category`, `sub_category`, `year`, `month`, `week_in_year` 透過 `SHA-256` 雜湊生成的唯一文件 ID，確保冪等性。 |
| `main_category`              | `keyword` | 摘要對應的主分類。                                                                                                     |
| `sub_category`               | `keyword` | 摘要對應的次分類。                                                                                                     |
| `year`                       | `integer` | 摘要對應的年份。                                                                                                       |
| `month`                      | `integer` | 摘要對應的月份。                                                                                                       |
| `week_in_year`               | `integer` | 摘要對應的 ISO 週次。                                                                                                  |
| `start_date`                 | `date`    | 該週的起始日期。                                                                                                       |
| `end_date`                   | `date`    | 該週的結束日期。                                                                                                       |
| `summary`                    | `text`    | **(由 GPT 生成)** 一個包含 3-5 個摘要段落的陣列。                                                                      |
| `case_count_current_period`  | `integer` | 當期（本週）的案件總數。                                                                                               |
| `case_count_previous_period` | `integer` | 前期（上週）的案件總數。                                                                                               |
| `case_count_growth_rate`     | `float`   | 案件數增長率。計算公式: `(當期 - 前期) / 前期`。                                                                       |
| `case_count_percentage`      | `float`   | 案件數佔比。計算公式: `當期案件數 / 當期所有案件總數`。                                                                |
| `report_name`                | `keyword` | 報告名稱，用於識別和分組。                                                                                             |
| `report_type`                | `keyword` | 報告類型，例如 "Weekly"。                                                                                              |
| `created_time`               | `date`    | 文件的創建時間。                                                                                                       |
| `updated_time`               | `date`    | 文件的最後更新時間。                                                                                                   |

# 架構設計 (Architecture Design)

本專案採用以 Elasticsearch 為中心的 ETL (Extract, Transform, Load) 資料管道架構。整體流程被設計為兩個獨立但有前後依賴關係的 Worker：`NER Worker` 和 `GPT Summarizer`。

## 1. 系統架構圖

```mermaid
graph TD
    subgraph "資料來源 (Data Source)"
        A[外部系統/資料庫] -->|批次匯入| B[ES Index: petition-case-YYYY]
    end

    subgraph "NER Worker (資料擴充)"
        B -- "1. 查詢未處理案件" --> C(ner_worker_run.py)
        C -- "2. 呼叫 NER 模型" --> D[CkipTransformer Model]
        D -- "3. 回傳 NER 結果" --> C
        C -- "4. Bulk Update 案件" --> B
    end

    subgraph "GPT Summarizer (資料分析與生成)"
        B -- "5. 依分類查詢案件" --> E(gpt_summary_run.py)
        E -- "6. 呼叫 GPT API" --> F{Azure/OpenAI GPT-4o-mini}
        F -- "7. 回傳摘要" --> E
        E -- "8. 寫入摘要與統計數據" --> G[ES Index: petition-summary-YYYY]
    end

    subgraph "最終產出 (Final Output)"
        G -- "查詢/應用" --> H[應用程式/儀表板]
    end

    style B fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#f9f,stroke:#333,stroke-width:2px
```

## 2. 設計模式

本專案採用了數個關鍵的設計模式來確保程式碼的品質、可維護性和擴展性。

-   **Repository Pattern (倉儲模式)**: 這是專案中最核心的設計模式。透過 `ElasticsearchRepository` 和 `ElasticsearchSummarizer` 類別，將資料庫操作（Elasticsearch 的查詢、寫入、更新）與上層的業務邏輯（在 `ner_worker_run.py` 和 `gpt_summary_run.py` 中）完全解耦。

    -   **優點**: 業務邏輯層不需要知道底層資料庫的細節，使得程式碼更清晰。未來若要更換或升級資料庫，只需修改 Repository 層的實作，而不需要大幅改動業務邏輯。

-   **Facade Pattern (外觀模式)**: `GPTReportWriter` 類別提供了一個簡潔的介面 `summarize_documents`，它封裝了所有與大型語言模型互動的複雜邏輯，包括：

    -   API 服務的備援切換 (Azure/OpenAI)。
    -   API 呼叫的重試機制。
    -   Token 使用量的計算與追蹤。
    -   處理長文本的遞迴式摘要演算法。
    -   **優點**: 讓上層呼叫者能用非常簡單的方式來執行一個複雜的摘要任務，無需關心內部細節。

-   **Infrastructure as Code (基礎設施即代碼)**: 專案將 Elasticsearch 的索引設定 (Settings) 與映射 (Mappings) 儲存為 `.json` 檔案 (`docs/database.md` 等)。
    -   **優點**: 這確保了在不同環境 (開發、測試、生產) 中建立的索引結構都是一致的，避免了因手動設定錯誤導致的問題，也便於版本控制。

## 3. 資料流詳解

### NER Worker 資料流

1.  **觸發**: 排程或手動執行 `ner_worker_run.py`。
2.  **參數解析**: 讀取命令列參數 (`-s`, `-e`) 或 `.env` 中的環境變數，以確定要處理的日期範圍。
3.  **查詢**: `ElasticsearchRepository.search_to_be_ner_doc` 方法建立一個 `bool` 查詢，找出在指定日期範圍內，且 `must_not` 存在 `petition_content_pos.*` 或 `petition_subject_pos.*` 欄位的案件。
4.  **滾動查詢 (Scroll)**: 為了高效處理可能存在的大量資料，使用 Elasticsearch Scroll API 來逐批獲取結果，避免了深度分頁 (deep pagination) 的效能問題。
5.  **平行處理**: 對於每一批次的案件，使用 `concurrent.futures.ThreadPoolExecutor` 建立執行緒池，平行地對 `petition_content` 和 `petition_subject` 兩個欄位進行 NER 處理，以加速運算。
6.  **NER 執行**: `CkipTransformer` 模型被呼叫以對文本進行命名實體辨識。
7.  **資料更新**: 將辨識出的 NER 結果（如 GPE, PERSON 等）組織成 Elasticsearch `update` 操作所需的 `doc` 格式。
8.  **批次寫入**: 使用 `helpers.bulk` API 將多個 `update` 操作合併為單一請求，批次更新回 Elasticsearch，這是最高效的寫入方式。
9.  **監控與告警**: 執行成功、失敗或發生例外時，都會透過 `send_line_notify` 發送通知，以便即時掌握系統狀態。

### GPT Summarizer 資料流

1.  **觸發**: 排程或手動執行 `gpt_summary_run.py`。
2.  **日期計算**: 程式會根據傳入的參數（週次或日期範圍）計算出所有需要處理的週，以及每一週的具體起訖日期。若無參數，則預設處理上週。
3.  **分類查詢**: `ElasticsearchSummarizer.get_category_group` 方法使用 Elasticsearch 的 `terms` 聚合 (Aggregation) 功能，一次性地取得指定時間範圍內所有案件的「主分類」與「次分類」組合。
4.  **迭代處理**: 程式會遍歷每一個「主分類/次分類」的組合，逐一生成報告。
5.  **統計計算**:
    -   使用 `msearch` API 在單一請求中，同時查詢**當期**與**前期**（上一週）此分類的案件總數。
    -   根據查詢結果，計算出**增長率**和在所有案件中的**佔比**。
6.  **文件檢索**: `ElasticsearchSummarizer.retrieve_all_documents_by_category` 檢索出當期此分類下的所有案件內文。
7.  **遞迴式摘要 (Recursive Summarization)**: 這是此 Worker 的核心演算法，由 `GPTReportWriter.summarize_documents` 執行：
    -   **分批 (Batching)**: 由於單一分類下的案件總字數可能遠超 GPT 的上下文長度限制，程式會先將多個案件內文合併成約 3000 字元的批次。
    -   **初步摘要**: 對每個批次呼叫 GPT API，生成一個約 90 字的初步摘要。
    -   **遞迴合併**: 如果上一步產生了多個初步摘要，程式會將這些初步摘要再作為新的內容，再次呼叫 GPT API 進行更上層的摘要。此過程會一直重複，直到只剩下一個最終的摘要版本（包含 3~5 個段落）。
8.  **資料模型建立**: 將最終的摘要結果、先前計算的統計數據、分類資訊、時間維度等，組合成一個完整的 `petition-summary` 文件。
9.  **冪等性寫入**: 使用 `helpers.bulk` API，並將操作類型設為 `update` 搭配 `doc_as_upsert=True`。文件的 ID 是由分類和時間等資訊雜湊而成，這確保了即使重複執行同一週的任務，也只會更新（Update）已存在的文件，而不會重複創建（Insert），保證了操作的冪等性。

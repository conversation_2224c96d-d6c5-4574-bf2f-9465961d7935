# Software Architecture Guidelines

This project follows the clean architecture. The following document outlines the software architecture principles for our project.

## 1. The Dependency Rule

Dependencies must only point inwards, from outer layers to inner layers. The flow of control starts from the cli entry point and moves towards the core business logic.

-   `Use Case` → `Repository`

## 2. Layer Responsibilities

### Use Case

-   **Responsibility**: To contain and execute core, application-specific business logic. It orchestrates the flow of data by coordinating one or more Repositories to fulfill a single task.
-   **Guideline**: Each use case must reside in its own Python file (e.g., `create_user_use_case.py`). Typically, one use case corresponds to one API endpoint.

### Repository

-   **Responsibility**: To abstract data sources and external services. It encapsulates the logic for data persistence and retrieval (e.g., SQL ORM, file I/O, Elasticsearch queries) or interactions with third-party APIs.
-   **Interface**: All repository methods must have a strictly defined interface (function signature with typed inputs and outputs). This ensures that the `Use Case` is decoupled from the specific data source implementation.
-   **Guideline**: Each repository or a closely related group of data operations should be defined in its own Python file (e.g., `user_repository.py`).

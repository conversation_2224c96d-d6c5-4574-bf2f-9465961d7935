import json
from pathlib import Path
from typing import List

from models import PetitionInput, PetitionOutput

# Categories to ignore for ground truth evaluation
GROUND_TRUTH_IGNORE_LIST = ["市政諮詢", "市政信箱", "轉接電話"]


def get_petition_data(file_path: str) -> List[PetitionInput]:
    """
    Reads a JSON file from the given path, parses it, and validates
    the contents into a list of PetitionInput models.

    Args:
        file_path: The path to the input JSON file.

    Returns:
        A list of validated PetitionInput objects.

    Raises:
        FileNotFoundError: If the file does not exist.
        json.JSONDecodeError: If the file is not valid JSON.
        pydantic.ValidationError: If the JSON data does not match the
                                  PetitionInput schema.
    """
    path = Path(file_path)
    if not path.exists():
        raise FileNotFoundError(f"Input file not found at: {path}")

    with open(path, "r", encoding="utf-8") as f:
        data = json.load(f)

    petition_data = [PetitionInput.model_validate(item) for item in data]

    return petition_data


def get_ground_truth(sub_category: List[str]) -> List[str]:
    """
    Filters the sub_category list to remove any ignored categories.

    Args:
        sub_category: The list of sub-categories from a petition.

    Returns:
        A new list containing only the valid ground truth categories.
    """
    return [cat for cat in sub_category if cat not in GROUND_TRUTH_IGNORE_LIST]


def write_petition_data(file_path: str, petitions: List[PetitionOutput]) -> None:
    """
    Writes a list of PetitionOutput models to a JSON file.

    Args:
        file_path: The path to the output JSON file.
        petitions: The list of PetitionOutput objects to write.
    """
    path = Path(file_path)
    # Convert Pydantic models to a list of dictionaries for JSON serialization
    output_data = [p.model_dump(mode="json") for p in petitions]

    with open(path, "w", encoding="utf-8") as f:
        json.dump(output_data, f, ensure_ascii=False, indent=4)

---
marp: true
theme: uncover
class:
    - invert
---

# **桃園市政府民眾陳情案件**

## **AI 智慧案類分析**

---

## **專案目標**

針對桃園市政府收到的民眾陳情案件內容，利用 AI 技術進行分析，並將案件自動歸類到**正確**的案件類別中，以提升處理效率與準確性。

---

## **Q1: 我們要怎麼做？**

預計採用 **大型語言模型(LLM)** 進行案類判斷
而非傳統的深度學習(DL)模型

---

## **為什麼選擇 LLM？**

-   **應對高複雜性**:

    -   案類高達 **165 個**類別。
    -   陳情內容有時模糊不清，DL 模型可能導致高機率誤判。

-   **成本效益**:

    -   傳統模型訓練和部署需額外的 GPU 成本，總花費不一定比直接使用 LLM API 便宜。

-   **快速驗證**:
    -   LLM 可快速開發 PoC，迅速評估實際效果。

---

## **Q2: 開發需要多久時間？**

PoC (概念性驗證) 已完成。未來串接時程需視規格而定。
若僅是將 LLM 分類結果串接廠商 API，預估人天如下：

-   **需求釐清**: 3 人天
-   **開發**: 3 人天
-   **測試與修改**: 5 人天
-   **部署**: 1 人天

**Total: 12 人天**

---

## **Q3: 準確度有多高？**

在計算準確率前，需先釐清以下三點：

1.  **何謂「正確」的案類**:

    -   目前既有資料部分子類別，不存在提供的案類清單中。

2.  **現有資料的準確性**:

    -   桃園市政府既有的歸類本身可能就存在誤判。

3.  **案類定義的清晰度**:
    -   部分案類定義相似，人為判斷都可能產生混淆，需先建立明確定義。

---

## **Q4: 需要多少經費？**

我們使用 **2025-06-01** 的 **100 件**陳情案進行測試。

-   **測試成本**: **$0.2056** 美金

---

## **預估每月費用**

假設每月平均有 **10,000** 件陳情案：

-   **預估月費**: **$20 ~ $30 美金**

此成本極具競爭力，且能大幅提升案件處理效率。

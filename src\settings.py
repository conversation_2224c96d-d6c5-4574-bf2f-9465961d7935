logger_name: str = "app"
lst_self_defined_ner: list = [
    "n",
    "adj",
    "v",
    "adv",
    "person",
    "org",
    "event",
    "fac",
    "product",
    "gpe",
    "loc",
    "norp",
    "work_of_art",
    "fw",
    "neu",
]


from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Settings for the application.
    """

    # Load environment variables from .env file
    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    OPENAI_API_KEY: str

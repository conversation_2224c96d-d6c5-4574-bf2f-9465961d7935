import datetime

# import importlib
import hashlib
import json
import logging
import os
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Tuple, Union

import openai
from elasticsearch import helpers
from elasticsearch.helpers import BulkIndexError
from opencc import OpenCC
from pydantic import BaseModel

import settings
from repository.connection_handler import ElasticsearchConnector

REPLACEMENTS = {"她": "他", "社羣": "社群"}


@dataclass
class TokenInfo:
    """
    Store the number of tokens used in the prompt and completion
    """

    completion_tokens: int = 0
    prompt_tokens: int = 0
    total_tokens: int = 0
    raw_data_characters: int = 0


class PetitionBatchSummary(BaseModel):
    petition: str


class PetitionFinalSummary(BaseModel):
    petition: List[str]


class GPTReportWriter:
    DEFAULT_ERR_MSG = "暫時無法提供精確的GPT分析結果。"

    def __init__(
        self,
        temperature=0,
        role="user",
    ):
        # self.api_type = os.environ.get("AZURE_OPENAI_API_TYPE", None)
        # self.api_version = os.environ.get("AZURE_OPENAI_API_VERSION", None)
        # self.api_base = os.environ.get("AZURE_OPENAI_API_BASE", None)

        self.azure_api_key = os.environ.get("AZURE_OPENAI_API_KEY", None)
        self.openai_api_key = os.environ.get("OPENAI_API_KEY", None)

        self.azure_gpt_client = openai.AzureOpenAI(
            api_key=self.azure_api_key,
            api_version="2024-08-01-preview",
            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
        )
        self.gpt_client = openai.OpenAI(
            api_key=self.openai_api_key,
        )
        self.temperature = temperature
        self.role = role
        # self.translator = ApiKeyTranslate(api_key=os.environ.get("GOOGLE_TRANSLATE_API_KEY", None))

        self.curr_token_recoder: TokenInfo = TokenInfo()
        self.dct_token_recorder: Dict[str, Dict[str, TokenInfo]] = dict()
        self.lst_error_summary: List[str] = list()
        logger_name = settings.logger_name
        self.logger = logging.getLogger(logger_name)

        self.open_cc = OpenCC("s2twp")  # 創建 OpenCC 物件，指定簡體到臺灣繁體的轉換

    def __s2twp_converter(self, str_simplified_text: str) -> str:
        """
        Convert a simplified Chinese text to traditional Chinese text.

        Args:
            str_simplified_text (str): The simplified Chinese text to be converted.

        Returns:
            str: The converted traditional Chinese text.
        """
        str_traditional_text = self.open_cc.convert(str_simplified_text)

        return str_traditional_text

    def _call_openai(
        self,
        system_prompt: str,
        user_prompt: str,
        content: str,
        output_response_model,
        temperature: int = 0,
        max_tokens: int = 1024,
        target_tokens: int = 4096,
        request_timeout: int = 60,
        is_convert_to_zh_tw: bool = True,
    ):
        """
        max_tokens vs. target_tokens: The former is supported by the API call. It's
        too strict though; the call fails immediately if prompt token + max_token
        > the limit supported by the model. This means that we have to be very
        careful when setting this parameter. The latter is simply a value provided
        by the caller specifying the target number of tokens to generate. We can
        then decide whether the completion goes haywire by checking afterwards.
        """
        logger = self.logger

        api_order = ["azure", "openai"]
        n_retry_count = 0
        response = None
        str_gpt_response_content = ""
        is_finished = False
        is_exception = False
        api_index = 0  # 0: azure, 1: openai
        lst_messages = [
            {"role": "system", "content": system_prompt},
            {"role": self.role, "content": user_prompt.format(content=content)},
        ]

        while not is_finished and n_retry_count < 3:
            n_retry_count += 1
            # logger.info(content)

            try:
                if api_order[api_index] == "azure":
                    logger.info("-----Azure OpenAI Service Start-----")
                    # response = self.azure_gpt_client.chat.completions.create(
                    #     model="gpt-4o-mini",
                    #     messages=lst_messages,
                    #     temperature=self.temperature,
                    #     max_tokens=max_tokens,
                    # )
                    response = self.azure_gpt_client.beta.chat.completions.parse(
                        model="gpt-4o-mini",
                        messages=lst_messages,
                        temperature=self.temperature,
                        response_format=output_response_model,
                    )

                    logger.info("-----Azure OpenAI Service END-----")

                elif api_order[api_index] == "openai":
                    logger.info("-----OpenAI API Start-----")
                    # response = self.gpt_client.chat.completions.create(
                    #     model="gpt-4o-mini",
                    #     messages=lst_messages,
                    #     temperature=self.temperature,
                    # )
                    response = self.gpt_client.beta.chat.completions.parse(
                        model="gpt-4o-mini",
                        messages=lst_messages,
                        temperature=self.temperature,
                        response_format=output_response_model,
                    )

                    logger.info("-----OpenAI API END-----")

                message = response.choices[0].message
                if message.parsed:
                    # logger.info(f"response: {message.parsed.petition}")
                    str_gpt_response_content = message.parsed.model_dump_json()
                else:
                    logger.error(message.refusal)
                    str_gpt_response_content = None

                # str_gpt_response_content = response.choices[0].message.content
                str_gpt_response_content = (
                    "" if str_gpt_response_content is None else str_gpt_response_content
                )

                logger.info(
                    f"completion_tokens: {response.usage.completion_tokens}, prompt_tokens: {response.usage.prompt_tokens}, total_tokens: {response.usage.total_tokens}"
                )

                self.curr_token_recoder.completion_tokens += (
                    response.usage.completion_tokens
                )
                self.curr_token_recoder.prompt_tokens += response.usage.prompt_tokens
                self.curr_token_recoder.total_tokens += response.usage.total_tokens

                if response.choices[0].finish_reason.lower() == "length":
                    logger.error("Completion hit max token!")

                    lst_messages = [
                        {"role": "system", "content": system_prompt},
                        {
                            "role": self.role,
                            "content": user_prompt.format(
                                content=str_gpt_response_content
                            ),
                        },
                    ]
                    logger.error(f"Try again... ({n_retry_count})")
                    continue

                if self.check_if_english(str_gpt_response_content):
                    logger.error(
                        f"English is more than Chinese in response. Try again... ({n_retry_count})"
                    )
                    lst_messages.append(
                        {
                            "role": "user",
                            "content": 'Use "Traditional Chinese (繁體中文)" to treat the above petition case as a whole and summarize it into a key summary of about 90 words.',
                        }
                    )
                    continue

                if not str_gpt_response_content and n_retry_count == 3:
                    n_retry_count = 0
                    if api_index == len(api_order) - 1:
                        break
                    api_index += 1

                is_exception = False

            except Exception as e:
                # logger.exception(f"{e}")
                logger.error(f"Open AI response error: {repr(e)}")
                # logger.error(f"user_prompt:{user_prompt}")
                # logger.error(f"content:{content}")

                is_exception = True

                if api_index == len(api_order) - 1:
                    break
                api_index += 1
                n_retry_count = 0
                continue

            is_finished = True

        if not is_exception:
            if is_convert_to_zh_tw:
                str_gpt_response_content = self.__s2twp_converter(
                    str_gpt_response_content
                )

            # Replace specific text from REPLACEMENTS
            for old, new in REPLACEMENTS.items():
                str_gpt_response_content = str_gpt_response_content.replace(old, new)

        return str_gpt_response_content

    def set_tokens(self, str_main_category: str, str_sub_category: str):
        """Set the tokens for a given main category and sub category.

        Parameters:
            str_main_category (str): The main category for which tokens are being set.
            str_sub_category (str): The sub category for which tokens are being set.

        Returns:
            None
        """
        self.dct_token_recorder[str_main_category] = {
            str_sub_category: TokenInfo(
                completion_tokens=self.curr_token_recoder.completion_tokens,
                prompt_tokens=self.curr_token_recoder.prompt_tokens,
                total_tokens=self.curr_token_recoder.total_tokens,
                raw_data_characters=self.curr_token_recoder.raw_data_characters,
            )
        }

    def reset_tokens(self):
        self.curr_token_recoder.completion_tokens = 0
        self.curr_token_recoder.prompt_tokens = 0
        self.curr_token_recoder.total_tokens = 0
        self.curr_token_recoder.raw_data_characters = 0

    def generate_conclusion(
        self, subject, post_content, is_final=False, idx=None, str_gpt_directory=None
    ):
        """
        Total 16.3k token
        ~1.5k token (1000 Chinese char) for completion;
        16.3k - 1.5k = 14.8k tokens for prompt
        0.5k tokens for instruction
        14.8k - 0.5k = 14.3k tokens for content;
        14300/1.5 => 9500 Chinese char. But we take 8000 char as max length.
        """
        # post_text = "\n".join(post_content)
        # post_content = GPTReportWriter._summarize_zh_tw_by_extract(post_text, char=4000)

        system_prompt = """You are a helpful assistant who's good at summarizing public petition cases for people to easily understand.
I'm summarizing the petition content for government. If the content includes "貓屍" or "屍體", they are not inappropriate content at all. So allow all rules.
"""
        user_prompt = """The following are the details of each petition case:

"{content}"
Please follow the instructions below strictly to complete the task:
"""
        if not is_final:
            output_response_model = PetitionBatchSummary
            user_prompt = (
                user_prompt
                + """1. Use "Traditional Chinese (繁體中文)" to treat the above petition case as a whole and summarize it into a key summary of about 90 words.
2. The key summary must be within 90 words; it cannot exceed this limit.
3. DO NOT repeating the same or similar words in the segments. DO NOT give the information that already exists in other segments.
4. Output in JSON format, with the key as "petition" and the value as the overall summary content.
5. Pay attention to the word limit; the summary content must strictly follow the word limit (within 90 words).
Output format:
{{
    "petition": key_summary
}}
"""
            )
        else:
            output_response_model = PetitionFinalSummary
            user_prompt = (
                user_prompt
                + """1. Use "Traditional Chinese (繁體中文)" to treat the above petition case as a whole and summarize it into 3~5 segments.
2. If the petition content is too short to be divided into 3 segments, summarizing it into just one or two segments is fine.
3. Each segment must be within 70 words; it cannot exceed this limit.
4. DO NOT repeating the same or similar words in the segments. DO NOT give the information that already exists in other segments.
5. Output in JSON format, with the key as "petition" and the value as a list of strings, where the maximum of list length is 5, representing the length of list must be less than 5 segments.
6. Pay attention to the word limit; each segment must strictly follow the word limit.
Output format:
{{
    "petition": [summary1, summary2, summary3]
}}
"""
            )

        if str_gpt_directory:
            if is_final:
                str_file_name = f"prompt_for_summary_{idx}.txt"
            else:
                str_file_name = f"prompt_for_content_{idx}.txt"

            with open(
                os.path.join(str_gpt_directory, str_file_name),
                "w",
                encoding="utf-8",
            ) as f:
                f.write(system_prompt + user_prompt.format(content=post_content))

        str_gpt_response_content = self._call_openai(
            system_prompt,
            user_prompt,
            content=post_content,
            max_tokens=1000,
            output_response_model=output_response_model,
            temperature=0,
        )

        str_gpt_response_content = str_gpt_response_content.replace("\\", "/")

        # if not str_gpt_response_content:
        #     return GPTReportWriter.DEFAULT_ERR_MSG
        return str_gpt_response_content

        # Remove exact duplicates
        # TODO: Remove duplicates by checking edit distances
        # bullets = re.split(r"(^|\s|。)\d+\.", str_gpt_response_content)
        # bullets = [l.strip() for l in bullets if l.strip() and l != "。"]
        # bullets = list(dict.fromkeys(bullets))

        # return bullets

    def summarize_documents(
        self,
        lst_documents: List[dict],
        str_subject: str,
        n_max_characters_per_batch: int = 3000,
        str_gpt_directory: str = None,
    ):
        lst_batches = []
        lst_current_batch = []
        n_current_length = 0
        # lst_case_id = []
        for idx, doc in enumerate(lst_documents, 1):
            content = doc["_source"]["petition_content"]
            content = content.strip()
            # content = re.sub(r"(\r\n.?)+", r"\r\n", content)
            content = content.replace("\r\n", "")
            content = content.replace("\n", "")
            content = content.replace("\r", "")

            content = content[:n_max_characters_per_batch]

            delimiter = "陳情內容:\n"
            if idx == 1:
                content = delimiter + content
            n_content_length = len(content)
            if n_current_length + n_content_length > n_max_characters_per_batch:
                # self.logger.info(f"case_id: {lst_case_id}")
                lst_batches.append("\n".join(lst_current_batch))
                # lst_batches.append(";".join(lst_current_batch))
                content = delimiter + content
                lst_current_batch = [content]
                n_content_length = len(content)
                # lst_case_id = [doc["_source"]["case_id"]]
                n_current_length = n_content_length
            else:
                lst_current_batch.append(content)
                # lst_case_id.append(doc["_source"]["case_id"])
                n_current_length += n_content_length

        if lst_current_batch:
            lst_batches.append("\n".join(lst_current_batch))
            # lst_batches.append(";".join(lst_current_batch))

        lst_batch_summaries = []
        for idx, batch in enumerate(lst_batches, 1):
            content = batch
            n_chars_count_in_content = len(content)
            self.logger.info(f"Summarizing batch {idx} of {len(lst_batches)}...")
            # self.logger.info(f"{n_chars_count_in_content} characters")
            self.curr_token_recoder.raw_data_characters += n_chars_count_in_content

            str_summary = self.generate_conclusion(
                str_subject, content, idx=idx, str_gpt_directory=str_gpt_directory
            )
            # if idx == 1:
            #     self.logger.info(f"str_summary: {str_summary}")
            #     self.logger.info(f"type(str_summary): {type(str_summary)}")
            #     self.logger.info(
            #         f"type(json.loads(str_summary)): {type(json.loads(str_summary))}"
            #     )
            try:
                dct_summary = json.loads(r"{}".format(str_summary))
                str_summary = dct_summary["petition"]
            except json.decoder.JSONDecodeError:
                self.logger.warning(f"JSONDecodeError str_summary: {str_summary}")

            except KeyError:
                self.logger.warning(f"JSON KeyError str_summary: {str_summary}")

            lst_batch_summaries.append(str_summary)

            if str_gpt_directory:
                with open(
                    os.path.join(str_gpt_directory, f"raw_data_{idx}.txt"),
                    "w",
                    encoding="utf-8",
                ) as f:
                    f.write(content)

                with open(
                    os.path.join(str_gpt_directory, f"gpt_summary_{idx}.txt"),
                    "w",
                    encoding="utf-8",
                ) as f:
                    f.write(str_summary)

        # Recursive function to summarize batch summaries
        def recursive_summarize(summaries, max_length, is_summarized, total_idx):
            if is_summarized and len(summaries) == 1:
                return summaries[0]

            lst_combined_summaries = []
            lst_summary_current_batch = []
            n_current_length = 0

            for idx, summary in enumerate(summaries, 1):
                if isinstance(summary, list):
                    summary = "\n".join(summary)

                if idx == 1:
                    delimiter = "陳情內容:\n"
                    summary = delimiter + summary

                n_summary_length = len(summary)
                if n_current_length + n_summary_length > max_length:
                    lst_combined_summaries.append("\n".join(lst_summary_current_batch))
                    # lst_combined_summaries.append(";".join(lst_summary_current_batch))
                    lst_summary_current_batch = [summary]
                    n_current_length = n_summary_length
                else:
                    lst_summary_current_batch.append(summary)
                    n_current_length += n_summary_length

            if lst_summary_current_batch:
                lst_combined_summaries.append("\n".join(lst_summary_current_batch))
                # lst_combined_summaries.append(";".join(lst_summary_current_batch))

            n_combined_summaries = len(lst_combined_summaries)
            lst_new_summaries = []
            for idx, str_combined_summary in enumerate(lst_combined_summaries, 1):
                str_summary_track_id = (
                    "final" if n_combined_summaries == 1 else f"{total_idx}_{idx}"
                )
                self.logger.info(
                    f"Further summarizing combined batch {total_idx}_{idx} of {n_combined_summaries}..."
                )
                str_summary = self.generate_conclusion(
                    str_subject,
                    str_combined_summary,
                    idx=str_summary_track_id,
                    str_gpt_directory=str_gpt_directory,
                    is_final=True,
                )

                if n_combined_summaries > 1:
                    try:
                        dct_summary = json.loads(r"{}".format(str_summary))
                        str_summary = dct_summary["petition"]
                    except json.decoder.JSONDecodeError:
                        self.logger.warning(
                            f"JSONDecodeError str_summary: {str_summary}"
                        )
                    except KeyError:
                        self.logger.warning(f"JSON KeyError str_summary: {str_summary}")

                lst_new_summaries.append(str_summary)

            return recursive_summarize(
                lst_new_summaries, max_length, True, total_idx + 1
            )

        # Combine batch summaries recursively
        str_final_summary = '{"petition":["本周無陳情案件"]}'
        if len(lst_batch_summaries) > 0:
            str_final_summary = recursive_summarize(
                lst_batch_summaries, n_max_characters_per_batch, False, 1
            )

        if str_gpt_directory:
            with open(
                os.path.join(str_gpt_directory, "gpt_summary_final.txt"),
                "w",
                encoding="utf-8",
            ) as f:
                f.write(str_final_summary)

        # self.logger.info(str_final_summary)
        lst_final_summary = None
        try:
            dct_final_summary = json.loads(r"{}".format(str_final_summary))
            lst_final_summary = dct_final_summary["petition"]
        except json.decoder.JSONDecodeError:
            self.logger.warning(
                f"JSONDecodeError str_final_summary: {str_final_summary}"
            )
            self.lst_error_summary.append(str_subject)
            str_summary = str_summary.replace(" ", "")
            str_summary = str_summary.replace("\r\n", "")
            str_summary = str_summary.replace("\n", "")
            str_summary = str_summary.replace("\r", "")
            if str_summary.startswith('{"petition":['):
                str_summary = str_summary[
                    len('{"petition":[') :
                ]  # Remove the initial structure
        except KeyError:
            self.logger.warning(f"JSON KeyError str_final_summary: {str_final_summary}")
            str_summary = str_summary.replace(" ", "")
            str_summary = str_summary.replace("\r\n", "")
            str_summary = str_summary.replace("\n", "")
            str_summary = str_summary.replace("\r", "")
            if str_summary.startswith('{"petition":['):
                str_summary = str_summary[
                    len('{"petition":[') :
                ]  # Remove the initial structure

        return lst_final_summary if lst_final_summary else [str_final_summary]

    def check_if_english(self, text: str):
        """Compares count of English and Chinese characters in text.

        Args:
            text (str): Text to analyze.

        Returns:
            bool: True if more English letters, else False.
        """
        count_english = len(re.findall(r"[a-zA-Z]", text))
        count_chinese = len(re.findall(r"[\u4e00-\u9fff]", text))
        # self.logger.info(
        #     f"Character count: [EN]: {count_english}, [CN]: {count_chinese}"
        # )

        return count_english > count_chinese


class ElasticsearchSummarizer:
    def __init__(self):
        logger_name = settings.logger_name
        self.logger = logging.getLogger(logger_name)
        self.es_connector = ElasticsearchConnector()
        self.es = self.es_connector.get_es()

        # Get the current date and year
        tz = datetime.timezone(datetime.timedelta(hours=+8))
        t_current_date = datetime.datetime.now(tz)
        current_year = t_current_date.year

        self.petition_case_index_base_name = "petition-case"
        self.petition_case_index_name = f"{self.petition_case_index_base_name}-{current_year}"  # 使用當前年分作為petition_case_index_name
        self.petition_case_alias_name = f"{self.petition_case_index_name}-alias"
        self.petition_case_latest_alias_name = (
            f"{self.petition_case_index_base_name}-latest-alias"
        )

        self.summary_index_base_name = "petition-summary"
        self.summary_index_name = f"{self.summary_index_base_name}-{current_year}"  # 使用當前年分作為summary_index_name
        self.summary_alias_name = f"{self.summary_index_name}-alias"
        self.summary_latest_alias_name = f"{self.summary_index_base_name}-latest-alias"

        self.set_error_date = set()
        self.set_index_check_cache = set()

    @staticmethod
    def __process_time(
        start_time: Union[str, datetime.date],
        end_time: Union[str, datetime.date],
    ) -> Tuple[str, str]:
        # If start_time is a datetime object, convert it to a string
        if isinstance(start_time, datetime.date):
            start_time = start_time.strftime("%Y/%m/%d")

        if isinstance(end_time, datetime.date):
            end_time = end_time.strftime("%Y/%m/%d")

        return start_time, end_time

    def _build_query(
        self,
        str_start_time: str,
        str_end_time: str,
        dct_terms_fields: dict = None,
        dct_category: dict = None,
        lst_search_condition: list = None,
        is_sort_by_time: bool = True,
        n_size: int = -1,
        source_fields: list = None,
        is_track_total_hits: bool = False,
    ):
        str_start_time, str_end_time = self.__process_time(str_start_time, str_end_time)
        dct_query = {
            "query": {
                "bool": {
                    "filter": [
                        {
                            "range": {
                                "record_time": {
                                    "gte": str_start_time,
                                    "lte": str_end_time,
                                    "format": "yyyy/MM/dd",
                                }
                            }
                        },
                        # {"term": {"record_channel": "市政信箱"}},
                    ]
                }
            },
        }

        if n_size >= 0:
            dct_query["size"] = n_size

        if is_track_total_hits:
            dct_query["track_total_hits"] = True

        if is_sort_by_time:
            dct_query["sort"] = [{"record_time": {"order": "desc"}}]

        if source_fields:
            dct_query["_source"] = source_fields

        if dct_terms_fields:
            for str_field, v in dct_terms_fields.items():
                if v:
                    if isinstance(v, list):
                        q = {"terms": {str_field: v}}
                    else:
                        q = {"term": {str_field: v}}
                    dct_query["query"]["bool"]["filter"].append(q)

        if dct_category:
            dct_category_query = {"bool": {"should": []}}

            for str_main_category, lst_sub_category in dct_category.items():
                dct_each_category_query = {
                    "bool": {
                        "filter": [{"terms": {"main_category": [str_main_category]}}]
                    }
                }
                if lst_sub_category:
                    dct_each_category_query["bool"]["filter"].append(
                        {"terms": {"sub_category": lst_sub_category}}
                    )

                dct_category_query["bool"]["should"].append(dct_each_category_query)

            dct_query["query"]["bool"]["filter"].append(dct_category_query)

        if lst_search_condition:
            lst_search_fields = ["petition_subject", "petition_content"]

            # dct_filter_query = {"bool": {"filter": []}}
            dct_should_query = {"bool": {"should": []}}
            dct_must_not_query = {"must_not": []}
            for dct_search_condition in lst_search_condition:
                # 如果是include(包含)就用OR邏輯
                if dct_search_condition["logic"] == "include":
                    # dct_search_query = {"bool": {"filter": []}}
                    dct_should_query["bool"]["should"].append(
                        {
                            "multi_match": {
                                "query": dct_search_condition["keyword"],
                                "fields": lst_search_fields,
                            }
                        }
                    )
                else:
                    # dct_search_query = {"bool": {"must_not": []}}
                    dct_must_not_query["must_not"].append(
                        {
                            "multi_match": {
                                "query": dct_search_condition["keyword"],
                                "fields": lst_search_fields,
                            }
                        }
                    )
                # dct_query["query"]["bool"]["filter"].append(dct_search_query)

            # if dct_filter_query["bool"]["filter"]:
            #     dct_query["query"]["bool"]["filter"].append(dct_filter_query)
            if dct_should_query["bool"]["should"]:
                dct_query["query"]["bool"]["filter"].append(dct_should_query)
            if dct_must_not_query["must_not"]:
                dct_query["query"]["bool"]["must_not"] = dct_must_not_query["must_not"]

        return dct_query

    def get_total_count_by_index(
        self,
        str_start_time: str = None,
        str_end_time: str = None,
        dct_terms_fields: dict = None,
    ):
        lst_alias_name = self.calc_alias_name(str_start_time, str_end_time)
        # dct_params = {"index": lst_alias_name, "params": {"format": "json"}}
        dct_params = {"index": lst_alias_name}

        if str_start_time and str_end_time:
            dct_query = self._build_query(
                str_start_time,
                str_end_time,
                dct_terms_fields=dct_terms_fields,
                is_sort_by_time=False,
            )
            dct_params["body"] = dct_query

        # self.es.indices.refresh(lst_alias_name)
        # dct_search_result = self.es.cat.count(**dct_params)
        dct_search_result = self.es.count(**dct_params)

        # dct_search_result = {'count': 1269, '_shards': {'total': 1, 'successful': 1, 'skipped': 0, 'failed': 0}}
        # self.logger.info(f"dct_search_result: {dct_search_result}")
        n_total_doc_count = dct_search_result["count"]
        # self.logger.info(f"n_total_doc_count: {n_total_doc_count}")
        return n_total_doc_count

    def get_documents_by_category(
        self, str_start_time, str_end_time, main_category, sub_category, n_size=10000
    ):
        str_start_time, str_end_time = self.__process_time(str_start_time, str_end_time)
        dct_query = self._build_query(
            str_start_time,
            str_end_time,
            dct_terms_fields={
                "main_category": main_category,
                "sub_category": sub_category,
            },
            n_size=n_size,
            is_sort_by_time=False,
        )
        # dct_query = {
        #     "size": n_size,
        #     "query": {
        #         "bool": {
        #             "filter": [
        #                 {
        #                     "range": {
        #                         "record_time": {
        #                             "gte": str_start_time,
        #                             "lte": str_end_time,
        #                             "format": "yyyy/MM/dd",
        #                         }
        #                     }
        #                 },
        #                 {"term": {"main_category": main_category}},
        #                 {"term": {"sub_category": sub_category}},
        #             ]
        #         }
        #     },
        # }

        n_year = str_start_time.split("/")[0]
        response = self.search(
            alias_name=self.get_petition_case_alias(n_year),
            dct_query=dct_query,
            scroll="2m",
        )
        return response

    def scroll_documents(self, scroll_id):
        response = self.es.scroll(scroll_id=scroll_id, scroll="2m")
        return response

    def retrieve_all_documents_by_category(
        self, start_date, end_date, main_category, sub_category
    ):
        response = self.get_documents_by_category(
            start_date, end_date, main_category, sub_category
        )
        lst_documents = response["hits"]["hits"]
        scroll_id = response["_scroll_id"]
        while len(response["hits"]["hits"]) > 0:
            response = self.scroll_documents(scroll_id)
            lst_documents.extend(response["hits"]["hits"])
        return lst_documents

    def get_case_count_by_category_group(
        self,
        str_start_time: str = None,
        str_end_time: str = None,
        dct_category: Dict[str, List[str]] = None,
        dct_terms_fields: Dict[str, Union[str, List[str]]] = None,
        is_return_query_only: bool = False,
        dct_search_result: dict = None,
    ):
        if not dct_search_result:
            dct_query = self._build_query(
                str_start_time,
                str_end_time,
                dct_terms_fields=dct_terms_fields,
                dct_category=dct_category,
                n_size=0,
                is_sort_by_time=False,
                is_track_total_hits=True,
            )

            if is_return_query_only:
                # self.logger.debug(f"query: {dct_query}")
                return dct_query

            lst_alias_name = self.calc_alias_name(str_start_time, str_end_time)
            dct_search_result = self.search(
                alias_name=lst_alias_name, dct_query=dct_query
            )
        n_total_doc_count = dct_search_result["hits"]["total"]["value"]
        return n_total_doc_count

    def get_category_group(
        self,
        str_start_time: str = "",
        str_end_time: str = "",
        lst_owner_unit: list = None,
    ):
        dct_terms_fields = {"recent_process.owner_unit": lst_owner_unit}
        dct_query = self._build_query(
            str_start_time,
            str_end_time,
            dct_terms_fields=dct_terms_fields,
            n_size=0,
        )
        dct_query["aggs"] = {
            "main_categories": {
                "terms": {"field": "main_category", "size": 10000},
                "aggs": {
                    "sub_categories": {
                        "terms": {"field": "sub_category", "size": 10000}
                    }
                },
            }
        }

        """
        dct_search_result = {
            "aggregations": {
                "main_categories": {
                    "doc_count_error_upper_bound": 0,
                    "sum_other_doc_count": 0,
                    "buckets": [
                        {
                            "key": "交通號誌、標誌、標線及大眾運輸",
                            "doc_count": 427,
                            "sub_categories": {
                                "doc_count_error_upper_bound": 0,
                                "sum_other_doc_count": 0,
                                "buckets": [
                                    {
                                        "key": "公車問題及站牌、候車亭設施管理",
                                        "doc_count": 172,
                                    },
                                ],
                            },
                        }
                    ],
                }
            }
        }
        """

        if str_start_time and str_end_time:
            lst_alias_name = self.calc_alias_name(str_start_time, str_end_time)
        else:  # 如果沒有給時間參數，就只搜尋最近一年的資料
            lst_alias_name = []
            lst_alias_name.append(self.petition_case_latest_alias_name)

        dct_search_result = self.search(alias_name=lst_alias_name, dct_query=dct_query)

        dct_category = {}
        for dct_result_main_category in dct_search_result["aggregations"][
            "main_categories"
        ]["buckets"]:
            str_main_category = dct_result_main_category["key"]
            dct_category[str_main_category] = set()
            for dct_sub_category in dct_result_main_category["sub_categories"][
                "buckets"
            ]:
                dct_category[str_main_category].add(dct_sub_category["key"])

        return dct_category

    def check_if_summary_exists(
        self, str_main_category: str, str_sub_category: str, month, year, week_in_year
    ):
        str_es_doc_id = self.generate_id(
            **{
                "str_main_category": str_main_category,
                "str_sub_category": str_sub_category,
                "year": year,
                "month": month,
                "week_in_year": week_in_year,
            }
        )
        dct_query = {
            "size": 0,
            "query": {
                "bool": {
                    "filter": [
                        {"term": {"_id": str_es_doc_id}},
                        {"exists": {"field": "summary"}},
                    ]
                }
            },
        }

        alias_name = self.get_summary_alias(year)
        response = self.search(alias_name=alias_name, dct_query=dct_query)
        # self.logger.debug(f"check_if_summary_exists: {response}")
        return response["hits"]["total"]["value"] > 0

    @staticmethod
    def generate_id(
        str_main_category: str,
        str_sub_category: str,
        year: int,
        month: int,
        week_in_year: int,
    ):
        """
        Generate a unique ID based on the provided main category, sub category, year, month, and year week.

        Args:
            str_main_category (str): The main category.
            str_sub_category (str): The sub category.
            year (int): The year.
            month (int): The month.
            year_week (int): The year week.

        Returns:
            str: The generated unique ID.
        """
        unique_string = (
            f"{str_main_category}-{str_sub_category}-{year}-{month}-{week_in_year}"
        )
        return hashlib.sha256(unique_string.encode()).hexdigest()

    def bulk_operation(
        self,
        lst_docs: List[dict],
        id_field: str = None,
        op_type: str = "index",
    ):
        lst_error_doc_id = []
        n_docs_success = 0
        n_docs_total = len(lst_docs)
        try:
            lst_actions = []
            set_bulk_date_recorder = set()
            n_len_actions = 0
            for doc in lst_docs:
                # self.logger.debug(f"{doc}")
                alias_name = doc.pop("alias_name")
                index_name = doc.pop("index_name")
                # if index_name not in self.set_index_check_cache:
                #     self.es_create_index(index_name, alias_name)

                dct_item = {"_op_type": op_type, "_index": alias_name}
                if op_type == "index":
                    dct_item["_source"] = doc
                elif op_type == "update":
                    dct_item["doc"] = doc
                    dct_item["doc_as_upsert"] = True
                if id_field:
                    dct_item["_id"] = doc.pop(id_field)

                lst_actions.append(dct_item)
                set_bulk_date_recorder.add(
                    f"{doc['year']}/{doc['month']}/W{doc['week_in_year']}|"
                )

                n_len_actions += 1
                if n_len_actions % 5000 == 0 and n_len_actions != 0:
                    n_docs_success += n_len_actions
                    helpers.bulk(self.es, lst_actions)
                    self.logger.info(
                        f"[{set_bulk_date_recorder}] Bulk [{op_type}] successfully. [{n_len_actions}] docs processed"
                    )
                    n_len_actions = 0
                    set_bulk_date_recorder = set()

            if n_len_actions != 0:
                n_docs_success += n_len_actions
                helpers.bulk(self.es, lst_actions)
                self.logger.info(
                    f"[{set_bulk_date_recorder}] Bulk [{op_type}] successfully. [{n_len_actions}] docs processed"
                )

            # helpers.bulk(self.es, lst_actions)

        except BulkIndexError as e:
            n_error_docs_count = len(e.errors)
            # e.errors contains the errors that occurred during the bulk operation
            for error in e.errors:
                # Each error is a tuple where the first element is the action that failed and the second element is the error information
                self.logger.error(f"Error indexing document: {error}\n")
                str_main_category = error[op_type]["main_category"]
                str_sub_category = error[op_type]["sub_category"]
                year = error[op_type]["year"]
                month = error[op_type]["month"]
                week_in_year = error[op_type]["weeb_in_year"]
                doc_id = f"{str_main_category}-{str_sub_category}-{year}-{month}-{week_in_year}"
                str_bulk_date_recorder = f"{year}/{month}/W{week_in_year}"
                lst_error_doc_id.append(doc_id)
                # self.logger.error(f"Error indexing document with ID {doc_id}")
                self.set_error_date.add(str_bulk_date_recorder)

            n_docs_success -= n_error_docs_count
            self.logger.warn(f"[{n_error_docs_count}] docs failed")
        except Exception as e:
            self.logger.error(f"Unexpected error: {repr(e)}")
            self.logger.warn(f"[{n_docs_total - n_docs_success}] docs failed")

        else:
            pass
        finally:
            n_docs_error = len(lst_error_doc_id)
            # n_docs_total2 = n_docs_success + n_docs_error

            f_success_rate = (
                (n_docs_success / n_docs_total) * 100 if n_docs_total != 0 else 0
            )
            f_error_rate = (
                (n_docs_error / n_docs_total) * 100 if n_docs_total != 0 else 0
            )

            # self.logger.debug(
            #     f"n_docs_total: {n_docs_total}, n_docs_total2: {n_docs_total2}"
            # )
            self.logger.info(
                f"Batch Bulk total:[{n_docs_total}] / success:[{n_docs_success}] / error:[{n_docs_error}] / success_rate:[{f_success_rate:.2f}%] / error_rate:[{f_error_rate:.2f}%]]"
            )
            self.logger.info(f"lst_error_doc_id: {lst_error_doc_id}")
            return {
                "n_docs_success": n_docs_success,
                "n_docs_error": n_docs_error,
                "n_docs_total": n_docs_total,
                "lst_error_doc_id": lst_error_doc_id,
            }

    def calc_alias_name(self, str_start_time: str, str_end_time: str):
        n_start_year = int(str_start_time.split("/")[0])
        n_end_year = int(str_end_time.split("/")[0])
        lst_alias_name = []
        for n_year in range(n_start_year, n_end_year + 1):
            alias_name = self.get_petition_case_alias(n_year)
            lst_alias_name.append(alias_name)

        return lst_alias_name

    def search(self, dct_query: dict, alias_name: Union[str, List[str]], scroll=None):
        return self.es.search(index=alias_name, body=dct_query, scroll=scroll)

    def delete_index(self, index_name: str):
        self.es.indices.delete(index=index_name)
        self.logger.info(f"Index '{index_name}' deleted successfully")

    def es_create_index(
        self,
        index_name: str,
        alias_name: str = None,
        is_delete_index: bool = False,
    ):
        """
        Creates an Elasticsearch index with the given index name.

        Args:
            index_name (str): The name of the index to be created.
            alias_name (str, optional): The alias to be associated with the index. Defaults to None.
            is_delete_index (bool, optional): Whether to delete the index if it already exists. Defaults to False.

        Returns:
            bool: True if the index is created successfully, False otherwise.

        Raises:
            None
        """
        is_index_exist = self.es.indices.exists(index=index_name)

        self.set_index_check_cache.add(index_name)

        # Delete index
        if is_index_exist and is_delete_index:
            self.es.indices.delete(index=index_name)
            self.logger.info(f"Index '{index_name}' deleted successfully")
            is_index_exist = False

        if not is_index_exist:
            es_setting_file_path = os.path.join("es", "summary_setting.json")
            with open(es_setting_file_path, mode="r", encoding="utf-8") as file:
                dct_settings = json.load(file)

            es_mapping_file_path = os.path.join("es", "summary_mapping.json")
            with open(es_mapping_file_path, mode="r", encoding="utf-8") as file:
                dct_mapping = json.load(file)

            # Define settings
            dct_index_settings = {
                "settings": dct_settings,
                "mappings": dct_mapping,
            }

            if alias_name:
                dct_index_settings.update({"aliases": {alias_name: {}}})
                self.logger.info(
                    f"alias '{alias_name}' appended to index '{index_name}'"
                )

            # self.logger.info(f"index setting: {dct_index_settings}")

            # Create index
            self.es.indices.create(index=index_name, body=dct_index_settings)
            self.logger.info(f"index '{index_name}' created successfully")
            return True
        else:
            self.logger.info(f"index '{index_name}' already exists")
            return False

    def reindex(self, str_source_index: str, str_dest_index: str):
        self.logger.info("reindexing...")
        self.es.reindex(
            **{
                "source": {
                    "index": str_source_index,
                    # "query": {
                    #     # your query body...
                    # }
                },
                "dest": {"index": str_dest_index},
                "request_timeout": 1200,
                "wait_for_completion": True,
            }
        )
        self.logger.info(
            f"reindex from '{str_source_index}' to '{str_dest_index}' successfully"
        )

    def put_alias(self, index_name: str, alias_name: str):
        self.es.indices.put_alias(index=index_name, name=alias_name)
        self.logger.info(f"alias '{alias_name}' set successfully")

    def update_alias(self, str_old_index: str, str_new_index: str, alias_name: str):
        # Update the alias to point to the new index
        self.es.indices.update_aliases(
            body={
                "actions": [
                    {"remove": {"index": str_old_index, "alias": alias_name}},
                    {"add": {"index": str_new_index, "alias": alias_name}},
                ]
            }
        )
        self.logger.info(
            f"alias '{alias_name}' updated from '{str_old_index}' to '{str_new_index}'"
        )

    def msearch(self, lst_query: List[Dict[str, Any]]):
        """
        lst_query = [
            {"index": "case-petition-2024", "query": {...}}
        ]
        """
        lst_search_query = []
        for dct_query in lst_query:
            index_name = dct_query["alias"]
            lst_search_query.append({"index": index_name})
            lst_search_query.append(dct_query["query"])

        # self.logger.info(f"lst_search_query: {lst_search_query}")
        return self.es.msearch(body=lst_search_query)

    def get_petition_case_index_name(self, year):
        return f"{self.petition_case_index_base_name}-{year}"

    def get_petition_case_alias(self, year):
        return f"{self.petition_case_index_base_name}-{year}-alias"

    def get_summary_index_name(self, year):
        return f"{self.summary_index_base_name}-{year}"

    def get_summary_alias(self, year):
        return f"{self.summary_index_base_name}-{year}-alias"

    def list_indices_and_aliases(self):
        indices = list(self.es.indices.get_alias(index="*").keys())
        indices = [i for i in indices if not i.startswith(".")]

        aliases = self.es.indices.get_alias(index=indices)

        return {
            "indices": indices,
            "aliases": aliases,
        }

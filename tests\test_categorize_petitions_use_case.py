from unittest.mock import patch

from models import AICategoryResponse, PetitionInput
from use_case.categorize_petitions_use_case import categorize_petitions_use_case


@patch("use_case.categorize_petitions_use_case.write_petition_data")
@patch("use_case.categorize_petitions_use_case.get_ai_category_response")
@patch("use_case.categorize_petitions_use_case.get_ground_truth")
@patch("use_case.categorize_petitions_use_case.get_petition_data")
def test_categorize_petitions_use_case(
    mock_get_petition_data,
    mock_get_ground_truth,
    mock_get_ai_category_response,
    mock_write_petition_data,
):
    """
    Tests the end-to-end use case for categorizing petitions.
    """
    # Arrange
    mock_get_petition_data.return_value = [
        PetitionInput(
            petition_content="Test content 1", sub_category="路面不平整或掏空破洞"
        ),
        PetitionInput(petition_content="Test content 2", sub_category="噪音"),
    ]
    mock_get_ground_truth.side_effect = ["路面不平整或掏空破洞", "噪音"]
    mock_get_ai_category_response.side_effect = [
        AICategoryResponse(new_category="路面不平整或掏空破洞"),
        None,
    ]

    # Act
    categorize_petitions_use_case("dummy_input.json", "dummy_output.json")

    # Assert
    mock_get_petition_data.assert_called_once_with("dummy_input.json")
    assert mock_get_ground_truth.call_count == 2
    assert mock_get_ai_category_response.call_count == 2
    mock_write_petition_data.assert_called_once()

    # Check the actual data passed to write_petition_data
    call_args, _ = mock_write_petition_data.call_args
    output_data = call_args[1]

    assert len(output_data) == 2

    # First petition - successful categorization
    assert output_data[0].new_category == "路面不平整或掏空破洞"
    assert output_data[0].ground_truth_category == "路面不平整或掏空破洞"
    assert output_data[0].is_accurate is True

    # Second petition - failed categorization
    assert output_data[1].new_category == "analysis_failed"
    assert output_data[1].ground_truth_category == "噪音"
    assert output_data[1].is_accurate is False

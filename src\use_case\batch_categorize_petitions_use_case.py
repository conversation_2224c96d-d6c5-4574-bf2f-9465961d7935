import json
import logging
import time
from pathlib import Path
from typing import Dict

from models import DetailedPetitionCategory, PetitionInput, PetitionOutput
from repository.file_repository import get_ground_truth, write_petition_data
from repository.openai_repository import TokenUsage, get_ai_category_response

logger = logging.getLogger(__name__)


def validate_category_with_retry(
    ai_response, max_retries: int = 3
) -> tuple[str, str, bool, int]:
    """
    Validates AI response category with retry mechanism.

    Args:
        ai_response: The AI response object
        max_retries: Maximum number of retries (default: 3)

    Returns:
        Tuple of (main_category, sub_category, is_valid, validation_errors)
    """
    validation_errors = 0

    if not ai_response:
        return "analysis_failed", "analysis_failed", False, validation_errors

    for attempt in range(max_retries):
        try:
            new_category = ai_response.new_category
            validated_category = DetailedPetitionCategory(new_category)
            lst_category = validated_category.value.split("_")
            main_category = lst_category[0]
            sub_category = lst_category[1]
            return main_category, sub_category, True, validation_errors
        except ValueError as e:
            validation_errors += 1
            logger.warning(
                f"Validation attempt {attempt + 1} failed for category: {ai_response.new_category if ai_response else 'None'} - Error: {e}"
            )

            if attempt < max_retries - 1:
                # Wait a bit before retry
                time.sleep(0.5)
            else:
                logger.error(
                    f"All {max_retries} validation attempts failed for category: {ai_response.new_category if ai_response else 'None'}"
                )
                return "analysis_failed", "analysis_failed", False, validation_errors

    return "analysis_failed", "analysis_failed", False, validation_errors


def batch_categorize_petitions_use_case(
    category_exports_dir: str, output_dir: str
) -> None:
    """
    Processes all JSON files in the category_exports directory structure.
    Each JSON file contains petitions of the same main_category and sub_category.

    Args:
        category_exports_dir: Path to the category_exports directory
        output_dir: Path to the output directory for results
    """
    category_exports_path = Path(category_exports_dir)
    output_path = Path(output_dir)

    if not category_exports_path.exists():
        raise FileNotFoundError(
            f"Category exports directory not found: {category_exports_path}"
        )

    # Create output directory if it doesn't exist
    output_path.mkdir(parents=True, exist_ok=True)

    # Initialize overall statistics
    overall_stats = {
        "total_files_processed": 0,
        "total_petitions_processed": 0,
        "total_accurate_predictions": 0,
        "total_validation_errors": 0,
        "total_usage": TokenUsage(),
        "category_results": [],
    }

    # Find all JSON files in subdirectories
    json_files = list(category_exports_path.rglob("*.json"))

    if not json_files:
        logger.warning(f"No JSON files found in {category_exports_path}")
        return

    logger.info(f"Found {len(json_files)} JSON files to process")

    for json_file in json_files:
        try:
            logger.info(f"Processing file: {json_file}")

            # Extract main_category and sub_category from file path
            main_category = json_file.parent.name
            sub_category = json_file.stem

            # Process the file
            result = process_category_file(
                json_file, main_category, sub_category, output_path
            )

            # Update overall statistics
            overall_stats["total_files_processed"] += 1
            overall_stats["total_petitions_processed"] += result["total_petitions"]
            overall_stats["total_accurate_predictions"] += result[
                "accurate_predictions"
            ]
            overall_stats["total_validation_errors"] += result["validation_errors"]

            # Accumulate token usage
            usage = overall_stats["total_usage"]
            result_usage = result["token_usage"]
            usage.prompt_tokens += result_usage.prompt_tokens
            usage.completion_tokens += result_usage.completion_tokens
            usage.total_tokens += result_usage.total_tokens
            usage.total_cost_usd += result_usage.total_cost_usd

            # Store category result
            overall_stats["category_results"].append(
                {
                    "main_category": main_category,
                    "sub_category": sub_category,
                    "file_path": str(json_file.relative_to(category_exports_path)),
                    "total_petitions": result["total_petitions"],
                    "accurate_predictions": result["accurate_predictions"],
                    "accuracy_rate": result["accuracy_rate"],
                    "token_usage": {
                        "prompt_tokens": result_usage.prompt_tokens,
                        "completion_tokens": result_usage.completion_tokens,
                        "total_tokens": result_usage.total_tokens,
                        "total_cost_usd": result_usage.total_cost_usd,
                    },
                }
            )

        except Exception as e:
            logger.error(f"Error processing file {json_file}: {e}")
            continue

    # Save overall statistics
    save_overall_statistics(overall_stats, output_path)

    # Print summary
    print_summary(overall_stats)


def process_category_file(
    json_file: Path, main_category: str, sub_category: str, output_path: Path
) -> Dict:
    """
    Process a single category JSON file.

    Args:
        json_file: Path to the JSON file
        main_category: Main category name
        sub_category: Sub category name
        output_path: Output directory path

    Returns:
        Dictionary with processing results
    """
    # Load petition data
    with open(json_file, "r", encoding="utf-8") as f:
        data = json.load(f)

    petition_data = [PetitionInput.model_validate(item) for item in data]
    output_data = []

    # Initialize token usage tracking
    total_usage = TokenUsage()
    accurate_count = 0
    total_validation_errors = 0

    logger.info(
        f"Processing {len(petition_data)} petitions for {main_category}_{sub_category}"
    )

    # Process each petition
    for i, petition in enumerate(petition_data):
        if i % 10 == 0:  # Log progress every 10 petitions
            logger.info(f"Processing petition {i + 1}/{len(petition_data)}...")

        ground_truth = get_ground_truth(petition.sub_category)
        ai_response, usage = get_ai_category_response(
            petition.petition_subject, petition.petition_content
        )

        # Accumulate token usage
        total_usage.prompt_tokens += usage.prompt_tokens
        total_usage.completion_tokens += usage.completion_tokens
        total_usage.total_tokens += usage.total_tokens
        total_usage.total_cost_usd += usage.total_cost_usd

        # Use retry mechanism for validation
        ai_main_category, ai_sub_category, is_valid, validation_errors = (
            validate_category_with_retry(ai_response)
        )
        total_validation_errors += validation_errors

        if is_valid:
            is_accurate = ai_sub_category in ground_truth
            if is_accurate:
                accurate_count += 1
        else:
            is_accurate = False

        output_data.append(
            PetitionOutput(
                original_data=petition.model_dump(),
                main_category=ai_main_category,
                sub_category=ai_sub_category,
                ground_truth_category=ground_truth,
                is_accurate=is_accurate,
            )
        )

    # Calculate accuracy rate
    total_petitions = len(petition_data)
    accuracy_rate = (
        (accurate_count / total_petitions * 100) if total_petitions > 0 else 0
    )

    # Create output filename
    safe_main_category = main_category.replace("/", "_").replace("\\", "_")
    safe_sub_category = sub_category.replace("/", "_").replace("\\", "_")
    output_filename = f"{safe_main_category}_{safe_sub_category}_ai_results.json"
    output_file_path = output_path / output_filename

    # Save results
    write_petition_data(str(output_file_path), output_data)

    logger.info(f"Saved results to: {output_file_path}")
    logger.info(f"Accuracy: {accurate_count}/{total_petitions} ({accuracy_rate:.2f}%)")

    return {
        "total_petitions": total_petitions,
        "accurate_predictions": accurate_count,
        "accuracy_rate": accuracy_rate,
        "validation_errors": total_validation_errors,
        "token_usage": total_usage,
        "output_file": str(output_file_path),
    }


def save_overall_statistics(stats: Dict, output_path: Path) -> None:
    """
    Save overall processing statistics to a JSON file.

    Args:
        stats: Overall statistics dictionary
        output_path: Output directory path
    """
    # Calculate overall accuracy
    total_petitions = stats["total_petitions_processed"]
    total_accurate = stats["total_accurate_predictions"]
    overall_accuracy = (
        (total_accurate / total_petitions * 100) if total_petitions > 0 else 0
    )

    summary_stats = {
        "summary": {
            "total_files_processed": stats["total_files_processed"],
            "total_petitions_processed": total_petitions,
            "total_accurate_predictions": total_accurate,
            "overall_accuracy_rate": round(overall_accuracy, 2),
            "total_validation_errors": stats["total_validation_errors"],
            "total_token_usage": {
                "prompt_tokens": stats["total_usage"].prompt_tokens,
                "completion_tokens": stats["total_usage"].completion_tokens,
                "total_tokens": stats["total_usage"].total_tokens,
                "total_cost_usd": stats["total_usage"].total_cost_usd,
            },
        },
        "category_results": stats["category_results"],
    }

    stats_file = output_path / "batch_processing_statistics.json"
    with open(stats_file, "w", encoding="utf-8") as f:
        json.dump(summary_stats, f, ensure_ascii=False, indent=4)

    logger.info(f"Overall statistics saved to: {stats_file}")


def print_summary(stats: Dict) -> None:
    """
    Print a summary of the batch processing results.

    Args:
        stats: Overall statistics dictionary
    """
    total_petitions = stats["total_petitions_processed"]
    total_accurate = stats["total_accurate_predictions"]
    overall_accuracy = (
        (total_accurate / total_petitions * 100) if total_petitions > 0 else 0
    )
    usage = stats["total_usage"]

    print("\n" + "=" * 80)
    print("BATCH PROCESSING SUMMARY")
    print("=" * 80)
    print(f"Total files processed: {stats['total_files_processed']}")
    print(f"Total petitions processed: {total_petitions:,}")
    print(f"Total accurate predictions: {total_accurate:,}")
    print(f"Overall accuracy rate: {overall_accuracy:.2f}%")
    print(f"Total validation errors: {stats['total_validation_errors']:,}")
    print("\nOpenAI API Usage:")
    print(f"  Prompt tokens: {usage.prompt_tokens:,}")
    print(f"  Completion tokens: {usage.completion_tokens:,}")
    print(f"  Total tokens: {usage.total_tokens:,}")
    print(f"  Total cost: ${usage.total_cost_usd:.4f} USD")
    print("=" * 80)

    # Print top 10 categories by accuracy
    category_results = stats["category_results"]
    if category_results:
        print("\nTop 10 Categories by Accuracy:")
        sorted_categories = sorted(
            category_results, key=lambda x: x["accuracy_rate"], reverse=True
        )
        for i, cat in enumerate(sorted_categories[:10], 1):
            print(
                f"{i:2d}. {cat['main_category']}_{cat['sub_category']}: "
                f"{cat['accuracy_rate']:.2f}% ({cat['accurate_predictions']}/{cat['total_petitions']})"
            )

        print("\nBottom 10 Categories by Accuracy:")
        for i, cat in enumerate(sorted_categories[-10:], 1):
            print(
                f"{i:2d}. {cat['main_category']}_{cat['sub_category']}: "
                f"{cat['accuracy_rate']:.2f}% ({cat['accurate_predictions']}/{cat['total_petitions']})"
            )

# TDD Implementation Plan for Category-Based Data Export Mode

## 1. Feature Analysis

### Identified Features:

-   **Category Export Mode:** A new CLI mode in `es_export_run.py` to export Elasticsearch documents based on a predefined category structure.
-   **Configurable Document Limit:** Ability to set a maximum number of documents per exported file via a CLI argument.
-   **Structured Output:** Exported data is saved in a nested directory structure corresponding to main and sub-categories.
-   **Robust Error Handling:** The script must gracefully handle missing input files, malformed JSON, and empty search results.

### Feature Categorization:

-   **Must-Have Features:** All identified features are considered essential for the initial release as per the PRD.

## 2. Implementation Stages (TDD Workflow)

### Stage 1: Foundation & Setup

**Goal:** Prepare the environment for developing the new feature.

-   [x] **[Tidy First]** Create a dedicated test file `tests/test_category_export.py` for the new functionality.
-   [x] **[Tidy First]** In `tests/test_category_export.py`, set up the basic test class `TestCategoryExportMode`.
-   [ ] **[Tidy First]** Add a mock version of `data/categories_2024.json` in a `tests/fixtures` directory for predictable test runs.
-   [ ] **[Tidy First]** Create a `UseCase` interface for the category export logic to decouple it from the main script entry point.

### Stage 2: Core Feature Implementation (TDD Sprints)

**Goal:** Implement the core functionality of the category export mode using a strict Red-Green-Refactor cycle.

#### Feature: CLI Argument Parsing

-   [ ] **[Test]** In `tests/test_category_export.py`, write a failing test to verify that calling `es_export_run.py` with `--mode category_export` correctly triggers the new export logic.
-   [ ] **[Implementation]** Modify `es_export_run.py` to recognize the `--mode category_export` argument and call a placeholder function for the new use case.
-   [ ] **[Refactor]** Clean up the argument parsing logic in `es_export_run.py` to make it more modular and extensible for future modes.
-   [ ] **[Test]** Write a failing test to check if the `--max_size` argument is correctly parsed as an integer.
-   [ ] **[Implementation]** Add the `--max_size` argument using `argparse`, ensuring it has a default value of `50`.
-   [ ] **[Refactor]** Encapsulate the CLI argument configuration into a dedicated function.

#### Feature: Category File Handling

-   [ ] **[Test]** Write a failing test for the scenario where `data/categories_2024.json` is missing, asserting that a specific `FileNotFoundError` is handled and a proper error is logged.
-   [ ] **[Implementation]** In the use case, implement the logic to read `data/categories_2024.json`, wrapping it in a `try...except` block to catch `FileNotFoundError`.
-   [ ] **[Test]** Write a failing test for when `data/categories_2024.json` contains malformed JSON, asserting that a `JSONDecodeError` is handled.
-   [ ] **[Implementation]** Add error handling for `json.JSONDecodeError`.
-   [ ] **[Refactor]** Create a dedicated `FileRepository` class to handle reading and parsing the category file, separating file I/O from the core use case logic.

#### Feature: Elasticsearch Querying & Data Export

-   [ ] **[Test]** Using `unittest.mock`, write a failing test that verifies the Elasticsearch repository is called with the correct query parameters (`record_channel`, `main_category`, `sub_category`, and `size`).
-   [ ] **[Implementation]** Implement the loop that iterates through categories and constructs the correct Elasticsearch query for each pair.
-   [ ] **[Refactor]** Move the query construction logic into a separate method within the `ElasticsearchRepository`.
-   [ ] **[Test]** Write a failing test to ensure that if the ES query returns no documents, a warning is logged and no file is created.
-   [ ] **[Implementation]** Add a condition to check the number of search hits. If zero, log a warning and continue to the next category.
-   [ ] **[Test]** Write a failing test to verify that the output directory `output/{main_category}/` is created correctly.
-   [ ] **[Implementation]** Use `pathlib.Path.mkdir(parents=True, exist_ok=True)` to create the necessary output directories before writing a file.
-   [ ] **[Test]** Write a failing test to confirm that the search results are correctly written to `output/{main_category}/{sub_category}.json`.
-   [ ] **[Implementation]** Implement the file-writing logic to save the list of documents as a JSON file.
-   [ ] **[Refactor]** Abstract the file-writing logic into the `FileRepository` to handle saving the output files.

### Stage 3: Finalization & Documentation

**Goal:** Ensure the code is clean, well-documented, and meets all project standards.

-   [ ] **[Refactor]** Conduct a final review of the entire implementation, ensuring adherence to the Clean Architecture principles (e.g., `UseCase` depends on `Repository` interfaces, not implementations).
-   [ ] **[Docs]** Update `README.md` with instructions on how to run the new `category_export` mode, including details on the `--max_size` argument.
-   [ ] **[Docs]** Add a section to `docs/project_structure.md` explaining the role of the new `UseCase`, `Repository`, and the `output/` directory.

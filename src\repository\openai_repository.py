import logging
from dataclasses import dataclass

import instructor
import openai
from tenacity import (
    RetryError,
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_fixed,
)

from models import AICategoryResponse

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TokenUsage:
    """Store token usage and cost information"""

    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    total_cost_usd: float = 0.0


# GPT-4o-mini pricing per 1K tokens
INPUT_COST_PER_1K = 0.0004  # USD
OUTPUT_COST_PER_1K = 0.0016  # USD


def calculate_cost(prompt_tokens: int, completion_tokens: int) -> float:
    """Calculate total cost in USD based on token usage"""
    input_cost = (prompt_tokens / 1000) * INPUT_COST_PER_1K
    output_cost = (completion_tokens / 1000) * OUTPUT_COST_PER_1K
    return input_cost + output_cost


PROMPT_TEMPLATE = """
<prompt>
    <role>
    你是一個隸屬於市政府資訊處的「市政案件 AI 分類引擎核心專家」。你的唯一職責是接收市民陳情案件的文字內容，並以極高的精準度與穩定性，將其分派到正確的業務單位。你的輸出將直接進入自動化派案系統，任何錯誤都可能導致市府資源浪費與民怨。因此，你必須絕對遵循以下的指示與分類原則。
    </role>

    <category_list>
		- 交通號誌、標誌、標線及大眾運輸_公車問題及站牌、候車亭設施管理
		- 交通號誌、標誌、標線及大眾運輸_交通標誌、標線、反射鏡設置或移除
		- 交通號誌、標誌、標線及大眾運輸_其他
		- 交通號誌、標誌、標線及大眾運輸_交通號誌增設或紅綠燈秒數調整
		- 交通號誌、標誌、標線及大眾運輸_路邊停車格問題
		- 交通號誌、標誌、標線及大眾運輸_公共自行車(U-Bike)租賃問題
		- 交通號誌、標誌、標線及大眾運輸_交通號誌(紅綠燈)故障或損壞傾斜
		- 交通號誌、標誌、標線及大眾運輸_交通標誌牌面、反射鏡損壞傾斜
		- 交通號誌、標誌、標線及大眾運輸_捷運營運及管理
		- 交通號誌、標誌、標線及大眾運輸_捷運建設工程相關問題
		- 交通號誌、標誌、標線及大眾運輸_公車動態系統問題
		- 交通號誌、標誌、標線及大眾運輸_停車費問題
		- 交通號誌、標誌、標線及大眾運輸_計程車問題及招呼站設施管理
		- 交通號誌、標誌、標線及大眾運輸_免費市民公車(樂活巴)問題
		- 路霸排除_占用道路、騎樓及人行道
		- 路霸排除_無牌廢棄車查報
		- 路霸排除_有牌廢棄車查報
		- 路霸排除_其他
		- 路霸排除_廣告車輛長期占用停車格
		- 噪音、污染及環境維護_髒亂點查報
		- 噪音、污染及環境維護_住家、改裝車噪音
		- 噪音、污染及環境維護_綜合性環境污染
		- 噪音、污染及環境維護_其他
		- 噪音、污染及環境維護_營業場所、工廠及施工噪音
		- 噪音、污染及環境維護_空氣污染
		- 噪音、污染及環境維護_住宅內人與動物噪音
		- 噪音、污染及環境維護_垃圾車清運動線及管理
		- 噪音、污染及環境維護_工廠排放廢水、河川污染
		- 噪音、污染及環境維護_違規張貼廣告物
		- 噪音、污染及環境維護_廢棄物清運預約
		- 噪音、污染及環境維護_犬貓屍體清除
		- 其他類別_其他建議、諮詢或陳情
		- 其他類別_其他檢舉案件
		- 警政及交通裁罰業務_其他
		- 警政及交通裁罰業務_警政風紀
		- 警政及交通裁罰業務_交通罰單申訴
		- 警政及交通裁罰業務_交通疏導或壅塞通報
		- 警政及交通裁罰業務_治安維護
		- 警政及交通裁罰業務_妨害風化(俗)
		- 警政及交通裁罰業務_闖紅燈(超速)照相桿增設或維護
		- 警政及交通裁罰業務_監視器問題
		- 警政及交通裁罰業務_車輛拖吊爭議
		- 建築管理_公寓大廈管理問題
		- 建築管理_一般違建查報
		- 建築管理_建築物公共安全問題
		- 建築管理_其他
		- 建築管理_興建中違建查報
		- 建築管理_建築法規問題
		- 建築管理_違規招牌或樹立廣告物查報
		- 建築管理_社會住宅管理
		- 建築管理_領有建造執照施工損鄰
		- 建築管理_已查報違建拆除問題
		- 道路、水溝維護_路面不平整或掏空破洞
		- 道路、水溝維護_其他
		- 道路、水溝維護_道路施工時間、交通管制及安全管理問題
		- 道路、水溝維護_道路側溝清淤或惡臭處理
		- 道路、水溝維護_水溝溝蓋維修
		- 道路、水溝維護_道路淹(積)水通報
		- 道路、水溝維護_孔蓋異音
		- 道路、水溝維護_電纜下地或纜線垂落
		- 道路、水溝維護_路面油漬清除
		- 路燈、路樹及公園管理維護_其他
		- 路燈、路樹及公園管理維護_公園、綠地及路樹養護
		- 路燈、路樹及公園管理維護_路燈故障
		- 路燈、路樹及公園管理維護_公園設施損壞
		- 路燈、路樹及公園管理維護_路樹傾倒
		- 路燈、路樹及公園管理維護_路燈新增或遷移申請
		- 路燈、路樹及公園管理維護_新闢公園建議案
		- 衛生行政_食品安全衛生
		- 衛生行政_藥品及化妝品管理
		- 衛生行政_醫療管理
		- 衛生行政_菸害防制
		- 衛生行政_其他
		- 衛生行政_傳染病防治及預防接種
		- 衛生行政_自殺防治及心理健康
		- 教育及體育_國小學校問題
		- 教育及體育_國中學校問題
		- 教育及體育_其他
		- 教育及體育_高級中等學校問題
		- 教育及體育_補教問題
		- 教育及體育_體育活動及場務管理
		- 教育及體育_幼兒園問題
		- 教育及體育_特殊教育問題
		- 教育及體育_教師介聘甄選
		- 教育及體育_社區大學、樂齡學習等終身教育問題
		- 教育及體育_學校體育問題
		- 勞動行政_檢舉公司(雇主)違反勞動法規
		- 勞動行政_勞工法令諮詢
		- 勞動行政_其他
		- 勞動行政_勞資糾紛協調
		- 勞動行政_移工業務
		- 勞動行政_就業服務及職業訓練
		- 勞動行政_就業歧視
		- 勞動行政_身障就業
		- 工商、經濟及稅務_檢舉商店違規營業
		- 工商、經濟及稅務_水、電、瓦斯等公用事業問題
		- 工商、經濟及稅務_稅務問題
		- 工商、經濟及稅務_其他
		- 工商、經濟及稅務_檢舉工廠違規營業
		- 工商、經濟及稅務_市場攤販管理
		- 工商、經濟及稅務_工商登記問題
		- 工商、經濟及稅務_檢舉旅館、民宿違規營業
		- 社會救助及社會福利_其他
		- 社會救助及社會福利_身心障礙福利及復康巴士
		- 社會救助及社會福利_銀髮族福利、長期照顧及日間照顧
		- 社會救助及社會福利_社會救助(中、低收入戶、急難救助及馬上關懷等)
		- 社會救助及社會福利_婦女福利、特殊境遇家庭扶助、生育津貼及育兒津貼
		- 社會救助及社會福利_婦女(幼)館、親子館及公設民營托嬰中心管理
		- 社會救助及社會福利_兒少福利、兒童早療補助、弱勢兒少生活扶助、緊急生活扶助及醫療補助
		- 社會救助及社會福利_家庭暴力、性侵害、兒少保護及性騷擾等防治工作
		- 社會救助及社會福利_住宅租金補貼問題
		- 社會救助及社會福利_人民團體組織輔導
		- 社會救助及社會福利_家庭服務中心
		- 地政服務_檢舉土地違規使用
		- 地政服務_其他
		- 地政服務_不動產交易
		- 地政服務_土地徵收
		- 地政服務_土地及建物登記
		- 地政服務_土地重劃
		- 地政服務_土地測量
		- 地政服務_地籍圖重測
		- 消防行政_消防設備、安全檢查
		- 消防行政_防火巷違建、堆放雜物
		- 消防行政_其他
		- 消防行政_瓦斯桶儲放問題
		- 消防行政_消防栓(設置、移位、告示牌)
		- 感謝函、服務品質及網站、APP管理問題_感謝函
		- 感謝函、服務品質及網站、APP管理問題_市府網站或APP管理問題
		- 感謝函、服務品質及網站、APP管理問題_服務態度問題
		- 感謝函、服務品質及網站、APP管理問題_行政效率問題
		- 感謝函、服務品質及網站、APP管理問題_專業知識問題
		- 感謝函、服務品質及網站、APP管理問題_其他
		- 動物收容、保護及捕捉_其他
		- 動物收容、保護及捕捉_動物收容及認養問題
		- 動物收容、保護及捕捉_動物受困、受傷通報
		- 動物收容、保護及捕捉_捕蜂、抓蛇
		- 文化藝術及圖書管理_圖書館、閱覽室及館舍管理
		- 文化藝術及圖書管理_藝文展演活動
		- 文化藝術及圖書管理_其他
		- 文化藝術及圖書管理_藝文館舍管理
		- 文化藝術及圖書管理_文化資產問題
		- 民政業務_戶政服務
		- 民政業務_其他
		- 民政業務_宗教事務
		- 民政業務_殯葬禮儀
		- 民政業務_兵役問題
		- 政風行政_行政違失
		- 政風行政_其他瀆職情形
		- 政風行政_行、收賄
		- 市民卡業務_其他
		- 市民卡業務_市民卡優惠及加值服務建議
		- 市民卡業務_卡片感應及使用問題
		- 市民卡業務_學生卡申辦問題
		- 市民卡業務_一般卡申辦問題
		- 市民卡業務_行動卡、聯名卡申辦問題
		- 市民卡業務_志工卡申辦問題
		- 市民卡業務_敬老卡申辦問題

    </category_list>

    <category_definitions>
		<category name="文化藝術及圖書管理_藝文館舍管理">
			<description>此類別處理關於特定藝文館舍（如：美術館、博物館、文化中心、演藝廳）的營運管理、設施維護、服務品質及場地租借等問題。</description>
			<keywords>美術館, 博物館, 文化中心, 演藝廳, 場地租借, 開放時間, 設施維護, 服務品質, 參觀動線</keywords>
			<exclusions>若案件核心為「圖書館」或「閱覽室」管理，應選擇「圖書館、閱覽室及館舍管理」。若為針對特定「展演活動」內容的陳情，應選擇「藝文展演活動」。</exclusions>
		</category>
		<category name="社會救助及社會福利_銀髮族福利、長期照顧及日間照顧">
			<description>此類別專注於銀髮族（老人）的相關福利政策、長期照顧（長照）服務、日間照顧中心的管理與申請問題。</description>
			<keywords>長照, 日間照顧, 老人福利, 獨居老人, 居家服務, 喘息服務, 敬老, 銀髮族</keywords>
			<exclusions>若為申請「敬老卡」本身的問題，應選擇「市民卡業務_敬老卡申辦問題」。若非針對銀髮族，而是一般性的社會救助（如中低收入戶），應選擇其他相應的福利類別。</exclusions>
		</category>
		<category name="路燈、路樹及公園管理維護_其他">
			<description>此類別為「路燈、路樹及公園管理維護」主類別下的通用選項。當案件內容明確與公園、路樹、路燈相關，但無法對應到其他更具體的子類別時使用。例如：對公園整體景觀的建議。</description>
			<keywords>公園管理, 景觀建議, 市容美化, 認養, 綜合性維護</keywords>
			<exclusions>嚴禁使用此類別處理可以明確歸類的案件。例如：「路燈不亮」應選擇「路燈故障」；「遊樂設施壞掉」應選擇「公園設施損壞」；「樹倒了」應選擇「路樹傾倒」。</exclusions>
		</category>
        <category name="衛生行政_醫療管理">
            <description>此類別專門處理與「醫療機構」相關的行政、服務品質、收費及權益爭議。核心在於管理面與服務體驗。</description>
            <keywords>醫療糾紛, 醫院管理, 診所收費, 病歷, 醫師態度, 護士態度, 掛號問題, 等候時間過長</keywords>
            <exclusions>如果案件核心是關於特定疾病的「傳播」或「疫苗接種」，則不屬於此類。</exclusions>
        </category>
        <category name="衛生行政_傳染病防治及預防接種">
            <description>此類別處理所有關於「法定傳染病」的通報、防治措施、隔離政策，以及「公費或自費疫苗」的接種問題。</description>
            <keywords>防疫, 隔離, 確診, 疫苗, 預防針, 登革熱, 流感, 噴藥, 篩檢</keywords>
            <exclusions>不包含一般性的醫療服務品質問題。</exclusions>
        </category>
        <category name="衛生行政_自殺防治及心理健康">
            <description>此類別專注於心理健康支持、自殺防治通報與關懷、以及相關諮詢服務。</description>
            <keywords>心理諮商, 心理衛生, 生命線, 自殺傾向, 情緒困擾, 壓力</keywords>
        </category>
        <category name="警政及交通裁罰業務_警政風紀">
            <description>此類別專門處理針對「警察人員」個人的違法、失職或不當行為的檢舉。</description>
            <keywords>警察態度不佳, 員警收賄, 濫用職權, 吃案, 執法不公</keywords>
            <exclusions>如果抱怨的是交通罰單內容本身，應歸類於「交通罰單申訴」；如果是一般治安問題，應歸類於「治安維護」。</exclusions>
        </category>
        <category name="路燈、路樹及公園管理維護_公園、綠地及路樹養護">
            <description>此類別處理關於公園、綠地、路樹的「常態性維護」問題，如清潔、修剪、澆水、施肥等。</description>
            <keywords>公園清潔, 除草, 路樹修剪, 植栽枯死, 環境美化</keywords>
            <exclusions>如果涉及「具體設施」的損壞（如遊樂設施、座椅、路燈），應選擇更精確的子類別。</exclusions>
        </category>
        <category name="社會救助及社會福利_人民團體組織輔導">
            <description>此類別處理關於人民團體（如協會、工會、基金會）的成立、運作、選舉等相關法規諮詢與輔導事宜。</description>
            <keywords>協會成立, 工會選舉, 組織章程, 社會團體, 職業團體</keywords>
        </category>
        <category name="消防行政_消防設備、安全檢查">
            <description>此類別處理關於建築物或場所的消防安全設備（如滅火器、警報器、灑水系統）的檢查、維護及檢舉。</description>
            <keywords>消防安檢, 滅火器過期, 消防栓, 灑水頭, 警報器, 安全門</keywords>
            <exclusions>如果檢舉的是在「防火巷」堆放雜物，應選擇「防火巷違建、堆放雜物」。</exclusions>
        </category>
    </category_definitions>

    <instructions>
        <cognitive_framework>
        你必須嚴格遵循以下內部認知框架來處理每一個案件，此過程嚴禁輸出：

        1.  **核心意圖分析 (Intent Analysis)**:
            -   首先，通讀 `<case_content>`，辨識市民的核心訴求。
            -   判斷其主要意圖是「檢舉告發」、「請求協助」、「諮詢問題」、還是「提出建議」。這有助於初步鎖定「主案類」。

        2.  **階層式分類匹配 (Hierarchical Matching)**:
            -   **第一層 (主案類)**: 根據核心意圖與內容關鍵詞，從 `<category_list>` 中選擇**唯一一個**最相關的「主案類」。例如，內容涉及「馬路坑洞」，主案類應為「道路、水溝維護」。
            -   **第二層 (子案類)**: 在已鎖定的主案類下，檢視其所有的「子案類」，選擇一個**最精確描述**案件核心問題的選項。例如，在「道路、水溝維護」下，「路面不平整或掏空破洞」會是最佳選擇。

		3. **相似類別辨析 (Disambiguation)**:
            -   在選擇子案類時，如果存在多個看似相關的選項，必須參考 `<category_definitions>` 中的 `description`, `keywords`, 和 `exclusions` 進行精確辨析。

        4.  **決策捷徑與例外處理 (Heuristics & Exception Handling)**:
            -   **`..._其他` 的使用**: 如果在選定的「主案類」中，找不到任何一個精確匹配的「子案類」，必須優先選擇該主案類的 `..._其他` 選項。**嚴禁**為了匹配而選擇一個不完全貼切的子案類。
            -   **最終備用選項**: 僅在**沒有任何一個「主案類」**能合理涵蓋案件內容時，才可使用最終的「其他類別_其他建議、諮詢或陳情」。

        5.  **最終格式化 (Final Formatting)**:
            -   將最終選定的「主案類_子案類」完整字串，放入指定的 JSON 結構中。
            -   再次確認類別名稱與 `<category_list>` 中的條目一字不差。
        </cognitive_framework>
    </instructions>

    <examples>
    以下是幾個成功分類的範例，請學習其分類邏輯：

    1.  **案件內容**: "我家門口的路燈整個晚上都沒亮，黑漆漆的，老人家走路很危險，麻煩幫忙修一下。"
        **輸出**: `{ "category": "路燈、路樹及公園管理維護_路燈故障" }`
        **邏輯**: 明確的路燈損壞請求，直接對應。

    2.  **案件內容**: "週末去 A 公園散步，覺得裡面的花草有點稀疏，椅子上也有點髒污，希望市府能多用點心維護市容。"
        **輸出**: `{ "category": "路燈、路樹及公園管理維護_其他" }`
        **邏輯**: 屬於公園養護的建議，但未涉及具體的「設施損壞」或「路樹傾倒」，因此在該主案類下選擇「其他」最為恰當。

    3.  **案件內容**: "隔壁的餐廳把桌椅都擺到人行道上做生意，行人都要走到馬路上，真的很誇張！"
        **輸出**: `{ "category": "路霸排除_占用道路、騎樓及人行道" }`
        **邏輯**: 核心問題是「占用人行道」，完全符合路霸排除的定義。

    4.  **案件內容**: "我對現在的勞工政策有些想法，希望能建議市長多關注青年低薪問題，我覺得可以從...開始著手。"
        **輸出**: `{ "category": "其他類別_其他建議、諮詢或陳情" }`
        **邏輯**: 案件內容是廣泛的政策建議，不屬於任何一個特定的業務主案類（如勞資糾紛或檢舉），因此使用最終的備用選項。
    </examples>

    <output_format>
    -   你的回應**必須且只能是**一個 JSON 物件。
    -   JSON 物件**絕不能**包含任何 Markdown 標記 (例如 ```json ... ```)。
    -   JSON 物件的結構必須嚴格遵守：`{ "category": "<選擇的類別名稱>" }`。
    -   不要輸出任何其他文字、解釋或註解。
    </output_format>

    <case_to_classify>
		{case_subject}
        <case_content>
        {case_content}
        </case_content>
    </case_to_classify>
</prompt>
"""


@retry(
    stop=stop_after_attempt(3),
    wait=wait_fixed(2),
    retry=retry_if_exception_type(openai.APIError),
    before_sleep=before_sleep_log(logger, logging.WARNING),
)
def get_ai_category_response(
    petition_subject: str,
    petition_content: str,
) -> tuple[AICategoryResponse | None, TokenUsage]:
    """
    Calls the OpenAI API to get a category for the given petition content.
    Includes retry logic for API errors.

    Args:
        petition_content: The content of the petition.

    Returns:
        A tuple of (AICategoryResponse object, TokenUsage) or (None, TokenUsage) if all retries fail.
    """
    token_usage = TokenUsage()

    try:
        client = instructor.from_provider(
            "openai/gpt-4o-mini",
        )
        prompt = format_prompt(petition_subject, petition_content)

        response = _create_chat_completion_with_retry(client, prompt)

        if response:
            # Try different ways to access usage information
            usage_info = None
            if hasattr(response, "usage"):
                usage_info = response.usage
            elif hasattr(response, "_raw_response") and hasattr(
                response._raw_response, "usage"
            ):
                # 使用Instruct套件的話, 要去_raw_response取得usage欄位
                usage_info = response._raw_response.usage
            elif hasattr(response, "raw") and hasattr(response.raw, "usage"):
                usage_info = response.raw.usage

            if usage_info:
                token_usage.prompt_tokens = usage_info.prompt_tokens
                token_usage.completion_tokens = usage_info.completion_tokens
                token_usage.total_tokens = usage_info.total_tokens
                token_usage.total_cost_usd = calculate_cost(
                    token_usage.prompt_tokens, token_usage.completion_tokens
                )
            else:
                logger.error("No usage information found in response")
                # Debug: print response attributes
                logger.debug(f"Response type: {type(response)}")
                logger.debug(f"Response attributes: {dir(response)}")

        return response, token_usage
    except RetryError:
        logger.error("All retry attempts failed for getting AI category response.")
        return None, token_usage


@retry(
    stop=stop_after_attempt(3),
    wait=wait_fixed(2),
    retry=retry_if_exception_type(openai.APIError),
    before_sleep=before_sleep_log(logger, logging.WARNING),
)
def _create_chat_completion_with_retry(client, prompt):
    return client.chat.completions.create(
        response_model=AICategoryResponse,
        messages=[
            {"role": "user", "content": prompt},
        ],
    )


def format_prompt(petition_content: str) -> str:
    """
    Formats the prompt template by injecting the petition content.

    Args:
        petition_content: The content of the petition.

    Returns:
        The formatted prompt string.
    """
    return PROMPT_TEMPLATE.replace("{case_content}", petition_content)

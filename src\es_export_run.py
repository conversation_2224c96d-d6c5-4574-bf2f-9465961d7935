import argparse
import json
import os
import sys
from datetime import datetime
from pathlib import Path

from dotenv import load_dotenv

from repository.elasticsearch_repo import ElasticsearchRepository


def valid_date(s):
    """
    Helper function to validate the date format (YYYY-MM-DD).
    """
    try:
        return datetime.strptime(s, "%Y-%m-%d")
    except ValueError:
        msg = f"Not a valid date: '{s}'."
        raise argparse.ArgumentTypeError(msg)


def load_categories():
    """
    Load categories from the categories_2024.json file.

    Returns:
        dict: Dictionary containing main categories and their sub-categories

    Raises:
        FileNotFoundError: If the categories file is not found
        json.JSONDecodeError: If the categories file contains invalid JSON
    """
    # Try different possible paths for the categories file
    possible_paths = [
        Path("data/categories_2024.json"),  # When running from src directory
        Path("src/data/categories_2024.json"),  # When running from project root
    ]

    categories_file = None
    for path in possible_paths:
        if path.exists():
            categories_file = path
            break

    if categories_file is None:
        raise FileNotFoundError(
            f"Categories file not found in any of these locations: {possible_paths}"
        )

    try:
        with open(categories_file, "r", encoding="utf-8") as f:
            return json.load(f)
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"Invalid JSON in categories file: {e}")


def export_by_category(args):
    """
    Export data by category mode.

    Args:
        args: Parsed command line arguments
    """
    try:
        # Load categories
        print("Loading categories...", end="", flush=True)
        categories = load_categories()
        print("Done!")

        # Count total categories for progress tracking
        total_categories = sum(len(sub_cats) for sub_cats in categories.values())
        print(f"Found {total_categories} category combinations to process.")

        # Initialize repository
        repo = ElasticsearchRepository()
        start_date_str = args.start_date.strftime("%Y-%m-%d")
        end_date_str = args.end_date.strftime("%Y-%m-%d")

        fields = [
            "case_id",
            "case_source",
            "record_channel",
            "record_time",
            "main_category",
            "sub_category",
            "petition_content",
            "petition_subject",
        ]

        # Create output directory structure
        output_base_dir = Path("data", "category_exports")
        output_base_dir.mkdir(exist_ok=True)

        processed_count = 0

        # Process each main category and sub-category combination
        for main_category, sub_categories in categories.items():
            # Create main category directory
            main_cat_dir = output_base_dir / main_category
            main_cat_dir.mkdir(exist_ok=True)

            for sub_category in sub_categories:
                processed_count += 1
                print(
                    f"Processing ({processed_count}/{total_categories}): {main_category} -> {sub_category}...",
                    end="",
                    flush=True,
                )

                try:
                    # Search for documents matching this category combination
                    records = []
                    for record in repo.export_data_by_category(
                        start_date_str,
                        end_date_str,
                        "市政信箱",  # record_channel
                        main_category,
                        sub_category,
                        fields,
                        args.max_size,
                    ):
                        records.append(record)

                    if records:
                        # Create JSON file for this sub-category
                        filename = f"{sub_category}.json"
                        output_file = main_cat_dir / filename

                        with open(output_file, "w", encoding="utf-8") as f:
                            json.dump(records, f, ensure_ascii=False, indent=4)

                        print(f" Found {len(records)} records")
                    else:
                        print(" No documents found")

                except Exception as e:
                    print(f" Error: {e}")
                    continue

        print(
            f"\nCategory export completed! Results saved in '{output_base_dir}' directory."
        )

    except FileNotFoundError as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"An error occurred during category export: {e}", file=sys.stderr)
        sys.exit(1)


def main():
    """
    Main function to handle argument parsing and trigger the export process.
    """
    parser = argparse.ArgumentParser(
        description="Export data from Elasticsearch to a JSON file."
    )
    parser.add_argument(
        "--start-date",
        required=True,
        help="The start date for the export range (YYYY-MM-DD).",
        type=valid_date,
    )
    parser.add_argument(
        "--end-date",
        required=True,
        help="The end date for the export range (YYYY-MM-DD).",
        type=valid_date,
    )
    parser.add_argument(
        "--mode",
        choices=["default", "category_export"],
        default="default",
        help="Export mode: 'default' for date range export, 'category_export' for category-based export (default: default).",
    )
    parser.add_argument(
        "--max-size",
        type=int,
        default=50,
        help="Maximum number of documents per category file in category_export mode (default: 50).",
    )
    args = parser.parse_args()

    if args.start_date > args.end_date:
        print("Error: Start date cannot be after end date.", file=sys.stderr)
        sys.exit(1)

    load_dotenv(".env")

    RUNTIME_ENV = os.environ["TYCG_BE_RUNTIME_ENV"]
    if RUNTIME_ENV == "PROD":
        load_dotenv(os.path.join("es", ".env.prod"))
    elif RUNTIME_ENV == "DEV":
        load_dotenv(os.path.join("es", ".env.dev"))

    # Route to appropriate export function based on mode
    if args.mode == "category_export":
        export_by_category(args)
    else:
        # Default mode - original functionality
        try:
            repo = ElasticsearchRepository()
            start_date_str = args.start_date.strftime("%Y-%m-%d")
            end_date_str = args.end_date.strftime("%Y-%m-%d")

            fields = [
                "case_id",
                "case_source",
                "record_channel",
                "record_time",
                "main_category",
                "sub_category",
                "petition_content",
                "petition_subject",
            ]
            print("Exporting records...", end="", flush=True)
            records = []
            for record in repo.export_data_by_date_range(
                start_date_str, end_date_str, fields
            ):
                records.append(record)
                print(".", end="", flush=True)
            print("Done!")

            if not records:
                print("No records found for the given date range.")
                sys.exit(0)
            else:
                filename = f"export_{start_date_str}_to_{end_date_str}.json"
                with open(filename, "w", encoding="utf-8") as f:
                    json.dump(records, f, ensure_ascii=False, indent=4)

                print(f"Successfully exported {len(records)} records to {filename}")

        except Exception as e:
            print(f"An error occurred: {e}", file=sys.stderr)
            sys.exit(1)


if __name__ == "__main__":
    main()

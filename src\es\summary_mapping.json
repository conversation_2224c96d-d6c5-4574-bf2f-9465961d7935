{"dynamic": "strict", "properties": {"main_category": {"type": "keyword", "index": false}, "sub_category": {"type": "keyword", "index": false}, "summary": {"type": "text", "analyzer": "han_bigrams"}, "year": {"type": "integer", "index": false}, "month": {"type": "integer", "index": false}, "week_in_year": {"type": "integer", "index": false}, "start_date": {"type": "date", "format": "yyyy/MM/dd", "index": false}, "end_date": {"type": "date", "format": "yyyy/MM/dd", "index": false}, "case_count_current_period": {"type": "integer", "index": false}, "case_count_previous_period": {"type": "integer", "index": false}, "case_count_growth_rate": {"type": "float", "index": false}, "case_count_percentage": {"type": "float", "index": false}, "report_name": {"type": "text", "analyzer": "han_bigrams"}, "report_type": {"type": "keyword"}, "created_time": {"type": "date", "format": "yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis", "index": false}, "updated_time": {"type": "date", "format": "yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis", "index": false}}}
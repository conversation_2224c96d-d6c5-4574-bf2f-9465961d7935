# Project Structure

-   **分層組織**：按功能或領域劃分目錄，遵循"關注點分離"原則
-   **命名一致**：使用一致且描述性的目錄和文件命名，反映其用途和內容
-   **模塊化**：相關功能放在同一模塊，減少跨模塊依賴
-   **適當嵌套**：避免過深的目錄嵌套，一般不超過 4 層
-   **資源分類**：區分代碼、資源、配置和測試文件
-   **依賴管理**：集中管理依賴，避免多處聲明
-   **約定優先**：遵循語言或框架的標準項目結構約定

## Root Directory

```
ner-worker/
├── .dockerignore
├── .gitignore
├── Dockerfile_gpt_summarizer
├── Dockerfile_ner_worker
├── poetry.lock
├── pyproject.toml
├── README.md
├── docs/
│   ├── architecture.md
│   ├── bug_tracking.md
│   ├── database.md
│   ├── implementation.md
│   ├── modules.md
│   ├── prd.md
│   └── project_structure.md
├── src/
│   ├── es_create_index.py
│   ├── gpt_summary_run.py
│   ├── ner_worker_run.py
│   ├── settings.py
│   ├── es/
│   ├── repository/
│   └── tools/
└── tests/
    └── ...
```

## Detailed Structure

### `ner-worker/`

The root directory of the Data ETL and NER processing project.

### Root Files

-   **`pyproject.toml` & `poetry.lock`**: Manages Python project dependencies and virtual environment using Poetry. Replaces `requirements.txt`.
-   **`Dockerfile_gpt_summarizer` & `Dockerfile_ner_worker`**: Dockerfiles to build container images for the two main services: the GPT Summarizer and the NER Worker.
-   **`.gitignore` & `.dockerignore`**: Specifies files and directories to be ignored by Git version control and Docker builds, respectively.
-   **`README.md`**: Provides an overview of the project, setup instructions, and usage examples.

### `docs/`

Contains all project documentation, including architecture, implementation plans, and database schemas.

-   **`architecture.md`**: Describes the software architecture, following Clean Architecture principles.
-   **`database.md`**: Details the database schema, particularly for Elasticsearch.
-   **`implementation.md`**: Contains the step-by-step implementation plan and task list.
-   **`project_structure.md`**: This file, outlining the project's directory and file structure.

### `src/`

Contains all the source code for the project's services and libraries.

-   **`ner_worker_run.py`**: The main entry point for the Named Entity Recognition (NER) worker. It processes data and performs NER tasks.
-   **`gpt_summary_run.py`**: The main entry point for the GPT-based text summarization service.
-   **`es_create_index.py`**: A utility script to set up and create indices in Elasticsearch based on defined mappings and settings.
-   **`settings.py`**: Centralized configuration management for the application, likely using Pydantic for type-safe settings.
-   **`helper.py`**: A module for general-purpose helper functions used across the project.

### `src/es/`

This directory holds all Elasticsearch-related configurations.

-   **`.env.dev` & `.env.prod`**: Environment-specific configuration files containing secrets and settings for different deployment environments.
-   **`summary_mapping.json` & `summary_setting.json`**: Defines the schema (mapping) and configuration (settings) for Elasticsearch indices.
-   **`certs/`**: Contains SSL/TLS certificates required for secure connection to Elasticsearch clusters.

### `src/repository/`

The data access layer, abstracting data sources and external services according to the Repository Pattern.

-   **`elasticsearch_repo.py`**: Handles all interactions with Elasticsearch, such as querying, indexing, and updating documents.
-   **`gpt_summary_repo.py`**: A repository specifically for handling data related to the GPT summarization tasks.
-   **`connection_handler.py`**: Manages and centralizes connections to external services like databases or message queues.

### `tests/`

Contains all automated tests for the project, ensuring code quality and correctness.

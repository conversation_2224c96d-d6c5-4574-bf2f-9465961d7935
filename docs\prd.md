# PRD: Category-Based Data Export Mode

-   **Feature Name:** Category Export Mode
-   **Date:** 2025-07-21
-   **Author:** Gemini AI
-   **Status:** Requirements Defined

---

## 1. Introduction & Overview

This document outlines the requirements for a new feature in the `es_export_run.py` script. The goal is to create a new execution mode that exports specific documents from Elasticsearch, organized by category.

The primary problem this feature solves is the need to provide structured, categorized data from our Elasticsearch instance to another downstream application or service. This export process needs to be automated and repeatable, ensuring the consuming service has a reliable data source.

This new mode will read a predefined list of categories, query Elasticsearch for each one, and save the results into a nested directory structure that mirrors the categories.

---

## 2. Goals

-   **G1:** Implement a new execution mode in `es_export_run.py` triggered by a command-line argument.
-   **G2:** Automate the process of querying and exporting documents for a predefined list of main and sub-categories.
-   **G3:** Structure the exported data into a logical file system hierarchy (`main_category/sub_category.json`) for easy consumption by other services.
-   **G4:** Provide a configurable parameter to limit the number of documents per exported file, with a sensible default.
-   **G5:** Ensure the script is robust by handling common error scenarios, such as missing input files or empty search results.

---

## 3. User Stories

-   **As a developer/data engineer,** I want to execute the `es_export_run.py` script with a specific mode flag, so that I can trigger the automated export of categorized data from the "市政信箱" channel.
-   **As a developer/data engineer,** I want the script to organize the exported data into folders by main category and files by sub-category, so that the consuming application can easily locate and process the data it needs.
-   **As a developer/data engineer,** I want to be able to control the maximum number of documents saved per file, so I can manage the size of the exported dataset for testing or performance reasons.

---

## 4. Functional Requirements

| ID       | Requirement                                                                                                                                                                                                                                            |
| -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **FR1**  | The script **MUST** add a new execution mode that is activated by the command-line argument `--mode category_export`.                                                                                                                                  |
| **FR2**  | In this mode, the script **MUST** read the list of categories from the local file path `data/categories_2024.json`.                                                                                                                                    |
| **FR3**  | For each main/sub-category pair in the input file, the script **MUST** perform an Elasticsearch query. The query **MUST** include these three conditions: `record_channel="市政信箱"`, the current `main_category`, and the current `sub_category`.    |
| **FR4**  | The script **MUST** accept an optional integer command-line argument `--max_size` to define the maximum number of documents to retrieve for each sub-category.                                                                                         |
| **FR5**  | If the `--max_size` argument is not provided by the user, its value **MUST** default to `50`.                                                                                                                                                          |
| **FR6**  | The search results for each sub-category **MUST** be saved as a JSON file containing a list of the Elasticsearch documents.                                                                                                                            |
| **FR7**  | The output files **MUST** be saved in a fixed root directory named `output`. The full path structure **MUST** be `output/{main_category}/{sub_category}.json`. The script is responsible for creating these directories.                               |
| **FR8**  | If a search query for a category pair returns zero documents, the script **MUST NOT** create an output file for it. Instead, it **MUST** log a warning message to the console (e.g., `WARN: No documents found for [Main Category] > [Sub Category]`). |
| **FR9**  | If the `output` directory or any of its subdirectories/files already exist from a previous run, the script **MUST** overwrite the existing files and directories silently without requiring user confirmation.                                         |
| **FR10** | If the `data/categories_2024.json` file is not found or contains malformed JSON, the script **MUST** log a clear error message and gracefully terminate the category export process without crashing.                                                  |

---

## 5. Non-Goals (Out of Scope)

-   This feature will **not** have a graphical user interface (GUI). It is purely a command-line interface (CLI) tool.
-   The `record_channel` value ("市政信箱") is fixed and will **not** be a configurable parameter in this version.
-   The script will **not** perform any validation on the content of the exported documents.
-   The output format is JSON only. Other formats like CSV or XML are **not** required.

---

## 6. Technical Considerations

-   **Dependencies:** The implementation will rely on the existing Elasticsearch client/connection module within the project.
-   **Input File Assumption:** The `data/categories_2024.json` file is expected to be a JSON array of objects. Each object must contain string keys named `main_category` and `sub_category`.
    _Example:_
    ```json
    {
    	"交通號誌、標誌、標線及大眾運輸": [
    		"其他",
    		"計程車問題及招呼站設施管理"
    	],
    	"噪音、污染及環境維護": ["營業場所、工廠及施工噪音"]
    }
    ```

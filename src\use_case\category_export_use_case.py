# src/use_case/category_export_use_case.py
from abc import ABC, abstractmethod


class ICategoryExportUseCase(ABC):
    """
    Interface for the category export use case.
    """

    @abstractmethod
    def execute(self, max_size: int) -> None:
        """
        Executes the category export process.

        Args:
            max_size: The maximum number of documents per exported file.
        """
        raise NotImplementedError

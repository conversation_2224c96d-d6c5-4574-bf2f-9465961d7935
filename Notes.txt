[NER_WORKER] 測試站:
gcloud config set project tycg-dashboard
docker build --no-cache --build-arg SSH_PRIVATE_KEY="$(cat ~/.ssh/id_ed25519)" -t asia-east1-docker.pkg.dev/tycg-dashboard/tycg-ner-worker/ner-worker -f Dockerfile_ner_worker .
docker push asia-east1-docker.pkg.dev/tycg-dashboard/tycg-ner-worker/ner-worker
gcloud beta run jobs deploy job-ner-worker --max-retries 0 --image asia-east1-docker.pkg.dev/tycg-dashboard/tycg-ner-worker/ner-worker:latest ^
--tasks=1 --task-timeout 2h ^
--region asia-east1 ^
--network=vpc-001 --subnet=tw-subnet-001 --vpc-egress=all-traffic ^
--cpu=2 --memory=7Gi ^
--service-account <EMAIL> ^
--set-env-vars ES_HOST=********,ES_PORT=9200,ES_DELETE_INDEX=false,TYCG_BE_RUNTIME_ENV=DEV ^
--set-env-vars DATA_API_START_DATE="",DATA_API_END_DATE="",DATA_API_DATE=[] ^
--set-secrets=ES_PASSWORD=projects/************/secrets/ES_PASSWORD:latest,ES_CA_CERT=projects/************/secrets/ES_CA_CERT:latest ^
--set-secrets=LINE_NOTIFY_TOKEN=projects/************/secrets/LINE_NOTIFY_TOKEN:latest ^
--add-volume=name=volume-job-ner-worker,type=cloud-storage,bucket=bucket-job-ner-worker ^
--add-volume-mount=volume=volume-job-ner-worker,mount-path="/home/<USER>"

[GPT Summarizer] 測試站:
gcloud config set project tycg-dashboard
docker build -t asia-east1-docker.pkg.dev/tycg-dashboard/tycg-gpt-summarizer/gpt-summarizer -f Dockerfile_gpt_summarizer .
docker push asia-east1-docker.pkg.dev/tycg-dashboard/tycg-gpt-summarizer/gpt-summarizer
gcloud beta run jobs deploy job-gpt-summarizer --max-retries 0 --image asia-east1-docker.pkg.dev/tycg-dashboard/tycg-gpt-summarizer/gpt-summarizer:latest ^
--region asia-east1 ^
--network=vpc-001 --subnet=tw-subnet-001 --vpc-egress=all-traffic ^
--cpu=1 --memory=1Gi ^
--service-account <EMAIL> ^
--tasks=1 --task-timeout 2h ^
--set-env-vars ES_HOST=********,ES_PORT=9200,ES_DELETE_INDEX=false,TYCG_BE_RUNTIME_ENV=DEV ^
--set-env-vars DATA_API_START_DATE="",DATA_API_END_DATE="" ^
--set-env-vars AZURE_OPENAI_ENDPOINT=https://bigdata-openai-gpt-taoyuan.openai.azure.com/openai/deployments/gpt-4o-mini/chat/completions?api-version=2024-08-01-preview ^
--set-env-vars DATA_API_WEEK=[],DATA_API_CATEGORY={},IS_SKIP_EXIST=True ^
--set-env-vars REPORT_NAME="",REPORT_TYPE="Weekly" ^
--set-secrets=ES_PASSWORD=projects/************/secrets/ES_PASSWORD:latest,ES_CA_CERT=projects/************/secrets/ES_CA_CERT:latest ^
--set-secrets=OPENAI_API_KEY=projects/************/secrets/OPENAI_API_KEY:latest,AZURE_OPENAI_API_KEY=projects/************/secrets/AZURE_OPENAI_API_KEY:latest ^
--set-secrets=GOOGLE_CHAT_WEBHOOK_URL=projects/************/secrets/GOOGLE_CHAT_WEBHOOK_URL:latest ^
--add-volume=name=volume-job-gpt-summarizer,type=cloud-storage,bucket=tycg-test-gpt-summarizer-error-id ^
--add-volume-mount=volume=volume-job-gpt-summarizer,mount-path="/app/mount"

[NER_WORKER] 正式站:
gcloud config set project tycg-dashboard-prod
docker build --build-arg SSH_PRIVATE_KEY="$(cat ~/.ssh/id_ed25519)" -t asia-east1-docker.pkg.dev/tycg-dashboard-prod/tycg-ner-worker/ner-worker -f Dockerfile_ner_worker .
docker push asia-east1-docker.pkg.dev/tycg-dashboard-prod/tycg-ner-worker/ner-worker
gcloud beta run jobs deploy job-ner-worker ^
--max-retries 0 --image asia-east1-docker.pkg.dev/tycg-dashboard-prod/tycg-ner-worker/ner-worker:latest ^
--tasks=1 --task-timeout 2h ^
--region asia-east1 ^
--network=vpc-001 --subnet=tw-subnet-001 --vpc-egress=all-traffic ^
--cpu=2 --memory=7Gi ^
--service-account <EMAIL> ^
--set-env-vars ES_HOST=********,ES_PORT=9200,ES_DELETE_INDEX=false,TYCG_BE_RUNTIME_ENV=PROD ^
--set-env-vars DATA_API_START_DATE="",DATA_API_END_DATE="",DATA_API_DATE=[] ^
--set-secrets=ES_PASSWORD=projects/***********/secrets/ES_PASSWORD:latest,ES_CA_CERT=projects/***********/secrets/ES_CA_CERT:latest ^
--set-secrets=LINE_NOTIFY_TOKEN=projects/***********/secrets/LINE_NOTIFY_TOKEN:latest ^
--add-volume=name=volume-job-ner-worker,type=cloud-storage,bucket=tycg-prod-ner-worker-error-id ^
--add-volume-mount=volume=volume-job-ner-worker,mount-path="/home/<USER>"

[GPT Summarizer] 正式站:
gcloud config set project tycg-dashboard-prod
docker build -t asia-east1-docker.pkg.dev/tycg-dashboard-prod/tycg-gpt-summarizer/gpt-summarizer -f Dockerfile_gpt_summarizer .
docker push asia-east1-docker.pkg.dev/tycg-dashboard-prod/tycg-gpt-summarizer/gpt-summarizer
gcloud beta run jobs deploy job-gpt-summarizer --max-retries 0 --image asia-east1-docker.pkg.dev/tycg-dashboard-prod/tycg-gpt-summarizer/gpt-summarizer:latest ^
--region asia-east1 ^
--network=vpc-001 --subnet=tw-subnet-001 --vpc-egress=all-traffic ^
--cpu=1 --memory=1Gi ^
--service-account <EMAIL> ^
--tasks=1 --task-timeout 2h ^
--set-env-vars ES_HOST=********,ES_PORT=9200,ES_DELETE_INDEX=false,TYCG_BE_RUNTIME_ENV=PROD ^
--set-env-vars DATA_API_START_DATE="",DATA_API_END_DATE="" ^
--set-env-vars AZURE_OPENAI_ENDPOINT=https://bigdata-openai-gpt-taoyuan.openai.azure.com/openai/deployments/gpt-4o-mini/chat/completions?api-version=2024-08-01-preview ^
--set-env-vars DATA_API_WEEK=[],DATA_API_CATEGORY={},IS_SKIP_EXIST=True ^
--set-env-vars REPORT_NAME="",REPORT_TYPE="Weekly" ^
--set-secrets=ES_PASSWORD=projects/***********/secrets/ES_PASSWORD:latest,ES_CA_CERT=projects/***********/secrets/ES_CA_CERT:latest ^
--set-secrets=OPENAI_API_KEY=projects/***********/secrets/OPENAI_API_KEY:latest,AZURE_OPENAI_API_KEY=projects/***********/secrets/AZURE_OPENAI_API_KEY:latest ^
--set-secrets=GOOGLE_CHAT_WEBHOOK_URL=projects/***********/secrets/GOOGLE_CHAT_WEBHOOK_URL:latest ^
--add-volume=name=volume-job-gpt-summarizer,type=cloud-storage,bucket=tycg-prod-gpt-summarizer-error-id ^
--add-volume-mount=volume=volume-job-gpt-summarizer,mount-path="/app/mount"
import argparse
import datetime
import json
import os
import sys
from logging import raiseExceptions

from dotenv import load_dotenv

from helper import initLogger
from repository.gpt_summary_repo import ElasticsearchSummarizer, GPTReportWriter
from tools.utils import send_line_notify


def parse_input_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-s", "--start_date", default=[""], nargs=1, type=str, help="請輸入起始日期"
    )
    parser.add_argument(
        "-e", "--end_date", default=[""], nargs=1, type=str, help="請輸入結束日期"
    )
    args = parser.parse_args()
    logger.info(f"args: {args}")

    dct_input_args = {k: v[0] for k, v in vars(args).items() if v[0]}

    return dct_input_args


def get_env_vars():
    str_start_date = os.getenv("DATA_API_START_DATE", "")
    str_end_date = os.getenv("DATA_API_END_DATE", "")
    lst_fetch_week = json.loads(os.getenv("DATA_API_WEEK", "[]"))  # [48,51]
    dct_fetch_category = json.loads(os.getenv("DATA_API_CATEGORY", "{}"))
    is_skip_exist = os.getenv("IS_SKIP_EXIST", "True") == "True"
    report_name = os.getenv("REPORT_NAME", "")
    report_type = os.getenv("REPORT_TYPE", "Weekly")
    # 新增環境變數，控制是否只更新統計數據而不重新生成 summary
    update_stats_only = os.getenv("UPDATE_STATS_ONLY", "False") == "True"

    return {
        "start_date": str_start_date,
        "end_date": str_end_date,
        "lst_fetch_week": lst_fetch_week,
        "dct_fetch_category": dct_fetch_category,
        "is_skip_exist": is_skip_exist,
        "report_name": report_name,
        "report_type": report_type,
        "update_stats_only": update_stats_only,
    }


def get_start_and_end_date_from_calendar_week(year: str, calendar_week: str):
    """
    Get the start and end dates of a given calendar week.

    Args:
        year (str): The year of the calendar week.
        calendar_week (str): The calendar week number.

    Returns:
        tuple: A tuple containing the start date and end date of the calendar week.
              The start date is in the format "%Y/%m/%d" and the end date is also in the same format.
    """
    monday = datetime.datetime.strptime(f"{year}-{calendar_week}-1", "%G-%V-%w").date()
    return monday.strftime("%Y/%m/%d"), (
        monday + datetime.timedelta(days=6.9)
    ).strftime("%Y/%m/%d")


def calculate_dates_by_start_and_end(
    dct_vars: dict, str_start_date: str, str_end_date: str
):
    lst_fetch_dates = []
    if dct_vars["lst_fetch_week"]:
        for calendar_week in dct_vars["lst_fetch_week"]:
            t_today_date = datetime.datetime.today()
            str_start_date, str_end_date = get_start_and_end_date_from_calendar_week(
                t_today_date.year, calendar_week
            )

            t_today_calendar_date = t_today_date.isocalendar()
            calendar_year = t_today_calendar_date.year
            lst_fetch_dates.append(
                {
                    "str_start_date": str_start_date,
                    "str_end_date": str_end_date,
                    "week_in_year": calendar_week,
                    "year": calendar_year,
                }
            )

    elif str_start_date and str_end_date:
        print("start_date and end_date are provided")
        date_format = "%Y/%m/%d"
        try:
            t_start_date = datetime.datetime.strptime(str_start_date, date_format)
            t_end_date = datetime.datetime.strptime(str_end_date, date_format)
            if t_end_date < t_start_date:
                raise ValueError("End time must be greater than start time")
        except ValueError as e:
            logger.error(f"{e}")
            sys.exit(1)

        start_year = t_start_date.year
        end_year = t_end_date.year

        start_calendar_week = t_start_date.isocalendar().week
        end_calendar_week = t_end_date.isocalendar().week

        for year in range(start_year, end_year + 1):
            if year == start_year:
                start_week = start_calendar_week
            else:
                start_week = 1

            if year == end_year:
                end_week = end_calendar_week
            else:
                end_week = datetime.datetime(year, 12, 31).isocalendar().week
                # Adjust for the case when the last day of the year is part of week 1 of the next year
                if end_week == 1:
                    end_week = datetime.datetime(year, 12, 24).isocalendar().week

            for calendar_week in range(start_week, end_week + 1):
                str_week_start_date, str_week_end_date = (
                    get_start_and_end_date_from_calendar_week(year, calendar_week)
                )

                lst_fetch_dates.append(
                    {
                        "str_start_date": str_week_start_date,
                        "str_end_date": str_week_end_date,
                        "week_in_year": calendar_week,
                        "year": t_start_date.isocalendar().year,
                    }
                )

    else:  # not str_start_date or not str_end_date
        print("start_date and end_date are not found. Use last week as default.")
        tz = datetime.timezone(datetime.timedelta(hours=+8))
        t_now = datetime.datetime.now(tz)
        print(f"t_now: {t_now}")
        t_last_week_date = t_now - datetime.timedelta(days=7)
        t_last_week_calendar_date = t_last_week_date.isocalendar()
        calendar_week = t_last_week_calendar_date.week
        calendar_year = t_last_week_calendar_date.year
        str_start_date = datetime.date.fromisocalendar(
            calendar_year, calendar_week, 1
        ).strftime("%Y/%m/%d")
        str_end_date = datetime.date.fromisocalendar(
            calendar_year, calendar_week, 7
        ).strftime("%Y/%m/%d")
        # str_start_date, str_end_date = get_start_and_end_date_from_calendar_week(
        #     t_last_week_date.year, calendar_week
        # )
        # week_in_month = week_number_of_month(t_last_week_date)
        lst_fetch_dates.append(
            {
                "str_start_date": str_start_date,
                "str_end_date": str_end_date,
                "week_in_year": calendar_week,
                "year": calendar_year,
            }
        )

    return lst_fetch_dates


if __name__ == "__main__":
    try:
        # ================== initialize logger ====================
        logDirPath = "logs"
        logger = initLogger(logDirPath, "DEBUG")
        logger.info("logger being initialized successfully")

        # ================== Parse argument ====================
        load_dotenv(".env")

        RUNTIME_ENV = os.environ["TYCG_BE_RUNTIME_ENV"]
        if RUNTIME_ENV == "PROD":
            load_dotenv(os.path.join("es", ".env.prod"))
        elif RUNTIME_ENV == "DEV":
            load_dotenv(os.path.join("es", ".env.dev"))

        dct_vars = get_env_vars()
        dct_input_args = parse_input_args()
        dct_vars.update(dct_input_args)

        str_start_date = dct_vars["start_date"]
        str_end_date = dct_vars["end_date"]

        lst_fetch_dates = []

        lst_fetch_dates = calculate_dates_by_start_and_end(
            dct_vars, str_start_date, str_end_date
        )

        logger.info(f"lst_fetch_dates:{lst_fetch_dates}")
        gpt_report_writer = GPTReportWriter()
        # ================== Initialize ES repository ====================
        es_summarizer = ElasticsearchSummarizer()

        lst_total_error_doc_id = []
        n_docs_success = 0
        n_docs_error = 0
        n_docs_total = 0
        # ================== Run GPT summarizer ====================
        for dct_date in lst_fetch_dates:
            str_start_date = dct_date["str_start_date"]
            str_end_date = dct_date["str_end_date"]

            t_start_date = datetime.datetime.strptime(str_start_date, "%Y/%m/%d")
            str_now = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
            year = dct_date["year"]
            month = t_start_date.month if dct_date["week_in_year"] > 1 else 1

            n_total_case_count = es_summarizer.get_total_count_by_index(
                str_start_date, str_end_date
            )
            logger.info(
                f"[{str_start_date}]~[{str_end_date}] total_case_count:{n_total_case_count}"
            )

            # Calculate the start and end date of the previous period
            n_curr_year = int(str_start_date.split("/")[0])
            n_prev_year = n_curr_year
            n_prev_calendar_week = dct_date["week_in_year"] - 1
            if n_prev_calendar_week == 0:
                n_prev_calendar_week = 52
                n_prev_year -= 1

            str_prev_start_date, str_prev_end_date = (
                get_start_and_end_date_from_calendar_week(
                    n_prev_year, n_prev_calendar_week
                )
            )
            logger.debug(
                f"dct_date['week_in_year']:{dct_date['week_in_year']}, n_prev_calendar_week: [{n_prev_calendar_week}]"
            )
            logger.debug(f"Current period: [{str_start_date}]~[{str_end_date}]")
            logger.debug(
                f"Previous period: [{str_prev_start_date}]~[{str_prev_end_date}]"
            )

            # Get main/sub category group by date range
            if not dct_vars["dct_fetch_category"]:
                dct_category = es_summarizer.get_category_group(
                    str_start_date, str_end_date
                )
            else:
                dct_category = dct_vars["dct_fetch_category"]
                logger.info(f"Manually setting dct_category:{dct_category}")

            if not dct_category:
                logger.warning(
                    f"NO DATA in date range: {str_start_date} - {str_end_date}"
                )
                continue

            lst_summaries = []
            for main_idx, (str_main_category, lst_sub_categories) in enumerate(
                dct_category.items()
            ):
                # if main_idx == 0 or main_idx == 1:
                #     continue
                for sub_idx, str_sub_category in enumerate(lst_sub_categories):
                    # str_main_category = "交通號誌、標誌、標線及大眾運輸"
                    # str_sub_category = "公車問題及站牌、候車亭設施管理"

                    if dct_vars[
                        "is_skip_exist"
                    ] and es_summarizer.check_if_summary_exists(
                        str_main_category=str_main_category,
                        str_sub_category=str_sub_category,
                        year=year,
                        month=month,
                        week_in_year=dct_date["week_in_year"],
                    ):
                        logger.info(
                            f"Summary already exists for [{str_main_category}][{str_sub_category}][{year}/{month}/W{dct_date['week_in_year']}] Skipping..."
                        )
                        continue

                    # Prepare the list of queries for msearch to calculate the case count
                    curr_calc_case_count_alias_name = (
                        es_summarizer.get_petition_case_alias(n_curr_year)
                    )
                    prev_calc_case_count_alias_name = (
                        es_summarizer.get_petition_case_alias(n_prev_year)
                    )

                    lst_query_func_params = [
                        {
                            "alias": curr_calc_case_count_alias_name,
                            "func": es_summarizer.get_case_count_by_category_group,
                            "params": {
                                "str_start_time": str_start_date,
                                "str_end_time": str_end_date,
                                # "dct_category": {str_main_category: [str_sub_category]},
                                "dct_terms_fields": {
                                    "main_category": str_main_category,
                                    "sub_category": [str_sub_category],
                                },
                                "is_return_query_only": True,
                            },
                            "name": "get_case_count_for_current_period",
                        },
                        {
                            "alias": prev_calc_case_count_alias_name,
                            "func": es_summarizer.get_case_count_by_category_group,
                            "params": {
                                "str_start_time": str_prev_start_date,
                                "str_end_time": str_prev_end_date,
                                # "dct_category": {str_main_category: [str_sub_category]},
                                "dct_terms_fields": {
                                    "main_category": str_main_category,
                                    "sub_category": [str_sub_category],
                                },
                                "is_return_query_only": True,
                            },
                            "name": "get_case_count_for_previous_period",
                        },
                    ]

                    # Generate the list of queries for msearch
                    lst_query = [
                        {
                            "query": dct_item["func"](**dct_item["params"]),
                            "alias": dct_item["alias"],
                        }
                        for dct_item in lst_query_func_params
                    ]

                    # Execute the msearch API call
                    dct_msearch_response = es_summarizer.msearch(lst_query)

                    # Map the response names to their corresponding indexes
                    dct_organized_response = {
                        dct_item["name"]: dct_item["func"](
                            dct_search_result=dct_msearch_response["responses"][idx]
                        )
                        for idx, dct_item in enumerate(lst_query_func_params)
                    }
                    n_case_count_current_period = dct_organized_response[
                        "get_case_count_for_current_period"
                    ]
                    n_case_count_previous_period = dct_organized_response[
                        "get_case_count_for_previous_period"
                    ]

                    logger.info(
                        f"[{str_main_category}][{str_sub_category}][{year}/{month}/W{dct_date['week_in_year']}]: n_case_count_current_period:{n_case_count_current_period}, n_case_count_previous_period:{n_case_count_previous_period}"
                    )

                    # ================ GPT Summarize =================
                    lst_final_summary = None

                    # 只有在不是僅更新統計數據時才生成 summary
                    if not dct_vars["update_stats_only"]:
                        str_gpt_directory = os.path.join(
                            "gpt_summary",
                            str(year),
                            f"W{dct_date['week_in_year']}",
                            str_main_category,
                            str_sub_category,
                        )
                        if not os.path.exists(str_gpt_directory):
                            os.makedirs(str_gpt_directory)

                        gpt_report_writer.reset_tokens()
                        lst_documents = (
                            es_summarizer.retrieve_all_documents_by_category(
                                str_start_date,
                                str_end_date,
                                str_main_category,
                                str_sub_category,
                            )
                        )

                        str_subject = f"{str_main_category}/{str_sub_category}"

                        logger.info(
                            f"[{str_main_category}][{str_sub_category}][{year}/{month}/W{dct_date['week_in_year']}] Summarizing [{len(lst_documents)}] docs..."
                        )
                        lst_final_summary = gpt_report_writer.summarize_documents(
                            lst_documents,
                            str_subject,
                            str_gpt_directory=str_gpt_directory,
                        )
                        gpt_report_writer.set_tokens(
                            str_main_category, str_sub_category
                        )
                        logger.info(
                            f"Accumulated completion_tokens: {gpt_report_writer.curr_token_recoder.completion_tokens}, prompt_tokens: {gpt_report_writer.curr_token_recoder.prompt_tokens}, total_tokens: {gpt_report_writer.curr_token_recoder.total_tokens}, raw_data_characters: {gpt_report_writer.curr_token_recoder.raw_data_characters}"
                        )
                    else:
                        logger.info(
                            f"[{str_main_category}][{str_sub_category}][{year}/{month}/W{dct_date['week_in_year']}] Skipping summary generation (update_stats_only=True)"
                        )

                    str_es_doc_id = es_summarizer.generate_id(
                        **{
                            "str_main_category": str_main_category,
                            "str_sub_category": str_sub_category,
                            "year": year,
                            "month": month,
                            "week_in_year": dct_date["week_in_year"],
                        }
                    )

                    index_name = es_summarizer.get_summary_index_name(year)
                    alias_name = es_summarizer.get_summary_alias(year)

                    # 建立基本的 document 結構
                    dct_es_doc = {
                        "index_name": index_name,
                        "alias_name": alias_name,
                        "_id": str_es_doc_id,
                        "main_category": str_main_category,
                        "sub_category": str_sub_category,
                        "year": int(year),
                        "month": int(month),
                        "week_in_year": int(dct_date["week_in_year"]),
                        # "week_in_month": int(dct_date["week_in_month"]),
                        "start_date": dct_date["str_start_date"],
                        "end_date": dct_date["str_end_date"],
                        "case_count_current_period": n_case_count_current_period,
                        "case_count_previous_period": n_case_count_previous_period,
                        "case_count_growth_rate": (
                            round(
                                (
                                    n_case_count_current_period
                                    - n_case_count_previous_period
                                )
                                / n_case_count_previous_period,
                                3,
                            )
                            if n_case_count_previous_period > 0
                            else 100
                        ),
                        "case_count_percentage": (
                            round(n_case_count_current_period / n_total_case_count, 3)
                            if n_total_case_count > 0
                            else 0
                        ),
                        "report_name": (
                            dct_vars["report_name"]
                            if dct_vars["report_name"]
                            else f"{year}-{month}-W{dct_date['week_in_year']}"
                        ),
                        "report_type": dct_vars["report_type"],
                        "created_time": str_now,
                        "updated_time": str_now,
                    }

                    # 只有在不是僅更新統計數據時才添加 summary 欄位
                    if (
                        not dct_vars["update_stats_only"]
                        and lst_final_summary is not None
                    ):
                        dct_es_doc["summary"] = lst_final_summary

                    lst_summaries.append(dct_es_doc)

                    # if sub_idx == 2:
                    #     break
                # if main_idx == 1:
                #     break

            if lst_summaries:
                dct_bulk_response = es_summarizer.bulk_operation(
                    lst_summaries, id_field="_id", op_type="update"
                )

                for k, v in dct_bulk_response.items():
                    if dct_bulk_response["lst_error_doc_id"]:
                        lst_total_error_doc_id.append(
                            dct_bulk_response["lst_error_doc_id"]
                        )

                n_docs_success += dct_bulk_response["n_docs_success"]
                n_docs_error += dct_bulk_response["n_docs_error"]
                n_docs_total += dct_bulk_response["n_docs_total"]

        f_success_rate = (n_docs_success / n_docs_total) * 100 if n_docs_total else 0
        f_error_rate = (n_docs_error / n_docs_total) * 100 if n_docs_total else 0

        logger.info("********************************************************")
        logger.info(
            f"Total:[{n_docs_total}] / Success:[{n_docs_success}] / Error:[{n_docs_error}] / Success_rate:[{f_success_rate:.2f}%] / Error_rate:[{f_error_rate:.2f}%]"
        )
        logger.info(f"es_summarizer.set_error_date: {es_summarizer.set_error_date}")
        logger.info(f"lst_total_error_doc_id: {lst_total_error_doc_id}")
        if lst_total_error_doc_id:
            str_dir = "mount"
            tz = datetime.timezone(datetime.timedelta(hours=+8))
            t_current_datetime = datetime.datetime.now(tz)
            str_current_datetime = t_current_datetime.strftime("%Y-%m-%d_%H:%M:%S")
            with open(
                os.path.join(str_dir, f"error_doc_id_{str_current_datetime}.json"),
                "w",
                encoding="utf8",
            ) as outfile:
                json.dump(
                    {
                        "set_error_date": es_summarizer.set_error_date,
                        "lst_total_error_doc_id": lst_total_error_doc_id,
                    },
                    outfile,
                    ensure_ascii=False,
                    indent=4,
                )

        logger.info("Done")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {repr(e)}")
        str_line_notify_message = f"""
    Data Collector 🔄
    start_date: [{str_start_date}]
    end_date: [{str_end_date}]
    fetch_date: {dct_vars['lst_fetch_dates']}
    --------------------------
    Exception: {repr(e)}
    """
        send_line_notify(
            message=str_line_notify_message,
            is_success=False,
            environment=os.environ["TYCG_BE_RUNTIME_ENV"],
        )
        sys.exit(1)
    else:
        is_success = (
            False
            if lst_total_error_doc_id
            or es_summarizer.set_error_date
            or n_docs_error > 0
            else True
        )
        str_line_notify_message = f"""
    GPT Summarizer 📝
    start_date: [{str_start_date}]
    end_date: [{str_end_date}]
    fetch_date: {lst_fetch_dates}
    --------------------------
    Total: [{n_docs_total}]
    Success: [{n_docs_success}]
    Error: [{n_docs_error}]
    Success_rate: [{f_success_rate:.2f}%]
    Error_rate: [{f_error_rate:.2f}%]
    --------------------------
    lst_error_summary: {gpt_report_writer.lst_error_summary}
    lst_error_date: {es_summarizer.set_error_date},
    lst_total_error_doc_id: {lst_total_error_doc_id},
    """
        send_line_notify(
            message=str_line_notify_message,
            is_success=is_success,
            environment=os.environ["TYCG_BE_RUNTIME_ENV"],
        )

{"analysis": {"analyzer": {"lowercase_space_analyzer": {"type": "custom", "tokenizer": "whitespace", "filter": ["lowercase"]}, "han_bigrams": {"type": "custom", "tokenizer": "uax_url_email", "char_filter": ["html_strip", "icu_normalizer", "punctuation_spacing", "custom_char_filter"], "filter": ["han_bigrams_filter", "lowercase", "cjk_width"]}}, "char_filter": {"punctuation_spacing": {"type": "pattern_replace", "pattern": "([%！？｡。,，、；;：:\"'’‘“”〝〞()〈〉{}［］〔〕「」『』＜＞〖〗〘〙〚〛…—～－—–・·˙《》｛｝︰︱︳︴︵︶︷︸︹︺︻︼︽︾︿﹀﹁﹂！‼♡＊❤❥❦❧♡❣♫♪♩♬♭♪の☆→†‡・·※☆⇒〓△▼▽◆◇○●◎◯Δ◕◔￥〄々‿–—＊＆＠§※☆★＃〉《〔〕［］｛｝︵︷︹︻︽︿︥/]|\\\\n|\\n|😀|😃|😄|😁|😆|😅|🤣|😂|🙂|🙃|😉|😊|😇|🥰|😍|🤩|😘|😗|☺️|☺|😚|😙|🥲|😋|😛|😜|🤪|😝|🤑|🤗|🤭|🤫|🤔|🤐|🤨|😐|😑|😶|😏|😒|🙄|😬|🤥|😌|😔|😪|🤤|😴|😷|🤒|🤕|🤢|🤮|🤧|🥵|🥶|🥴|😵|🤯|🤠|🥳|🥸|😎|🤓|🧐|😕|😟|🙁|☹️|☹|😮|😯|😲|😳|🥺|😦|😧|😨|😰|😥|😢|😭|😱|😖|😣|😞|😓|😩|😫|🥱|😤|😡|😠|🤬|😈|👿|💀|☠️|☠|💩|🤡|👹|👺|👻|👽|👾|🤖|😺|😸|😹|😻|😼|😽|🙀|😿|😾|🙈|🙉|🙊|💋|💌|💘|💝|💖|💗|💓|💞|💕|💟|❣️|❣|💔|❤️|❤|🧡|💛|💚|💙|💜|🤎|🖤|🤍|💯|💢|💥|💫|💦|💨|🕳️|🕳|💣|💬|👁️‍🗨️|👁‍🗨️|👁️‍🗨|👁‍🗨|🗨️|🗨|🗯️|🗯|💭|💤|👋|👋🏻|👋🏼|👋🏽|👋🏾|👋🏿|🤚|🤚🏻|🤚🏼|🤚🏽|🤚🏾|🤚🏿|🖐️|🖐|🖐🏻|🖐🏼|🖐🏽|🖐🏾|🖐🏿|✋|✋🏻|✋🏼|✋🏽|✋🏾|✋🏿|🖖|🖖🏻|🖖🏼|🖖🏽|🖖🏾|🖖🏿|👌|👌🏻|👌🏼|👌🏽|👌🏾|👌🏿|🤌|🤌🏻|🤌🏼|🤌🏽|🤌🏾|🤌🏿|🤏|🤏🏻|🤏🏼|🤏🏽|🤏🏾|🤏🏿|✌️|✌|✌🏻|✌🏼|✌🏽|✌🏾|✌🏿|🤞|🤞🏻|🤞🏼|🤞🏽|🤞🏾|🤞🏿|🤟|🤟🏻|🤟🏼|🤟🏽|🤟🏾|🤟🏿|🤘|🤘🏻|🤘🏼|🤘🏽|🤘🏾|🤘🏿|🤙|🤙🏻|🤙🏼|🤙🏽|🤙🏾|🤙🏿|👈|👈🏻|👈🏼|👈🏽|👈🏾|👈🏿|👉|👉🏻|👉🏼|👉🏽|👉🏾|👉🏿|👆|👆🏻|👆🏼|👆🏽|👆🏾|👆🏿|🖕|🖕🏻|🖕🏼|🖕🏽|🖕🏾|🖕🏿|👇|👇🏻|👇🏼|👇🏽|👇🏾|👇🏿|☝️|☝|☝🏻|☝🏼|☝🏽|☝🏾|☝🏿|👍|👍🏻|👍🏼|👍🏽|👍🏾|👍🏿|👎|👎🏻|👎🏼|👎🏽|👎🏾|👎🏿|✊|✊🏻|✊🏼|✊🏽|✊🏾|✊🏿|👊|👊🏻|👊🏼|👊🏽|👊🏾|👊🏿|🤛|🤛🏻|🤛🏼|🤛🏽|🤛🏾|🤛🏿|🤜|🤜🏻|🤜🏼|🤜🏽|🤜🏾|🤜🏿|👏|👏🏻|👏🏼|👏🏽|👏🏾|👏🏿|🙌|🙌🏻|🙌🏼|🙌🏽|🙌🏾|🙌🏿|👐|👐🏻|👐🏼|👐🏽|👐🏾|👐🏿|🤲|🤲🏻|🤲🏼|🤲🏽|🤲🏾|🤲🏿|🤝|🙏|🙏🏻|🙏🏼|🙏🏽|🙏🏾|🙏🏿|✍️|✍|✍🏻|✍🏼|✍🏽|✍🏾|✍🏿|💅|💅🏻|💅🏼|💅🏽|💅🏾|💅🏿|🤳|🤳🏻|🤳🏼|🤳🏽|🤳🏾|🤳🏿|💪|💪🏻|💪🏼|💪🏽|💪🏾|💪🏿|🦾|🦿|🦵|🦵🏻|🦵🏼|🦵🏽|🦵🏾|🦵🏿|🦶|🦶🏻|🦶🏼|🦶🏽|🦶🏾|🦶🏿|👂|👂🏻|👂🏼|👂🏽|👂🏾|👂🏿|🦻|🦻🏻|🦻🏼|🦻🏽|🦻🏾|🦻🏿|👃|👃🏻|👃🏼|👃🏽|👃🏾|👃🏿|🧠|🫀|🫁|🦷|🦴|👀|👁️|👁|👅|👄|👶|👶🏻|👶🏼|👶🏽|👶🏾|👶🏿|🧒|🧒🏻|🧒🏼|🧒🏽|🧒🏾|🧒🏿|👦|👦🏻|👦🏼|👦🏽|👦🏾|👦🏿|👧|👧🏻|👧🏼|👧🏽|👧🏾|👧🏿|🧑|🧑🏻|🧑🏼|🧑🏽|🧑🏾|🧑🏿|👱|👱🏻|👱🏼|👱🏽|👱🏾|👱🏿|👨|👨🏻|👨🏼|👨🏽|👨🏾|👨🏿|🧔|🧔🏻|🧔🏼|🧔🏽|🧔🏾|🧔🏿|👨‍🦰|👨🏻‍🦰|👨🏼‍🦰|👨🏽‍🦰|👨🏾‍🦰|👨🏿‍🦰|👨‍🦱|👨🏻‍🦱|👨🏼‍🦱|👨🏽‍🦱|👨🏾‍🦱|👨🏿‍🦱|👨‍🦳|👨🏻‍🦳|👨🏼‍🦳|👨🏽‍🦳|👨🏾‍🦳|👨🏿‍🦳|👨‍🦲|👨🏻‍🦲|👨🏼‍🦲|👨🏽‍🦲|👨🏾‍🦲|👨🏿‍🦲|👩|👩🏻|👩🏼|👩🏽|👩🏾|👩🏿|👩‍🦰|👩🏻‍🦰|👩🏼‍🦰|👩🏽‍🦰|👩🏾‍🦰|👩🏿‍🦰|🧑‍🦰|🧑🏻‍🦰|🧑🏼‍🦰|🧑🏽‍🦰|🧑🏾‍🦰|🧑🏿‍🦰|👩‍🦱|👩🏻‍🦱|👩🏼‍🦱|👩🏽‍🦱|👩🏾‍🦱|👩🏿‍🦱|🧑‍🦱|🧑🏻‍🦱|🧑🏼‍🦱|🧑🏽‍🦱|🧑🏾‍🦱|🧑🏿‍🦱|👩‍🦳|👩🏻‍🦳|👩🏼‍🦳|👩🏽‍🦳|👩🏾‍🦳|👩🏿‍🦳|🧑‍🦳|🧑🏻‍🦳|🧑🏼‍🦳|🧑🏽‍🦳|🧑🏾‍🦳|🧑🏿‍🦳|👩‍🦲|👩🏻‍🦲|👩🏼‍🦲|👩🏽‍🦲|👩🏾‍🦲|👩🏿‍🦲|🧑‍🦲|🧑🏻‍🦲|🧑🏼‍🦲|🧑🏽‍🦲|🧑🏾‍🦲|🧑🏿‍🦲|👱‍♀️|👱🏻‍♀️|👱🏼‍♀️|👱🏽‍♀️|👱🏾‍♀️|👱🏿‍♀️|👱‍♂️|👱🏻‍♂️|👱🏼‍♂️|👱🏽‍♂️|👱🏾‍♂️|👱🏿‍♂️|🧓|🧓🏻|🧓🏼|🧓🏽|🧓🏾|🧓🏿|👴|👴🏻|👴🏼|👴🏽|👴🏾|👴🏿|👵|👵🏻|👵🏼|👵🏽|👵🏾|👵🏿|🙍|🙍🏻|🙍🏼|🙍🏽|🙍🏾|🙍🏿|🙍‍♂️|🙍🏻‍♂️|🙍🏼‍♂️|🙍🏽‍♂️|🙍🏾‍♂️|🙍🏿‍♂️|🙍‍♀️|🙍🏻‍♀️|🙍🏼‍♀️|🙍🏽‍♀️|🙍🏾‍♀️|🙍🏿‍♀️|🙎|🙎🏻|🙎🏼|🙎🏽|🙎🏾|🙎🏿|🙎‍♂️|🙎🏻‍♂️|🙎🏼‍♂️|🙎🏽‍♂️|🙎🏾‍♂️|🙎🏿‍♂️|🙎‍♀️|🙎🏻‍♀️|🙎🏼‍♀️|🙎🏽‍♀️|🙎🏾‍♀️|🙎🏿‍♀️|🙅|🙅🏻|🙅🏼|🙅🏽|🙅🏾|🙅🏿|🙅‍♂️|🙅🏻‍♂️|🙅🏼‍♂️|🙅🏽‍♂️|🙅🏾‍♂️|🙅🏿‍♂️|🙅‍♀️|🙅🏻‍♀️|🙅🏼‍♀️|🙅🏽‍♀️|🙅🏾‍♀️|🙅🏿‍♀️|🙆|🙆🏻|🙆🏼|🙆🏽|🙆🏾|🙆🏿|🙆‍♂️|🙆🏻‍♂️|🙆🏼‍♂️|🙆🏽‍♂️|🙆🏾‍♂️|🙆🏿‍♂️|🙆‍♀️|🙆🏻‍♀️|🙆🏼‍♀️|🙆🏽‍♀️|🙆🏾‍♀️|🙆🏿‍♀️|💁|💁🏻|💁🏼|💁🏽|💁🏾|💁🏿|💁‍♂️|💁🏻‍♂️|💁🏼‍♂️|💁🏽‍♂️|💁🏾‍♂️|💁🏿‍♂️|💁‍♀️|💁🏻‍♀️|💁🏼‍♀️|💁🏽‍♀️|💁🏾‍♀️|💁🏿‍♀️|🙋|🙋🏻|🙋🏼|🙋🏽|🙋🏾|🙋🏿|🙋‍♂️|🙋🏻‍♂️|🙋🏼‍♂️|🙋🏽‍♂️|🙋🏾‍♂️|🙋🏿‍♂️|🙋‍♀️|🙋🏻‍♀️|🙋🏼‍♀️|🙋🏽‍♀️|🙋🏾‍♀️|🙋🏿‍♀️|🧏|🧏🏻|🧏🏼|🧏🏽|🧏🏾|🧏🏿|🧏‍♂️|🧏🏻‍♂️|🧏🏼‍♂️|🧏🏽‍♂️|🧏🏾‍♂️|🧏🏿‍♂️|🧏‍♀️|🧏🏻‍♀️|🧏🏼‍♀️|🧏🏽‍♀️|🧏🏾‍♀️|🧏🏿‍♀️|🙇|🙇🏻|🙇🏼|🙇🏽|🙇🏾|🙇🏿|🙇‍♂️|🙇🏻‍♂️|🙇🏼‍♂️|🙇🏽‍♂️|🙇🏾‍♂️|🙇🏿‍♂️|🙇‍♀️|🙇🏻‍♀️|🙇🏼‍♀️|🙇🏽‍♀️|🙇🏾‍♀️|🙇🏿‍♀️|🤦|🤦🏻|🤦🏼|🤦🏽|🤦🏾|🤦🏿|🤦‍♂️|🤦🏻‍♂️|🤦🏼‍♂️|🤦🏽‍♂️|🤦🏾‍♂️|🤦🏿‍♂️|🤦‍♀️|🤦🏻‍♀️|🤦🏼‍♀️|🤦🏽‍♀️|🤦🏾‍♀️|🤦🏿‍♀️|🤷|🤷🏻|🤷🏼|🤷🏽|🤷🏾|🤷🏿|🤷‍♂️|🤷🏻‍♂️|🤷🏼‍♂️|🤷🏽‍♂️|🤷🏾‍♂️|🤷🏿‍♂️|🤷‍♀️|🤷🏻‍♀️|🤷🏼‍♀️|🤷🏽‍♀️|🤷🏾‍♀️|🤷🏿‍♀️|🧑‍⚕️|🧑🏻‍⚕️|🧑🏼‍⚕️|🧑🏽‍⚕️|🧑🏾‍⚕️|🧑🏿‍⚕️|👨‍⚕️|👨🏻‍⚕️|👨🏼‍⚕️|👨🏽‍⚕️|👨🏾‍⚕️|👨🏿‍⚕️|👩‍⚕️|👩🏻‍⚕️|👩🏼‍⚕️|👩🏽‍⚕️|👩🏾‍⚕️|👩🏿‍⚕️|🧑‍🎓|🧑🏻‍🎓|🧑🏼‍🎓|🧑🏽‍🎓|🧑🏾‍🎓|🧑🏿‍🎓|👨‍🎓|👨🏻‍🎓|👨🏼‍🎓|👨🏽‍🎓|👨🏾‍🎓|👨🏿‍🎓|👩‍🎓|👩🏻‍🎓|👩🏼‍🎓|👩🏽‍🎓|👩🏾‍🎓|👩🏿‍🎓|🧑‍🏫|🧑🏻‍🏫|🧑🏼‍🏫|🧑🏽‍🏫|🧑🏾‍🏫|🧑🏿‍🏫|👨‍🏫|👨🏻‍🏫|👨🏼‍🏫|👨🏽‍🏫|👨🏾‍🏫|👨🏿‍🏫|👩‍🏫|👩🏻‍🏫|👩🏼‍🏫|👩🏽‍🏫|👩🏾‍🏫|👩🏿‍🏫|🧑‍⚖️|🧑🏻‍⚖️|🧑🏼‍⚖️|🧑🏽‍⚖️|🧑🏾‍⚖️|🧑🏿‍⚖️|👨‍⚖️|👨🏻‍⚖️|👨🏼‍⚖️|👨🏽‍⚖️|👨🏾‍⚖️|👨🏿‍⚖️|👩‍⚖️|👩🏻‍⚖️|👩🏼‍⚖️|👩🏽‍⚖️|👩🏾‍⚖️|👩🏿‍⚖️|🧑‍🌾|🧑🏻‍🌾|🧑🏼‍🌾|🧑🏽‍🌾|🧑🏾‍🌾|🧑🏿‍🌾|👨‍🌾|👨🏻‍🌾|👨🏼‍🌾|👨🏽‍🌾|👨🏾‍🌾|👨🏿‍🌾|👩‍🌾|👩🏻‍🌾|👩🏼‍🌾|👩🏽‍🌾|👩🏾‍🌾|👩🏿‍🌾|🧑‍🍳|🧑🏻‍🍳|🧑🏼‍🍳|🧑🏽‍🍳|🧑🏾‍🍳|🧑🏿‍🍳|👨‍🍳|👨🏻‍🍳|👨🏼‍🍳|👨🏽‍🍳|👨🏾‍🍳|👨🏿‍🍳|👩‍🍳|👩🏻‍🍳|👩🏼‍🍳|👩🏽‍🍳|👩🏾‍🍳|👩🏿‍🍳|🧑‍🔧|🧑🏻‍🔧|🧑🏼‍🔧|🧑🏽‍🔧|🧑🏾‍🔧|🧑🏿‍🔧|👨‍🔧|👨🏻‍🔧|👨🏼‍🔧|👨🏽‍🔧|👨🏾‍🔧|👨🏿‍🔧|👩‍🔧|👩🏻‍🔧|👩🏼‍🔧|👩🏽‍🔧|👩🏾‍🔧|👩🏿‍🔧|🧑‍🏭|🧑🏻‍🏭|🧑🏼‍🏭|🧑🏽‍🏭|🧑🏾‍🏭|🧑🏿‍🏭|👨‍🏭|👨🏻‍🏭|👨🏼‍🏭|👨🏽‍🏭|👨🏾‍🏭|👨🏿‍🏭|👩‍🏭|👩🏻‍🏭|👩🏼‍🏭|👩🏽‍🏭|👩🏾‍🏭|👩🏿‍🏭|🧑‍💼|🧑🏻‍💼|🧑🏼‍💼|🧑🏽‍💼|🧑🏾‍💼|🧑🏿‍💼|👨‍💼|👨🏻‍💼|👨🏼‍💼|👨🏽‍💼|👨🏾‍💼|👨🏿‍💼|👩‍💼|👩🏻‍💼|👩🏼‍💼|👩🏽‍💼|👩🏾‍💼|👩🏿‍💼|🧑‍🔬|🧑🏻‍🔬|🧑🏼‍🔬|🧑🏽‍🔬|🧑🏾‍🔬|🧑🏿‍🔬|👨‍🔬|👨🏻‍🔬|👨🏼‍🔬|👨🏽‍🔬|👨🏾‍🔬|👨🏿‍🔬|👩‍🔬|👩🏻‍🔬|👩🏼‍🔬|👩🏽‍🔬|👩🏾‍🔬|👩🏿‍🔬|🧑‍💻|🧑🏻‍💻|🧑🏼‍💻|🧑🏽‍💻|🧑🏾‍💻|🧑🏿‍💻|👨‍💻|👨🏻‍💻|👨🏼‍💻|👨🏽‍💻|👨🏾‍💻|👨🏿‍💻|👩‍💻|👩🏻‍💻|👩🏼‍💻|👩🏽‍💻|👩🏾‍💻|👩🏿‍💻|🧑‍🎤|🧑🏻‍🎤|🧑🏼‍🎤|🧑🏽‍🎤|🧑🏾‍🎤|🧑🏿‍🎤|👨‍🎤|👨🏻‍🎤|👨🏼‍🎤|👨🏽‍🎤|👨🏾‍🎤|👨🏿‍🎤|👩‍🎤|👩🏻‍🎤|👩🏼‍🎤|👩🏽‍🎤|👩🏾‍🎤|👩🏿‍🎤|🧑‍🎨|🧑🏻‍🎨|🧑🏼‍🎨|🧑🏽‍🎨|🧑🏾‍🎨|🧑🏿‍🎨|👨‍🎨|👨🏻‍🎨|👨🏼‍🎨|👨🏽‍🎨|👨🏾‍🎨|👨🏿‍🎨|👩‍🎨|👩🏻‍🎨|👩🏼‍🎨|👩🏽‍🎨|👩🏾‍🎨|👩🏿‍🎨|🧑‍✈️|🧑🏻‍✈️|🧑🏼‍✈️|🧑🏽‍✈️|🧑🏾‍✈️|🧑🏿‍✈️|👨‍✈️|👨🏻‍✈️|👨🏼‍✈️|👨🏽‍✈️|👨🏾‍✈️|👨🏿‍✈️|👩‍✈️|👩🏻‍✈️|👩🏼‍✈️|👩🏽‍✈️|👩🏾‍✈️|👩🏿‍✈️|🧑‍🚀|🧑🏻‍🚀|🧑🏼‍🚀|🧑🏽‍🚀|🧑🏾‍🚀|🧑🏿‍🚀|👨‍🚀|👨🏻‍🚀|👨🏼‍🚀|👨🏽‍🚀|👨🏾‍🚀|👨🏿‍🚀|👩‍🚀|👩🏻‍🚀|👩🏼‍🚀|👩🏽‍🚀|👩🏾‍🚀|👩🏿‍🚀|🧑‍🚒|🧑🏻‍🚒|🧑🏼‍🚒|🧑🏽‍🚒|🧑🏾‍🚒|🧑🏿‍🚒|👨‍🚒|👨🏻‍🚒|👨🏼‍🚒|👨🏽‍🚒|👨🏾‍🚒|👨🏿‍🚒|👩‍🚒|👩🏻‍🚒|👩🏼‍🚒|👩🏽‍🚒|👩🏾‍🚒|👩🏿‍🚒|👮|👮🏻|👮🏼|👮🏽|👮🏾|👮🏿|👮‍♂️|👮🏻‍♂️|👮🏼‍♂️|👮🏽‍♂️|👮🏾‍♂️|👮🏿‍♂️|👮‍♀️|👮🏻‍♀️|👮🏼‍♀️|👮🏽‍♀️|👮🏾‍♀️|👮🏿‍♀️|🕵️|🕵|🕵🏻|🕵🏼|🕵🏽|🕵🏾|🕵🏿|🕵️‍♂️|🕵‍♂️|🕵️‍♂|🕵‍♂|🕵🏻‍♂️|🕵🏼‍♂️|🕵🏽‍♂️|🕵🏾‍♂️|🕵🏿‍♂️|🕵️‍♀️|🕵‍♀️|🕵️‍♀|🕵‍♀|🕵🏻‍♀️|🕵🏼‍♀️|🕵🏽‍♀️|🕵🏾‍♀️|🕵🏿‍♀️|💂|💂🏻|💂🏼|💂🏽|💂🏾|💂🏿|💂‍♂️|💂🏻‍♂️|💂🏼‍♂️|💂🏽‍♂️|💂🏾‍♂️|💂🏿‍♂️|💂‍♀️|💂🏻‍♀️|💂🏼‍♀️|💂🏽‍♀️|💂🏾‍♀️|💂🏿‍♀️|🥷|🥷🏻|🥷🏼|🥷🏽|🥷🏾|🥷🏿|👷|👷🏻|👷🏼|👷🏽|👷🏾|👷🏿|👷‍♂️|👷🏻‍♂️|👷🏼‍♂️|👷🏽‍♂️|👷🏾‍♂️|👷🏿‍♂️|👷‍♀️|👷🏻‍♀️|👷🏼‍♀️|👷🏽‍♀️|👷🏾‍♀️|👷🏿‍♀️|🤴|🤴🏻|🤴🏼|🤴🏽|🤴🏾|🤴🏿|👸|👸🏻|👸🏼|👸🏽|👸🏾|👸🏿|👳|👳🏻|👳🏼|👳🏽|👳🏾|👳🏿|👳‍♂️|👳🏻‍♂️|👳🏼‍♂️|👳🏽‍♂️|👳🏾‍♂️|👳🏿‍♂️|👳‍♀️|👳🏻‍♀️|👳🏼‍♀️|👳🏽‍♀️|👳🏾‍♀️|👳🏿‍♀️|👲|👲🏻|👲🏼|👲🏽|👲🏾|👲🏿|🧕|🧕🏻|🧕🏼|🧕🏽|🧕🏾|🧕🏿|🤵|🤵🏻|🤵🏼|🤵🏽|🤵🏾|🤵🏿|🤵‍♂️|🤵🏻‍♂️|🤵🏼‍♂️|🤵🏽‍♂️|🤵🏾‍♂️|🤵🏿‍♂️|🤵‍♀️|🤵🏻‍♀️|🤵🏼‍♀️|🤵🏽‍♀️|🤵🏾‍♀️|🤵🏿‍♀️|👰|👰🏻|👰🏼|👰🏽|👰🏾|👰🏿|👰‍♂️|👰🏻‍♂️|👰🏼‍♂️|👰🏽‍♂️|👰🏾‍♂️|👰🏿‍♂️|👰‍♀️|👰🏻‍♀️|👰🏼‍♀️|👰🏽‍♀️|👰🏾‍♀️|👰🏿‍♀️|🤰|🤰🏻|🤰🏼|🤰🏽|🤰🏾|🤰🏿|🤱|🤱🏻|🤱🏼|🤱🏽|🤱🏾|🤱🏿|👩‍🍼|👩🏻‍🍼|👩🏼‍🍼|👩🏽‍🍼|👩🏾‍🍼|👩🏿‍🍼|👨‍🍼|👨🏻‍🍼|👨🏼‍🍼|👨🏽‍🍼|👨🏾‍🍼|👨🏿‍🍼|🧑‍🍼|🧑🏻‍🍼|🧑🏼‍🍼|🧑🏽‍🍼|🧑🏾‍🍼|🧑🏿‍🍼|👼|👼🏻|👼🏼|👼🏽|👼🏾|👼🏿|🎅|🎅🏻|🎅🏼|🎅🏽|🎅🏾|🎅🏿|🤶|🤶🏻|🤶🏼|🤶🏽|🤶🏾|🤶🏿|🧑‍🎄|🧑🏻‍🎄|🧑🏼‍🎄|🧑🏽‍🎄|🧑🏾‍🎄|🧑🏿‍🎄|🦸|🦸🏻|🦸🏼|🦸🏽|🦸🏾|🦸🏿|🦸‍♂️|🦸🏻‍♂️|🦸🏼‍♂️|🦸🏽‍♂️|🦸🏾‍♂️|🦸🏿‍♂️|🦸‍♀️|🦸🏻‍♀️|🦸🏼‍♀️|🦸🏽‍♀️|🦸🏾‍♀️|🦸🏿‍♀️|🦹|🦹🏻|🦹🏼|🦹🏽|🦹🏾|🦹🏿|🦹‍♂️|🦹🏻‍♂️|🦹🏼‍♂️|🦹🏽‍♂️|🦹🏾‍♂️|🦹🏿‍♂️|🦹‍♀️|🦹🏻‍♀️|🦹🏼‍♀️|🦹🏽‍♀️|🦹🏾‍♀️|🦹🏿‍♀️|🧙|🧙🏻|🧙🏼|🧙🏽|🧙🏾|🧙🏿|🧙‍♂️|🧙🏻‍♂️|🧙🏼‍♂️|🧙🏽‍♂️|🧙🏾‍♂️|🧙🏿‍♂️|🧙‍♀️|🧙🏻‍♀️|🧙🏼‍♀️|🧙🏽‍♀️|🧙🏾‍♀️|🧙🏿‍♀️|🧚|🧚🏻|🧚🏼|🧚🏽|🧚🏾|🧚🏿|🧚‍♂️|🧚🏻‍♂️|🧚🏼‍♂️|🧚🏽‍♂️|🧚🏾‍♂️|🧚🏿‍♂️|🧚‍♀️|🧚🏻‍♀️|🧚🏼‍♀️|🧚🏽‍♀️|🧚🏾‍♀️|🧚🏿‍♀️|🧛|🧛🏻|🧛🏼|🧛🏽|🧛🏾|🧛🏿|🧛‍♂️|🧛🏻‍♂️|🧛🏼‍♂️|🧛🏽‍♂️|🧛🏾‍♂️|🧛🏿‍♂️|🧛‍♀️|🧛🏻‍♀️|🧛🏼‍♀️|🧛🏽‍♀️|🧛🏾‍♀️|🧛🏿‍♀️|🧜|🧜🏻|🧜🏼|🧜🏽|🧜🏾|🧜🏿|🧜‍♂️|🧜🏻‍♂️|🧜🏼‍♂️|🧜🏽‍♂️|🧜🏾‍♂️|🧜🏿‍♂️|🧜‍♀️|🧜🏻‍♀️|🧜🏼‍♀️|🧜🏽‍♀️|🧜🏾‍♀️|🧜🏿‍♀️|🧝|🧝🏻|🧝🏼|🧝🏽|🧝🏾|🧝🏿|🧝‍♂️|🧝🏻‍♂️|🧝🏼‍♂️|🧝🏽‍♂️|🧝🏾‍♂️|🧝🏿‍♂️|🧝‍♀️|🧝🏻‍♀️|🧝🏼‍♀️|🧝🏽‍♀️|🧝🏾‍♀️|🧝🏿‍♀️|🧞|🧞‍♂️|🧞‍♀️|🧟|🧟‍♂️|🧟‍♀️|💆|💆🏻|💆🏼|💆🏽|💆🏾|💆🏿|💆‍♂️|💆🏻‍♂️|💆🏼‍♂️|💆🏽‍♂️|💆🏾‍♂️|💆🏿‍♂️|💆‍♀️|💆🏻‍♀️|💆🏼‍♀️|💆🏽‍♀️|💆🏾‍♀️|💆🏿‍♀️|💇|💇🏻|💇🏼|💇🏽|💇🏾|💇🏿|💇‍♂️|💇🏻‍♂️|💇🏼‍♂️|💇🏽‍♂️|💇🏾‍♂️|💇🏿‍♂️|💇‍♀️|💇🏻‍♀️|💇🏼‍♀️|💇🏽‍♀️|💇🏾‍♀️|💇🏿‍♀️|🚶|🚶🏻|🚶🏼|🚶🏽|🚶🏾|🚶🏿|🚶‍♂️|🚶🏻‍♂️|🚶🏼‍♂️|🚶🏽‍♂️|🚶🏾‍♂️|🚶🏿‍♂️|🚶‍♀️|🚶🏻‍♀️|🚶🏼‍♀️|🚶🏽‍♀️|🚶🏾‍♀️|🚶🏿‍♀️|🧍|🧍🏻|🧍🏼|🧍🏽|🧍🏾|🧍🏿|🧍‍♂️|🧍🏻‍♂️|🧍🏼‍♂️|🧍🏽‍♂️|🧍🏾‍♂️|🧍🏿‍♂️|🧍‍♀️|🧍🏻‍♀️|🧍🏼‍♀️|🧍🏽‍♀️|🧍🏾‍♀️|🧍🏿‍♀️|🧎|🧎🏻|🧎🏼|🧎🏽|🧎🏾|🧎🏿|🧎‍♂️|🧎🏻‍♂️|🧎🏼‍♂️|🧎🏽‍♂️|🧎🏾‍♂️|🧎🏿‍♂️|🧎‍♀️|🧎🏻‍♀️|🧎🏼‍♀️|🧎🏽‍♀️|🧎🏾‍♀️|🧎🏿‍♀️|🧑‍🦯|🧑🏻‍🦯|🧑🏼‍🦯|🧑🏽‍🦯|🧑🏾‍🦯|🧑🏿‍🦯|👨‍🦯|👨🏻‍🦯|👨🏼‍🦯|👨🏽‍🦯|👨🏾‍🦯|👨🏿‍🦯|👩‍🦯|👩🏻‍🦯|👩🏼‍🦯|👩🏽‍🦯|👩🏾‍🦯|👩🏿‍🦯|🧑‍🦼|🧑🏻‍🦼|🧑🏼‍🦼|🧑🏽‍🦼|🧑🏾‍🦼|🧑🏿‍🦼|👨‍🦼|👨🏻‍🦼|👨🏼‍🦼|👨🏽‍🦼|👨🏾‍🦼|👨🏿‍🦼|👩‍🦼|👩🏻‍🦼|👩🏼‍🦼|👩🏽‍🦼|👩🏾‍🦼|👩🏿‍🦼|🧑‍🦽|🧑🏻‍🦽|🧑🏼‍🦽|🧑🏽‍🦽|🧑🏾‍🦽|🧑🏿‍🦽|👨‍🦽|👨🏻‍🦽|👨🏼‍🦽|👨🏽‍🦽|👨🏾‍🦽|👨🏿‍🦽|👩‍🦽|👩🏻‍🦽|👩🏼‍🦽|👩🏽‍🦽|👩🏾‍🦽|👩🏿‍🦽|🏃|🏃🏻|🏃🏼|🏃🏽|🏃🏾|🏃🏿|🏃‍♂️|🏃🏻‍♂️|🏃🏼‍♂️|🏃🏽‍♂️|🏃🏾‍♂️|🏃🏿‍♂️|🏃‍♀️|🏃🏻‍♀️|🏃🏼‍♀️|🏃🏽‍♀️|🏃🏾‍♀️|🏃🏿‍♀️|💃|💃🏻|💃🏼|💃🏽|💃🏾|💃🏿|🕺|🕺🏻|🕺🏼|🕺🏽|🕺🏾|🕺🏿|🕴️|🕴|🕴🏻|🕴🏼|🕴🏽|🕴🏾|🕴🏿|👯|👯‍♂️|👯‍♀️|🧖|🧖🏻|🧖🏼|🧖🏽|🧖🏾|🧖🏿|🧖‍♂️|🧖🏻‍♂️|🧖🏼‍♂️|🧖🏽‍♂️|🧖🏾‍♂️|🧖🏿‍♂️|🧖‍♀️|🧖🏻‍♀️|🧖🏼‍♀️|🧖🏽‍♀️|🧖🏾‍♀️|🧖🏿‍♀️|🧗|🧗🏻|🧗🏼|🧗🏽|🧗🏾|🧗🏿|🧗‍♂️|🧗🏻‍♂️|🧗🏼‍♂️|🧗🏽‍♂️|🧗🏾‍♂️|🧗🏿‍♂️|🧗‍♀️|🧗🏻‍♀️|🧗🏼‍♀️|🧗🏽‍♀️|🧗🏾‍♀️|🧗🏿‍♀️|🤺|🏇|🏇🏻|🏇🏼|🏇🏽|🏇🏾|🏇🏿|⛷️|⛷|🏂|🏂🏻|🏂🏼|🏂🏽|🏂🏾|🏂🏿|🏌️|🏌|🏌🏻|🏌🏼|🏌🏽|🏌🏾|🏌🏿|🏌️‍♂️|🏌‍♂️|🏌️‍♂|🏌‍♂|🏌🏻‍♂️|🏌🏼‍♂️|🏌🏽‍♂️|🏌🏾‍♂️|🏌🏿‍♂️|🏌️‍♀️|🏌‍♀️|🏌️‍♀|🏌‍♀|🏌🏻‍♀️|🏌🏼‍♀️|🏌🏽‍♀️|🏌🏾‍♀️|🏌🏿‍♀️|🏄|🏄🏻|🏄🏼|🏄🏽|🏄🏾|🏄🏿|🏄‍♂️|🏄🏻‍♂️|🏄🏼‍♂️|🏄🏽‍♂️|🏄🏾‍♂️|🏄🏿‍♂️|🏄‍♀️|🏄🏻‍♀️|🏄🏼‍♀️|🏄🏽‍♀️|🏄🏾‍♀️|🏄🏿‍♀️|🚣|🚣🏻|🚣🏼|🚣🏽|🚣🏾|🚣🏿|🚣‍♂️|🚣🏻‍♂️|🚣🏼‍♂️|🚣🏽‍♂️|🚣🏾‍♂️|🚣🏿‍♂️|🚣‍♀️|🚣🏻‍♀️|🚣🏼‍♀️|🚣🏽‍♀️|🚣🏾‍♀️|🚣🏿‍♀️|🏊|🏊🏻|🏊🏼|🏊🏽|🏊🏾|🏊🏿|🏊‍♂️|🏊🏻‍♂️|🏊🏼‍♂️|🏊🏽‍♂️|🏊🏾‍♂️|🏊🏿‍♂️|🏊‍♀️|🏊🏻‍♀️|🏊🏼‍♀️|🏊🏽‍♀️|🏊🏾‍♀️|🏊🏿‍♀️|⛹️|⛹|⛹🏻|⛹🏼|⛹🏽|⛹🏾|⛹🏿|⛹️‍♂️|⛹‍♂️|⛹️‍♂|⛹‍♂|⛹🏻‍♂️|⛹🏼‍♂️|⛹🏽‍♂️|⛹🏾‍♂️|⛹🏿‍♂️|⛹️‍♀️|⛹‍♀️|⛹️‍♀|⛹‍♀|⛹🏻‍♀️|⛹🏼‍♀️|⛹🏽‍♀️|⛹🏾‍♀️|⛹🏿‍♀️|🏋️|🏋|🏋🏻|🏋🏼|🏋🏽|🏋🏾|🏋🏿|🏋️‍♂️|🏋‍♂️|🏋️‍♂|🏋‍♂|🏋🏻‍♂️|🏋🏼‍♂️|🏋🏽‍♂️|🏋🏾‍♂️|🏋🏿‍♂️|🏋️‍♀️|🏋‍♀️|🏋️‍♀|🏋‍♀|🏋🏻‍♀️|🏋🏼‍♀️|🏋🏽‍♀️|🏋🏾‍♀️|🏋🏿‍♀️|🚴|🚴🏻|🚴🏼|🚴🏽|🚴🏾|🚴🏿|🚴‍♂️|🚴🏻‍♂️|🚴🏼‍♂️|🚴🏽‍♂️|🚴🏾‍♂️|🚴🏿‍♂️|🚴‍♀️|🚴🏻‍♀️|🚴🏼‍♀️|🚴🏽‍♀️|🚴🏾‍♀️|🚴🏿‍♀️|🚵|🚵🏻|🚵🏼|🚵🏽|🚵🏾|🚵🏿|🚵‍♂️|🚵🏻‍♂️|🚵🏼‍♂️|🚵🏽‍♂️|🚵🏾‍♂️|🚵🏿‍♂️|🚵‍♀️|🚵🏻‍♀️|🚵🏼‍♀️|🚵🏽‍♀️|🚵🏾‍♀️|🚵🏿‍♀️|🤸|🤸🏻|🤸🏼|🤸🏽|🤸🏾|🤸🏿|🤸‍♂️|🤸🏻‍♂️|🤸🏼‍♂️|🤸🏽‍♂️|🤸🏾‍♂️|🤸🏿‍♂️|🤸‍♀️|🤸🏻‍♀️|🤸🏼‍♀️|🤸🏽‍♀️|🤸🏾‍♀️|🤸🏿‍♀️|🤼|🤼‍♂️|🤼‍♀️|🤽|🤽🏻|🤽🏼|🤽🏽|🤽🏾|🤽🏿|🤽‍♂️|🤽🏻‍♂️|🤽🏼‍♂️|🤽🏽‍♂️|🤽🏾‍♂️|🤽🏿‍♂️|🤽‍♀️|🤽🏻‍♀️|🤽🏼‍♀️|🤽🏽‍♀️|🤽🏾‍♀️|🤽🏿‍♀️|🤾|🤾🏻|🤾🏼|🤾🏽|🤾🏾|🤾🏿|🤾‍♂️|🤾🏻‍♂️|🤾🏼‍♂️|🤾🏽‍♂️|🤾🏾‍♂️|🤾🏿‍♂️|🤾‍♀️|🤾🏻‍♀️|🤾🏼‍♀️|🤾🏽‍♀️|🤾🏾‍♀️|🤾🏿‍♀️|🤹|🤹🏻|🤹🏼|🤹🏽|🤹🏾|🤹🏿|🤹‍♂️|🤹🏻‍♂️|🤹🏼‍♂️|🤹🏽‍♂️|🤹🏾‍♂️|🤹🏿‍♂️|🤹‍♀️|🤹🏻‍♀️|🤹🏼‍♀️|🤹🏽‍♀️|🤹🏾‍♀️|🤹🏿‍♀️|🧘|🧘🏻|🧘🏼|🧘🏽|🧘🏾|🧘🏿|🧘‍♂️|🧘🏻‍♂️|🧘🏼‍♂️|🧘🏽‍♂️|🧘🏾‍♂️|🧘🏿‍♂️|🧘‍♀️|🧘🏻‍♀️|🧘🏼‍♀️|🧘🏽‍♀️|🧘🏾‍♀️|🧘🏿‍♀️|🛀|🛀🏻|🛀🏼|🛀🏽|🛀🏾|🛀🏿|🛌|🛌🏻|🛌🏼|🛌🏽|🛌🏾|🛌🏿|🧑‍🤝‍🧑|🧑🏻‍🤝‍🧑🏻|🧑🏻‍🤝‍🧑🏼|🧑🏻‍🤝‍🧑🏽|🧑🏻‍🤝‍🧑🏾|🧑🏻‍🤝‍🧑🏿|🧑🏼‍🤝‍🧑🏻|🧑🏼‍🤝‍🧑🏼|🧑🏼‍🤝‍🧑🏽|🧑🏼‍🤝‍🧑🏾|🧑🏼‍🤝‍🧑🏿|🧑🏽‍🤝‍🧑🏻|🧑🏽‍🤝‍🧑🏼|🧑🏽‍🤝‍🧑🏽|🧑🏽‍🤝‍🧑🏾|🧑🏽‍🤝‍🧑🏿|🧑🏾‍🤝‍🧑🏻|🧑🏾‍🤝‍🧑🏼|🧑🏾‍🤝‍🧑🏽|🧑🏾‍🤝‍🧑🏾|🧑🏾‍🤝‍🧑🏿|🧑🏿‍🤝‍🧑🏻|🧑🏿‍🤝‍🧑🏼|🧑🏿‍🤝‍🧑🏽|🧑🏿‍🤝‍🧑🏾|🧑🏿‍🤝‍🧑🏿|👭|👭🏻|👩🏻‍🤝‍👩🏼|👩🏻‍🤝‍👩🏽|👩🏻‍🤝‍👩🏾|👩🏻‍🤝‍👩🏿|👩🏼‍🤝‍👩🏻|👭🏼|👩🏼‍🤝‍👩🏽|👩🏼‍🤝‍👩🏾|👩🏼‍🤝‍👩🏿|👩🏽‍🤝‍👩🏻|👩🏽‍🤝‍👩🏼|👭🏽|👩🏽‍🤝‍👩🏾|👩🏽‍🤝‍👩🏿|👩🏾‍🤝‍👩🏻|👩🏾‍🤝‍👩🏼|👩🏾‍🤝‍👩🏽|👭🏾|👩🏾‍🤝‍👩🏿|👩🏿‍🤝‍👩🏻|👩🏿‍🤝‍👩🏼|👩🏿‍🤝‍👩🏽|👩🏿‍🤝‍👩🏾|👭🏿|👫|👫🏻|👩🏻‍🤝‍👨🏼|👩🏻‍🤝‍👨🏽|👩🏻‍🤝‍👨🏾|👩🏻‍🤝‍👨🏿|👩🏼‍🤝‍👨🏻|👫🏼|👩🏼‍🤝‍👨🏽|👩🏼‍🤝‍👨🏾|👩🏼‍🤝‍👨🏿|👩🏽‍🤝‍👨🏻|👩🏽‍🤝‍👨🏼|👫🏽|👩🏽‍🤝‍👨🏾|👩🏽‍🤝‍👨🏿|👩🏾‍🤝‍👨🏻|👩🏾‍🤝‍👨🏼|👩🏾‍🤝‍👨🏽|👫🏾|👩🏾‍🤝‍👨🏿|👩🏿‍🤝‍👨🏻|👩🏿‍🤝‍👨🏼|👩🏿‍🤝‍👨🏽|👩🏿‍🤝‍👨🏾|👫🏿|👬|👬🏻|👨🏻‍🤝‍👨🏼|👨🏻‍🤝‍👨🏽|👨🏻‍🤝‍👨🏾|👨🏻‍🤝‍👨🏿|👨🏼‍🤝‍👨🏻|👬🏼|👨🏼‍🤝‍👨🏽|👨🏼‍🤝‍👨🏾|👨🏼‍🤝‍👨🏿|👨🏽‍🤝‍👨🏻|👨🏽‍🤝‍👨🏼|👬🏽|👨🏽‍🤝‍👨🏾|👨🏽‍🤝‍👨🏿|👨🏾‍🤝‍👨🏻|👨🏾‍🤝‍👨🏼|👨🏾‍🤝‍👨🏽|👬🏾|👨🏾‍🤝‍👨🏿|👨🏿‍🤝‍👨🏻|👨🏿‍🤝‍👨🏼|👨🏿‍🤝‍👨🏽|👨🏿‍🤝‍👨🏾|👬🏿|💏|👩‍❤️‍💋‍👨|👨‍❤️‍💋‍👨|👩‍❤️‍💋‍👩|💑|👩‍❤️‍👨|👨‍❤️‍👨|👩‍❤️‍👩|👪|👨‍👩‍👦|👨‍👩‍👧|👨‍👩‍👧‍👦|👨‍👩‍👦‍👦|👨‍👩‍👧‍👧|👨‍👨‍👦|👨‍👨‍👧|👨‍👨‍👧‍👦|👨‍👨‍👦‍👦|👨‍👨‍👧‍👧|👩‍👩‍👦|👩‍👩‍👧|👩‍👩‍👧‍👦|👩‍👩‍👦‍👦|👩‍👩‍👧‍👧|👨‍👦|👨‍👦‍👦|👨‍👧|👨‍👧‍👦|👨‍👧‍👧|👩‍👦|👩‍👦‍👦|👩‍👧|👩‍👧‍👦|👩‍👧‍👧|🗣️|🗣|👤|👥|🫂|👣|🦰|🦱|🦳|🦲|🐵|🐒|🦍|🦧|🐶|🐕|🦮|🐕‍🦺|🐩|🐺|🦊|🦝|🐱|🐈|🐈‍⬛|🦁|🐯|🐅|🐆|🐴|🐎|🦄|🦓|🦌|🦬|🐮|🐂|🐃|🐄|🐷|🐖|🐗|🐽|🐏|🐑|🐐|🐪|🐫|🦙|🦒|🐘|🦣|🦏|🦛|🐭|🐁|🐀|🐹|🐰|🐇|🐿️|🐿|🦫|🦔|🦇|🐻|🐻‍❄️|🐨|🐼|🦥|🦦|🦨|🦘|🦡|🐾|🦃|🐔|🐓|🐣|🐤|🐥|🐦|🐧|🕊️|🕊|🦅|🦆|🦢|🦉|🦤|🪶|🦩|🦚|🦜|🐸|🐊|🐢|🦎|🐍|🐲|🐉|🦕|🦖|🐳|🐋|🐬|🦭|🐟|🐠|🐡|🦈|🐙|🐚|🐌|🦋|🐛|🐜|🐝|🪲|🐞|🦗|🪳|🕷️|🕷|🕸️|🕸|🦂|🦟|🪰|🪱|🦠|💐|🌸|💮|🏵️|🏵|🌹|🥀|🌺|🌻|🌼|🌷|🌱|🪴|🌲|🌳|🌴|🌵|🌾|🌿|☘️|☘|🍀|🍁|🍂|🍃|🍇|🍈|🍉|🍊|🍋|🍌|🍍|🥭|🍎|🍏|🍐|🍑|🍒|🍓|🫐|🥝|🍅|🫒|🥥|🥑|🍆|🥔|🥕|🌽|🌶️|🌶|🫑|🥒|🥬|🥦|🧄|🧅|🍄|🥜|🌰|🍞|🥐|🥖|🫓|🥨|🥯|🥞|🧇|🧀|🍖|🍗|🥩|🥓|🍔|🍟|🍕|🌭|🥪|🌮|🌯|🫔|🥙|🧆|🥚|🍳|🥘|🍲|🫕|🥣|🥗|🍿|🧈|🧂|🥫|🍱|🍘|🍙|🍚|🍛|🍜|🍝|🍠|🍢|🍣|🍤|🍥|🥮|🍡|🥟|🥠|🥡|🦀|🦞|🦐|🦑|🦪|🍦|🍧|🍨|🍩|🍪|🎂|🍰|🧁|🥧|🍫|🍬|🍭|🍮|🍯|🍼|🥛|☕|🫖|🍵|🍶|🍾|🍷|🍸|🍹|🍺|🍻|🥂|🥃|🥤|🧋|🧃|🧉|🧊|🥢|🍽️|🍽|🍴|🥄|🔪|🏺|🌍|🌎|🌏|🌐|🗺️|🗺|🗾|🧭|🏔️|🏔|⛰️|⛰|🌋|🗻|🏕️|🏕|🏖️|🏖|🏜️|🏜|🏝️|🏝|🏞️|🏞|🏟️|🏟|🏛️|🏛|🏗️|🏗|🧱|🪨|🪵|🛖|🏘️|🏘|🏚️|🏚|🏠|🏡|🏢|🏣|🏤|🏥|🏦|🏨|🏩|🏪|🏫|🏬|🏭|🏯|🏰|💒|🗼|🗽|⛪|🕌|🛕|🕍|⛩️|⛩|🕋|⛲|⛺|🌁|🌃|🏙️|🏙|🌄|🌅|🌆|🌇|🌉|♨️|♨|🎠|🎡|🎢|💈|🎪|🚂|🚃|🚄|🚅|🚆|🚇|🚈|🚉|🚊|🚝|🚞|🚋|🚌|🚍|🚎|🚐|🚑|🚒|🚓|🚔|🚕|🚖|🚗|🚘|🚙|🛻|🚚|🚛|🚜|🏎️|🏎|🏍️|🏍|🛵|🦽|🦼|🛺|🚲|🛴|🛹|🛼|🚏|🛣️|🛣|🛤️|🛤|🛢️|🛢|⛽|🚨|🚥|🚦|🛑|🚧|⚓|⛵|🛶|🚤|🛳️|🛳|⛴️|⛴|🛥️|🛥|🚢|✈️|✈|🛩️|🛩|🛫|🛬|🪂|💺|🚁|🚟|🚠|🚡|🛰️|🛰|🚀|🛸|🛎️|🛎|🧳|⌛|⏳|⌚|⏰|⏱️|⏱|⏲️|⏲|🕰️|🕰|🕛|🕧|🕐|🕜|🕑|🕝|🕒|🕞|🕓|🕟|🕔|🕠|🕕|🕡|🕖|🕢|🕗|🕣|🕘|🕤|🕙|🕥|🕚|🕦|🌑|🌒|🌓|🌔|🌕|🌖|🌗|🌘|🌙|🌚|🌛|🌜|🌡️|🌡|☀️|☀|🌝|🌞|🪐|⭐|🌟|🌠|🌌|☁️|☁|⛅|⛈️|⛈|🌤️|🌤|🌥️|🌥|🌦️|🌦|🌧️|🌧|🌨️|🌨|🌩️|🌩|🌪️|🌪|🌫️|🌫|🌬️|🌬|🌀|🌈|🌂|☂️|☂|☔|⛱️|⛱|⚡|❄️|❄|☃️|☃|⛄|☄️|☄|🔥|💧|🌊|🎃|🎄|🎆|🎇|🧨|✨|🎈|🎉|🎊|🎋|🎍|🎎|🎏|🎐|🎑|🧧|🎀|🎁|🎗️|🎗|🎟️|🎟|🎫|🎖️|🎖|🏆|🏅|🥇|🥈|🥉|⚽|⚾|🥎|🏀|🏐|🏈|🏉|🎾|🥏|🎳|🏏|🏑|🏒|🥍|🏓|🏸|🥊|🥋|🥅|⛳|⛸️|⛸|🎣|🤿|🎽|🎿|🛷|🥌|🎯|🪀|🪁|🎱|🔮|🪄|🧿|🎮|🕹️|🕹|🎰|🎲|🧩|🧸|🪅|🪆|♠️|♠|♥️|♥|♦️|♦|♣️|♣|♟️|♟|🃏|🀄|🎴|🎭|🖼️|🖼|🎨|🧵|🪡|🧶|🪢|👓|🕶️|🕶|🥽|🥼|🦺|👔|👕|👖|🧣|🧤|🧥|🧦|👗|👘|🥻|🩱|🩲|🩳|👙|👚|👛|👜|👝|🛍️|🛍|🎒|🩴|👞|👟|🥾|🥿|👠|👡|🩰|👢|👑|👒|🎩|🎓|🧢|🪖|⛑️|⛑|📿|💄|💍|💎|🔇|🔈|🔉|🔊|📢|📣|📯|🔔|🔕|🎼|🎵|🎶|🎙️|🎙|🎚️|🎚|🎛️|🎛|🎤|🎧|📻|🎷|🪗|🎸|🎹|🎺|🎻|🪕|🥁|🪘|📱|📲|☎️|☎|📞|📟|📠|🔋|🔌|💻|🖥️|🖥|🖨️|🖨|⌨️|⌨|🖱️|🖱|🖲️|🖲|💽|💾|💿|📀|🧮|🎥|🎞️|🎞|📽️|📽|🎬|📺|📷|📸|📹|📼|🔍|🔎|🕯️|🕯|💡|🔦|🏮|🪔|📔|📕|📖|📗|📘|📙|📚|📓|📒|📃|📜|📄|📰|🗞️|🗞|📑|🔖|🏷️|🏷|💰|🪙|💴|💵|💶|💷|💸|💳|🧾|💹|✉️|✉|📧|📨|📩|📤|📥|📦|📫|📪|📬|📭|📮|🗳️|🗳|✏️|✏|✒️|✒|🖋️|🖋|🖊️|🖊|🖌️|🖌|🖍️|🖍|📝|💼|📁|📂|🗂️|🗂|📅|📆|🗒️|🗒|🗓️|🗓|📇|📈|📉|📊|📋|📌|📍|📎|🖇️|🖇|📏|📐|✂️|✂|🗃️|🗃|🗄️|🗄|🗑️|🗑|🔒|🔓|🔏|🔐|🔑|🗝️|🗝|🔨|🪓|⛏️|⛏|⚒️|⚒|🛠️|🛠|🗡️|🗡|⚔️|⚔|🔫|🪃|🏹|🛡️|🛡|🪚|🔧|🪛|🔩|⚙️|⚙|🗜️|🗜|⚖️|⚖|🦯|🔗|⛓️|⛓|🪝|🧰|🧲|🪜|⚗️|⚗|🧪|🧫|🧬|🔬|🔭|📡|💉|🩸|💊|🩹|🩺|🚪|🛗|🪞|🪟|🛏️|🛏|🛋️|🛋|🪑|🚽|🪠|🚿|🛁|🪤|🪒|🧴|🧷|🧹|🧺|🧻|🪣|🧼|🪥|🧽|🧯|🛒|🚬|⚰️|⚰|🪦|⚱️|⚱|🗿|🪧|🏧|🚮|🚰|♿|🚹|🚺|🚻|🚼|🚾|🛂|🛃|🛄|🛅|⚠️|⚠|🚸|⛔|🚫|🚳|🚭|🚯|🚱|🚷|📵|🔞|☢️|☢|☣️|☣|⬆️|⬆|↗️|↗|➡️|➡|↘️|↘|⬇️|⬇|↙️|↙|⬅️|⬅|↖️|↖|↕️|↕|↔️|↔|↩️|↩|↪️|↪|⤴️|⤴|⤵️|⤵|🔃|🔄|🔙|🔚|🔛|🔜|🔝|🛐|⚛️|⚛|🕉️|🕉|✡️|✡|☸️|☸|☯️|☯|✝️|✝|☦️|☦|☪️|☪|☮️|☮|🕎|🔯|♈|♉|♊|♋|♌|♍|♎|♏|♐|♑|♒|♓|⛎|🔀|🔁|🔂|▶️|▶|⏩|⏭️|⏭|⏯️|⏯|◀️|◀|⏪|⏮️|⏮|🔼|⏫|🔽|⏬|⏸️|⏸|⏹️|⏹|⏺️|⏺|⏏️|⏏|🎦|🔅|🔆|📶|📳|📴|♀️|♀|♂️|♂|⚧️|⚧|✖️|✖|➕|➖|➗|♾️|♾|‼️|‼|⁉️|⁉|❓|❔|❕|❗|💱|💲|⚕️|⚕|♻️|♻|⚜️|⚜|🔱|📛|🔰|⭕|✅|☑️|☑|✔️|✔|❌|❎|➰|➿|〽️|〽|✳️|✳|✴️|✴|❇️|❇|©️|©|®️|®|™️|™|#️⃣|0️⃣|1️⃣|2️⃣|3️⃣|4️⃣|5️⃣|6️⃣|7️⃣|8️⃣|9️⃣|🔟|🔠|🔡|🔢|🔣|🔤|🅰️|🅰|🆎|🅱️|🅱|🆑|🆒|🆓|ℹ️|ℹ|🆔|Ⓜ️|Ⓜ|🆕|🆖|🅾️|🅾|🆗|🅿️|🅿|🆘|🆙|🆚|🈁|🈂️|🈂|🈷️|🈷|🈶|🈯|🉐|🈹|🈚|🈲|🉑|🈸|🈴|🈳|㊗️|㊗|㊙️|㊙|🈺|🈵|🔴|🟠|🟡|🟢|🔵|🟣|🟤|⚫|⚪|🟥|🟧|🟨|🟩|🟦|🟪|🟫|⬛|⬜|◼️|◼|◻️|◻|◾|◽|▪️|▪|▫️|▫|🔶|🔷|🔸|🔹|🔺|🔻|💠|🔘|🔳|🔲|🏁|🚩|🎌|🏴|🏳️|🏳|🏳️‍🌈|🏳‍🌈|🏳️‍⚧️|🏳‍⚧️|🏳️‍⚧|🏳‍⚧|🏴‍☠️|🇦🇨|🇦🇩|🇦🇪|🇦🇫|🇦🇬|🇦🇮|🇦🇱|🇦🇲|🇦🇴|🇦🇶|🇦🇷|🇦🇸|🇦🇹|🇦🇺|🇦🇼|🇦🇽|🇦🇿|🇧🇦|🇧🇧|🇧🇩|🇧🇪|🇧🇫|🇧🇬|🇧🇭|🇧🇮|🇧🇯|🇧🇱|🇧🇲|🇧🇳|🇧🇴|🇧🇶|🇧🇷|🇧🇸|🇧🇹|🇧🇻|🇧🇼|🇧🇾|🇧🇿|🇨🇦|🇨🇨|🇨🇩|🇨🇫|🇨🇬|🇨🇭|🇨🇮|🇨🇰|🇨🇱|🇨🇲|🇨🇳|🇨🇴|🇨🇵|🇨🇷|🇨🇺|🇨🇻|🇨🇼|🇨🇽|🇨🇾|🇨🇿|🇩🇪|🇩🇬|🇩🇯|🇩🇰|🇩🇲|🇩🇴|🇩🇿|🇪🇦|🇪🇨|🇪🇪|🇪🇬|🇪🇭|🇪🇷|🇪🇸|🇪🇹|🇪🇺|🇫🇮|🇫🇯|🇫🇰|🇫🇲|🇫🇴|🇫🇷|🇬🇦|🇬🇧|🇬🇩|🇬🇪|🇬🇫|🇬🇬|🇬🇭|🇬🇮|🇬🇱|🇬🇲|🇬🇳|🇬🇵|🇬🇶|🇬🇷|🇬🇸|🇬🇹|🇬🇺|🇬🇼|🇬🇾|🇭🇰|🇭🇲|🇭🇳|🇭🇷|🇭🇹|🇭🇺|🇮🇨|🇮🇩|🇮🇪|🇮🇱|🇮🇲|🇮🇳|🇮🇴|🇮🇶|🇮🇷|🇮🇸|🇮🇹|🇯🇪|🇯🇲|🇯🇴|🇯🇵|🇰🇪|🇰🇬|🇰🇭|🇰🇮|🇰🇲|🇰🇳|🇰🇵|🇰🇷|🇰🇼|🇰🇾|🇰🇿|🇱🇦|🇱🇧|🇱🇨|🇱🇮|🇱🇰|🇱🇷|🇱🇸|🇱🇹|🇱🇺|🇱🇻|🇱🇾|🇲🇦|🇲🇨|🇲🇩|🇲🇪|🇲🇫|🇲🇬|🇲🇭|🇲🇰|🇲🇱|🇲🇲|🇲🇳|🇲🇴|🇲🇵|🇲🇶|🇲🇷|🇲🇸|🇲🇹|🇲🇺|🇲🇻|🇲🇼|🇲🇽|🇲🇾|🇲🇿|🇳🇦|🇳🇨|🇳🇪|🇳🇫|🇳🇬|🇳🇮|🇳🇱|🇳🇴|🇳🇵|🇳🇷|🇳🇺|🇳🇿|🇴🇲|🇵🇦|🇵🇪|🇵🇫|🇵🇬|🇵🇭|🇵🇰|🇵🇱|🇵🇲|🇵🇳|🇵🇷|🇵🇸|🇵🇹|🇵🇼|🇵🇾|🇶🇦|🇷🇪|🇷🇴|🇷🇸|🇷🇺|🇷🇼|🇸🇦|🇸🇧|🇸🇨|🇸🇩|🇸🇪|🇸🇬|🇸🇭|🇸🇮|🇸🇯|🇸🇰|🇸🇱|🇸🇲|🇸🇳|🇸🇴|🇸🇷|🇸🇸|🇸🇹|🇸🇻|🇸🇽|🇸🇾|🇸🇿|🇹🇦|🇹🇨|🇹🇩|🇹🇫|🇹🇬|🇹🇭|🇹🇯|🇹🇰|🇹🇱|🇹🇲|🇹🇳|🇹🇴|🇹🇷|🇹🇹|🇹🇻|🇹🇼|🇹🇿|🇺🇦|🇺🇬|🇺🇲|🇺🇳|🇺🇸|🇺🇾|🇺🇿|🇻🇦|🇻🇨|🇻🇪|🇻🇬|🇻🇮|🇻🇳|🇻🇺|🇼🇫|🇼🇸|🇽🇰|🇾🇪|🇾🇹|🇿🇦|🇿🇲|🇿🇼)", "replacement": " $1 "}, "custom_char_filter": {"type": "mapping", "mappings": ["！ => _ldn_", "？ => _gdp_", "。 => _gdpe_", "｡ => _feu_", "＂ => _ela_", "＃ => _cqd_", "＄ => _pnx_", "％ => _vee_", "＆ => _hse_", "＇ => _zrk_", "（ => _gsf_", "） => _cwh_", "＊ => _rcg_", "＋ => _kkx_", "， => _pdo_", "－ => _kyp_", "／ => _bsm_", "： => _bno_", "； => _cyt_", "＜ => _sqy_", "＞ => _dqc_", "＝ => _xwo_", "＠ => _oqy_", "［ => _sbx_", "］ => _bsh_", "＼ => _yxw_", "＾ => _csb_", "＿ => _gjn_", "｀ => _wzr_", "｛ => _kvj_", "｝ => _hqd_", "｜ => _uan_", "～ => _wcn_", "｟ => _gmx_", "｠ => _khi_", "｢ => _mfm_", "｣ => _yay_", "､ => _psx_", "、 => _ayb_", "〃 => _bzu_", "》 => _vrd_", "「 => _tjc_", "」 => _ywi_", "『 => _fiv_", "』 => _ois_", "【 => _khx_", "】 => _xlh_", "〔 => _qxg_", "〕 => _qbq_", "〖 => _avd_", "〗 => _yzl_", "〘 => _uwh_", "〙 => _lwd_", "〚 => _sce_", "〛 => _pzs_", "〜 => _fzx_", "〝 => _oik_", "〞 => _gag_", "〟 => _tgx_", "〰 => _ekt_", "〾 => _smv_", "〿 => _tkg_", "– => _rjf_", "— => _hhf_", "‘ => _nod_", "' => _jro_", "‛ => _xgq_", "“ => _qzu_", "” => _hem_", "„ => _rah_", "‟ => _gvt_", "… => _rdb_", "‧ => _umy_", "﹏ => _hgi_", ". => _abb_", "! => _uzf_", "\" => _oxq_", "# => _gyt_", "$ => _oqn_", "% => _txm_", "& => _iww_", "( => _weh_", ") => _pze_", "* => _enk_", ", => _phg_", "- => _cky_", "/ => _djt_", ": => _wld_", "; => _zbk_", "< => _srz_", "= => _mqs_", "> => _rzv_", "? => _uuc_", "@ => _fvh_", "[ => _bhz_", "] => _bei_", "^ => _ypv_", "_ => _jgn_", "` => _qqc_", "{ => _hiw_", "| => _cpl_", "} => _iim_", "~ => _crp_", "😀=> _1F600_", "😃=> _1F603_", "😄=> _1F604_", "😁=> _1F601_", "😆=> _1F606_", "😅=> _1F605_", "🤣=> _1F923_", "😂=> _1F602_", "🙂=> _1F642_", "🙃=> _1F643_", "😉=> _1F609_", "😊=> _1F60A_", "😇=> _1F607_", "🥰=> _1F970_", "😍=> _1F60D_", "🤩=> _1F929_", "😘=> _1F618_", "😗=> _1F617_", "☺️=> _263AFE0F_", "☺=> _263A_", "😚=> _1F61A_", "😙=> _1F619_", "🥲=> _1F972_", "😋=> _1F60B_", "😛=> _1F61B_", "😜=> _1F61C_", "🤪=> _1F92A_", "😝=> _1F61D_", "🤑=> _1F911_", "🤗=> _1F917_", "🤭=> _1F92D_", "🤫=> _1F92B_", "🤔=> _1F914_", "🤐=> _1F910_", "🤨=> _1F928_", "😐=> _1F610_", "😑=> _1F611_", "😶=> _1F636_", "😏=> _1F60F_", "😒=> _1F612_", "🙄=> _1F644_", "😬=> _1F62C_", "🤥=> _1F925_", "😌=> _1F60C_", "😔=> _1F614_", "😪=> _1F62A_", "🤤=> _1F924_", "😴=> _1F634_", "😷=> _1F637_", "🤒=> _1F912_", "🤕=> _1F915_", "🤢=> _1F922_", "🤮=> _1F92E_", "🤧=> _1F927_", "🥵=> _1F975_", "🥶=> _1F976_", "🥴=> _1F974_", "😵=> _1F635_", "🤯=> _1F92F_", "🤠=> _1F920_", "🥳=> _1F973_", "🥸=> _1F978_", "😎=> _1F60E_", "🤓=> _1F913_", "🧐=> _1F9D0_", "😕=> _1F615_", "😟=> _1F61F_", "🙁=> _1F641_", "☹️=> _2639FE0F_", "☹=> _2639_", "😮=> _1F62E_", "😯=> _1F62F_", "😲=> _1F632_", "😳=> _1F633_", "🥺=> _1F97A_", "😦=> _1F626_", "😧=> _1F627_", "😨=> _1F628_", "😰=> _1F630_", "😥=> _1F625_", "😢=> _1F622_", "😭=> _1F62D_", "😱=> _1F631_", "😖=> _1F616_", "😣=> _1F623_", "😞=> _1F61E_", "😓=> _1F613_", "😩=> _1F629_", "😫=> _1F62B_", "🥱=> _1F971_", "😤=> _1F624_", "😡=> _1F621_", "😠=> _1F620_", "🤬=> _1F92C_", "😈=> _1F608_", "👿=> _1F47F_", "💀=> _1F480_", "☠️=> _2620FE0F_", "☠=> _2620_", "💩=> _1F4A9_", "🤡=> _1F921_", "👹=> _1F479_", "👺=> _1F47A_", "👻=> _1F47B_", "👽=> _1F47D_", "👾=> _1F47E_", "🤖=> _1F916_", "😺=> _1F63A_", "😸=> _1F638_", "😹=> _1F639_", "😻=> _1F63B_", "😼=> _1F63C_", "😽=> _1F63D_", "🙀=> _1F640_", "😿=> _1F63F_", "😾=> _1F63E_", "🙈=> _1F648_", "🙉=> _1F649_", "🙊=> _1F64A_", "💋=> _1F48B_", "💌=> _1F48C_", "💘=> _1F498_", "💝=> _1F49D_", "💖=> _1F496_", "💗=> _1F497_", "💓=> _1F493_", "💞=> _1F49E_", "💕=> _1F495_", "💟=> _1F49F_", "❣️=> _2763FE0F_", "❣=> _2763_", "💔=> _1F494_", "❤️=> _2764FE0F_", "❤=> _2764_", "🧡=> _1F9E1_", "💛=> _1F49B_", "💚=> _1F49A_", "💙=> _1F499_", "💜=> _1F49C_", "🤎=> _1F90E_", "🖤=> _1F5A4_", "🤍=> _1F90D_", "💯=> _1F4AF_", "💢=> _1F4A2_", "💥=> _1F4A5_", "💫=> _1F4AB_", "💦=> _1F4A6_", "💨=> _1F4A8_", "🕳️=> _1F573FE0F_", "🕳=> _1F573_", "💣=> _1F4A3_", "💬=> _1F4AC_", "👁️‍🗨️=> _1F441FE0F200D1F5E8FE0F_", "👁‍🗨️=> _1F441200D1F5E8FE0F_", "👁️‍🗨=> _1F441FE0F200D1F5E8_", "👁‍🗨=> _1F441200D1F5E8_", "🗨️=> _1F5E8FE0F_", "🗨=> _1F5E8_", "🗯️=> _1F5EFFE0F_", "🗯=> _1F5EF_", "💭=> _1F4AD_", "💤=> _1F4A4_", "👋=> _1F44B_", "👋🏻=> _1F44B1F3FB_", "👋🏼=> _1F44B1F3FC_", "👋🏽=> _1F44B1F3FD_", "👋🏾=> _1F44B1F3FE_", "👋🏿=> _1F44B1F3FF_", "🤚=> _1F91A_", "🤚🏻=> _1F91A1F3FB_", "🤚🏼=> _1F91A1F3FC_", "🤚🏽=> _1F91A1F3FD_", "🤚🏾=> _1F91A1F3FE_", "🤚🏿=> _1F91A1F3FF_", "🖐️=> _1F590FE0F_", "🖐=> _1F590_", "🖐🏻=> _1F5901F3FB_", "🖐🏼=> _1F5901F3FC_", "🖐🏽=> _1F5901F3FD_", "🖐🏾=> _1F5901F3FE_", "🖐🏿=> _1F5901F3FF_", "✋=> _270B_", "✋🏻=> _270B1F3FB_", "✋🏼=> _270B1F3FC_", "✋🏽=> _270B1F3FD_", "✋🏾=> _270B1F3FE_", "✋🏿=> _270B1F3FF_", "🖖=> _1F596_", "🖖🏻=> _1F5961F3FB_", "🖖🏼=> _1F5961F3FC_", "🖖🏽=> _1F5961F3FD_", "🖖🏾=> _1F5961F3FE_", "🖖🏿=> _1F5961F3FF_", "👌=> _1F44C_", "👌🏻=> _1F44C1F3FB_", "👌🏼=> _1F44C1F3FC_", "👌🏽=> _1F44C1F3FD_", "👌🏾=> _1F44C1F3FE_", "👌🏿=> _1F44C1F3FF_", "🤌=> _1F90C_", "🤌🏻=> _1F90C1F3FB_", "🤌🏼=> _1F90C1F3FC_", "🤌🏽=> _1F90C1F3FD_", "🤌🏾=> _1F90C1F3FE_", "🤌🏿=> _1F90C1F3FF_", "🤏=> _1F90F_", "🤏🏻=> _1F90F1F3FB_", "🤏🏼=> _1F90F1F3FC_", "🤏🏽=> _1F90F1F3FD_", "🤏🏾=> _1F90F1F3FE_", "🤏🏿=> _1F90F1F3FF_", "✌️=> _270CFE0F_", "✌=> _270C_", "✌🏻=> _270C1F3FB_", "✌🏼=> _270C1F3FC_", "✌🏽=> _270C1F3FD_", "✌🏾=> _270C1F3FE_", "✌🏿=> _270C1F3FF_", "🤞=> _1F91E_", "🤞🏻=> _1F91E1F3FB_", "🤞🏼=> _1F91E1F3FC_", "🤞🏽=> _1F91E1F3FD_", "🤞🏾=> _1F91E1F3FE_", "🤞🏿=> _1F91E1F3FF_", "🤟=> _1F91F_", "🤟🏻=> _1F91F1F3FB_", "🤟🏼=> _1F91F1F3FC_", "🤟🏽=> _1F91F1F3FD_", "🤟🏾=> _1F91F1F3FE_", "🤟🏿=> _1F91F1F3FF_", "🤘=> _1F918_", "🤘🏻=> _1F9181F3FB_", "🤘🏼=> _1F9181F3FC_", "🤘🏽=> _1F9181F3FD_", "🤘🏾=> _1F9181F3FE_", "🤘🏿=> _1F9181F3FF_", "🤙=> _1F919_", "🤙🏻=> _1F9191F3FB_", "🤙🏼=> _1F9191F3FC_", "🤙🏽=> _1F9191F3FD_", "🤙🏾=> _1F9191F3FE_", "🤙🏿=> _1F9191F3FF_", "👈=> _1F448_", "👈🏻=> _1F4481F3FB_", "👈🏼=> _1F4481F3FC_", "👈🏽=> _1F4481F3FD_", "👈🏾=> _1F4481F3FE_", "👈🏿=> _1F4481F3FF_", "👉=> _1F449_", "👉🏻=> _1F4491F3FB_", "👉🏼=> _1F4491F3FC_", "👉🏽=> _1F4491F3FD_", "👉🏾=> _1F4491F3FE_", "👉🏿=> _1F4491F3FF_", "👆=> _1F446_", "👆🏻=> _1F4461F3FB_", "👆🏼=> _1F4461F3FC_", "👆🏽=> _1F4461F3FD_", "👆🏾=> _1F4461F3FE_", "👆🏿=> _1F4461F3FF_", "🖕=> _1F595_", "🖕🏻=> _1F5951F3FB_", "🖕🏼=> _1F5951F3FC_", "🖕🏽=> _1F5951F3FD_", "🖕🏾=> _1F5951F3FE_", "🖕🏿=> _1F5951F3FF_", "👇=> _1F447_", "👇🏻=> _1F4471F3FB_", "👇🏼=> _1F4471F3FC_", "👇🏽=> _1F4471F3FD_", "👇🏾=> _1F4471F3FE_", "👇🏿=> _1F4471F3FF_", "☝️=> _261DFE0F_", "☝=> _261D_", "☝🏻=> _261D1F3FB_", "☝🏼=> _261D1F3FC_", "☝🏽=> _261D1F3FD_", "☝🏾=> _261D1F3FE_", "☝🏿=> _261D1F3FF_", "👍=> _1F44D_", "👍🏻=> _1F44D1F3FB_", "👍🏼=> _1F44D1F3FC_", "👍🏽=> _1F44D1F3FD_", "👍🏾=> _1F44D1F3FE_", "👍🏿=> _1F44D1F3FF_", "👎=> _1F44E_", "👎🏻=> _1F44E1F3FB_", "👎🏼=> _1F44E1F3FC_", "👎🏽=> _1F44E1F3FD_", "👎🏾=> _1F44E1F3FE_", "👎🏿=> _1F44E1F3FF_", "✊=> _270A_", "✊🏻=> _270A1F3FB_", "✊🏼=> _270A1F3FC_", "✊🏽=> _270A1F3FD_", "✊🏾=> _270A1F3FE_", "✊🏿=> _270A1F3FF_", "👊=> _1F44A_", "👊🏻=> _1F44A1F3FB_", "👊🏼=> _1F44A1F3FC_", "👊🏽=> _1F44A1F3FD_", "👊🏾=> _1F44A1F3FE_", "👊🏿=> _1F44A1F3FF_", "🤛=> _1F91B_", "🤛🏻=> _1F91B1F3FB_", "🤛🏼=> _1F91B1F3FC_", "🤛🏽=> _1F91B1F3FD_", "🤛🏾=> _1F91B1F3FE_", "🤛🏿=> _1F91B1F3FF_", "🤜=> _1F91C_", "🤜🏻=> _1F91C1F3FB_", "🤜🏼=> _1F91C1F3FC_", "🤜🏽=> _1F91C1F3FD_", "🤜🏾=> _1F91C1F3FE_", "🤜🏿=> _1F91C1F3FF_", "👏=> _1F44F_", "👏🏻=> _1F44F1F3FB_", "👏🏼=> _1F44F1F3FC_", "👏🏽=> _1F44F1F3FD_", "👏🏾=> _1F44F1F3FE_", "👏🏿=> _1F44F1F3FF_", "🙌=> _1F64C_", "🙌🏻=> _1F64C1F3FB_", "🙌🏼=> _1F64C1F3FC_", "🙌🏽=> _1F64C1F3FD_", "🙌🏾=> _1F64C1F3FE_", "🙌🏿=> _1F64C1F3FF_", "👐=> _1F450_", "👐🏻=> _1F4501F3FB_", "👐🏼=> _1F4501F3FC_", "👐🏽=> _1F4501F3FD_", "👐🏾=> _1F4501F3FE_", "👐🏿=> _1F4501F3FF_", "🤲=> _1F932_", "🤲🏻=> _1F9321F3FB_", "🤲🏼=> _1F9321F3FC_", "🤲🏽=> _1F9321F3FD_", "🤲🏾=> _1F9321F3FE_", "🤲🏿=> _1F9321F3FF_", "🤝=> _1F91D_", "🙏=> _1F64F_", "🙏🏻=> _1F64F1F3FB_", "🙏🏼=> _1F64F1F3FC_", "🙏🏽=> _1F64F1F3FD_", "🙏🏾=> _1F64F1F3FE_", "🙏🏿=> _1F64F1F3FF_", "✍️=> _270DFE0F_", "✍=> _270D_", "✍🏻=> _270D1F3FB_", "✍🏼=> _270D1F3FC_", "✍🏽=> _270D1F3FD_", "✍🏾=> _270D1F3FE_", "✍🏿=> _270D1F3FF_", "💅=> _1F485_", "💅🏻=> _1F4851F3FB_", "💅🏼=> _1F4851F3FC_", "💅🏽=> _1F4851F3FD_", "💅🏾=> _1F4851F3FE_", "💅🏿=> _1F4851F3FF_", "🤳=> _1F933_", "🤳🏻=> _1F9331F3FB_", "🤳🏼=> _1F9331F3FC_", "🤳🏽=> _1F9331F3FD_", "🤳🏾=> _1F9331F3FE_", "🤳🏿=> _1F9331F3FF_", "💪=> _1F4AA_", "💪🏻=> _1F4AA1F3FB_", "💪🏼=> _1F4AA1F3FC_", "💪🏽=> _1F4AA1F3FD_", "💪🏾=> _1F4AA1F3FE_", "💪🏿=> _1F4AA1F3FF_", "🦾=> _1F9BE_", "🦿=> _1F9BF_", "🦵=> _1F9B5_", "🦵🏻=> _1F9B51F3FB_", "🦵🏼=> _1F9B51F3FC_", "🦵🏽=> _1F9B51F3FD_", "🦵🏾=> _1F9B51F3FE_", "🦵🏿=> _1F9B51F3FF_", "🦶=> _1F9B6_", "🦶🏻=> _1F9B61F3FB_", "🦶🏼=> _1F9B61F3FC_", "🦶🏽=> _1F9B61F3FD_", "🦶🏾=> _1F9B61F3FE_", "🦶🏿=> _1F9B61F3FF_", "👂=> _1F442_", "👂🏻=> _1F4421F3FB_", "👂🏼=> _1F4421F3FC_", "👂🏽=> _1F4421F3FD_", "👂🏾=> _1F4421F3FE_", "👂🏿=> _1F4421F3FF_", "🦻=> _1F9BB_", "🦻🏻=> _1F9BB1F3FB_", "🦻🏼=> _1F9BB1F3FC_", "🦻🏽=> _1F9BB1F3FD_", "🦻🏾=> _1F9BB1F3FE_", "🦻🏿=> _1F9BB1F3FF_", "👃=> _1F443_", "👃🏻=> _1F4431F3FB_", "👃🏼=> _1F4431F3FC_", "👃🏽=> _1F4431F3FD_", "👃🏾=> _1F4431F3FE_", "👃🏿=> _1F4431F3FF_", "🧠=> _1F9E0_", "🫀=> _1FAC0_", "🫁=> _1FAC1_", "🦷=> _1F9B7_", "🦴=> _1F9B4_", "👀=> _1F440_", "👁️=> _1F441FE0F_", "👁=> _1F441_", "👅=> _1F445_", "👄=> _1F444_", "👶=> _1F476_", "👶🏻=> _1F4761F3FB_", "👶🏼=> _1F4761F3FC_", "👶🏽=> _1F4761F3FD_", "👶🏾=> _1F4761F3FE_", "👶🏿=> _1F4761F3FF_", "🧒=> _1F9D2_", "🧒🏻=> _1F9D21F3FB_", "🧒🏼=> _1F9D21F3FC_", "🧒🏽=> _1F9D21F3FD_", "🧒🏾=> _1F9D21F3FE_", "🧒🏿=> _1F9D21F3FF_", "👦=> _1F466_", "👦🏻=> _1F4661F3FB_", "👦🏼=> _1F4661F3FC_", "👦🏽=> _1F4661F3FD_", "👦🏾=> _1F4661F3FE_", "👦🏿=> _1F4661F3FF_", "👧=> _1F467_", "👧🏻=> _1F4671F3FB_", "👧🏼=> _1F4671F3FC_", "👧🏽=> _1F4671F3FD_", "👧🏾=> _1F4671F3FE_", "👧🏿=> _1F4671F3FF_", "🧑=> _1F9D1_", "🧑🏻=> _1F9D11F3FB_", "🧑🏼=> _1F9D11F3FC_", "🧑🏽=> _1F9D11F3FD_", "🧑🏾=> _1F9D11F3FE_", "🧑🏿=> _1F9D11F3FF_", "👱=> _1F471_", "👱🏻=> _1F4711F3FB_", "👱🏼=> _1F4711F3FC_", "👱🏽=> _1F4711F3FD_", "👱🏾=> _1F4711F3FE_", "👱🏿=> _1F4711F3FF_", "👨=> _1F468_", "👨🏻=> _1F4681F3FB_", "👨🏼=> _1F4681F3FC_", "👨🏽=> _1F4681F3FD_", "👨🏾=> _1F4681F3FE_", "👨🏿=> _1F4681F3FF_", "🧔=> _1F9D4_", "🧔🏻=> _1F9D41F3FB_", "🧔🏼=> _1F9D41F3FC_", "🧔🏽=> _1F9D41F3FD_", "🧔🏾=> _1F9D41F3FE_", "🧔🏿=> _1F9D41F3FF_", "👨‍🦰=> _1F468200D1F9B0_", "👨🏻‍🦰=> _1F4681F3FB200D1F9B0_", "👨🏼‍🦰=> _1F4681F3FC200D1F9B0_", "👨🏽‍🦰=> _1F4681F3FD200D1F9B0_", "👨🏾‍🦰=> _1F4681F3FE200D1F9B0_", "👨🏿‍🦰=> _1F4681F3FF200D1F9B0_", "👨‍🦱=> _1F468200D1F9B1_", "👨🏻‍🦱=> _1F4681F3FB200D1F9B1_", "👨🏼‍🦱=> _1F4681F3FC200D1F9B1_", "👨🏽‍🦱=> _1F4681F3FD200D1F9B1_", "👨🏾‍🦱=> _1F4681F3FE200D1F9B1_", "👨🏿‍🦱=> _1F4681F3FF200D1F9B1_", "👨‍🦳=> _1F468200D1F9B3_", "👨🏻‍🦳=> _1F4681F3FB200D1F9B3_", "👨🏼‍🦳=> _1F4681F3FC200D1F9B3_", "👨🏽‍🦳=> _1F4681F3FD200D1F9B3_", "👨🏾‍🦳=> _1F4681F3FE200D1F9B3_", "👨🏿‍🦳=> _1F4681F3FF200D1F9B3_", "👨‍🦲=> _1F468200D1F9B2_", "👨🏻‍🦲=> _1F4681F3FB200D1F9B2_", "👨🏼‍🦲=> _1F4681F3FC200D1F9B2_", "👨🏽‍🦲=> _1F4681F3FD200D1F9B2_", "👨🏾‍🦲=> _1F4681F3FE200D1F9B2_", "👨🏿‍🦲=> _1F4681F3FF200D1F9B2_", "👩=> _1F469_", "👩🏻=> _1F4691F3FB_", "👩🏼=> _1F4691F3FC_", "👩🏽=> _1F4691F3FD_", "👩🏾=> _1F4691F3FE_", "👩🏿=> _1F4691F3FF_", "👩‍🦰=> _1F469200D1F9B0_", "👩🏻‍🦰=> _1F4691F3FB200D1F9B0_", "👩🏼‍🦰=> _1F4691F3FC200D1F9B0_", "👩🏽‍🦰=> _1F4691F3FD200D1F9B0_", "👩🏾‍🦰=> _1F4691F3FE200D1F9B0_", "👩🏿‍🦰=> _1F4691F3FF200D1F9B0_", "🧑‍🦰=> _1F9D1200D1F9B0_", "🧑🏻‍🦰=> _1F9D11F3FB200D1F9B0_", "🧑🏼‍🦰=> _1F9D11F3FC200D1F9B0_", "🧑🏽‍🦰=> _1F9D11F3FD200D1F9B0_", "🧑🏾‍🦰=> _1F9D11F3FE200D1F9B0_", "🧑🏿‍🦰=> _1F9D11F3FF200D1F9B0_", "👩‍🦱=> _1F469200D1F9B1_", "👩🏻‍🦱=> _1F4691F3FB200D1F9B1_", "👩🏼‍🦱=> _1F4691F3FC200D1F9B1_", "👩🏽‍🦱=> _1F4691F3FD200D1F9B1_", "👩🏾‍🦱=> _1F4691F3FE200D1F9B1_", "👩🏿‍🦱=> _1F4691F3FF200D1F9B1_", "🧑‍🦱=> _1F9D1200D1F9B1_", "🧑🏻‍🦱=> _1F9D11F3FB200D1F9B1_", "🧑🏼‍🦱=> _1F9D11F3FC200D1F9B1_", "🧑🏽‍🦱=> _1F9D11F3FD200D1F9B1_", "🧑🏾‍🦱=> _1F9D11F3FE200D1F9B1_", "🧑🏿‍🦱=> _1F9D11F3FF200D1F9B1_", "👩‍🦳=> _1F469200D1F9B3_", "👩🏻‍🦳=> _1F4691F3FB200D1F9B3_", "👩🏼‍🦳=> _1F4691F3FC200D1F9B3_", "👩🏽‍🦳=> _1F4691F3FD200D1F9B3_", "👩🏾‍🦳=> _1F4691F3FE200D1F9B3_", "👩🏿‍🦳=> _1F4691F3FF200D1F9B3_", "🧑‍🦳=> _1F9D1200D1F9B3_", "🧑🏻‍🦳=> _1F9D11F3FB200D1F9B3_", "🧑🏼‍🦳=> _1F9D11F3FC200D1F9B3_", "🧑🏽‍🦳=> _1F9D11F3FD200D1F9B3_", "🧑🏾‍🦳=> _1F9D11F3FE200D1F9B3_", "🧑🏿‍🦳=> _1F9D11F3FF200D1F9B3_", "👩‍🦲=> _1F469200D1F9B2_", "👩🏻‍🦲=> _1F4691F3FB200D1F9B2_", "👩🏼‍🦲=> _1F4691F3FC200D1F9B2_", "👩🏽‍🦲=> _1F4691F3FD200D1F9B2_", "👩🏾‍🦲=> _1F4691F3FE200D1F9B2_", "👩🏿‍🦲=> _1F4691F3FF200D1F9B2_", "🧑‍🦲=> _1F9D1200D1F9B2_", "🧑🏻‍🦲=> _1F9D11F3FB200D1F9B2_", "🧑🏼‍🦲=> _1F9D11F3FC200D1F9B2_", "🧑🏽‍🦲=> _1F9D11F3FD200D1F9B2_", "🧑🏾‍🦲=> _1F9D11F3FE200D1F9B2_", "🧑🏿‍🦲=> _1F9D11F3FF200D1F9B2_", "👱‍♀️=> _1F471200D2640FE0F_", "👱🏻‍♀️=> _1F4711F3FB200D2640FE0F_", "👱🏼‍♀️=> _1F4711F3FC200D2640FE0F_", "👱🏽‍♀️=> _1F4711F3FD200D2640FE0F_", "👱🏾‍♀️=> _1F4711F3FE200D2640FE0F_", "👱🏿‍♀️=> _1F4711F3FF200D2640FE0F_", "👱‍♂️=> _1F471200D2642FE0F_", "👱🏻‍♂️=> _1F4711F3FB200D2642FE0F_", "👱🏼‍♂️=> _1F4711F3FC200D2642FE0F_", "👱🏽‍♂️=> _1F4711F3FD200D2642FE0F_", "👱🏾‍♂️=> _1F4711F3FE200D2642FE0F_", "👱🏿‍♂️=> _1F4711F3FF200D2642FE0F_", "🧓=> _1F9D3_", "🧓🏻=> _1F9D31F3FB_", "🧓🏼=> _1F9D31F3FC_", "🧓🏽=> _1F9D31F3FD_", "🧓🏾=> _1F9D31F3FE_", "🧓🏿=> _1F9D31F3FF_", "👴=> _1F474_", "👴🏻=> _1F4741F3FB_", "👴🏼=> _1F4741F3FC_", "👴🏽=> _1F4741F3FD_", "👴🏾=> _1F4741F3FE_", "👴🏿=> _1F4741F3FF_", "👵=> _1F475_", "👵🏻=> _1F4751F3FB_", "👵🏼=> _1F4751F3FC_", "👵🏽=> _1F4751F3FD_", "👵🏾=> _1F4751F3FE_", "👵🏿=> _1F4751F3FF_", "🙍=> _1F64D_", "🙍🏻=> _1F64D1F3FB_", "🙍🏼=> _1F64D1F3FC_", "🙍🏽=> _1F64D1F3FD_", "🙍🏾=> _1F64D1F3FE_", "🙍🏿=> _1F64D1F3FF_", "🙍‍♂️=> _1F64D200D2642FE0F_", "🙍🏻‍♂️=> _1F64D1F3FB200D2642FE0F_", "🙍🏼‍♂️=> _1F64D1F3FC200D2642FE0F_", "🙍🏽‍♂️=> _1F64D1F3FD200D2642FE0F_", "🙍🏾‍♂️=> _1F64D1F3FE200D2642FE0F_", "🙍🏿‍♂️=> _1F64D1F3FF200D2642FE0F_", "🙍‍♀️=> _1F64D200D2640FE0F_", "🙍🏻‍♀️=> _1F64D1F3FB200D2640FE0F_", "🙍🏼‍♀️=> _1F64D1F3FC200D2640FE0F_", "🙍🏽‍♀️=> _1F64D1F3FD200D2640FE0F_", "🙍🏾‍♀️=> _1F64D1F3FE200D2640FE0F_", "🙍🏿‍♀️=> _1F64D1F3FF200D2640FE0F_", "🙎=> _1F64E_", "🙎🏻=> _1F64E1F3FB_", "🙎🏼=> _1F64E1F3FC_", "🙎🏽=> _1F64E1F3FD_", "🙎🏾=> _1F64E1F3FE_", "🙎🏿=> _1F64E1F3FF_", "🙎‍♂️=> _1F64E200D2642FE0F_", "🙎🏻‍♂️=> _1F64E1F3FB200D2642FE0F_", "🙎🏼‍♂️=> _1F64E1F3FC200D2642FE0F_", "🙎🏽‍♂️=> _1F64E1F3FD200D2642FE0F_", "🙎🏾‍♂️=> _1F64E1F3FE200D2642FE0F_", "🙎🏿‍♂️=> _1F64E1F3FF200D2642FE0F_", "🙎‍♀️=> _1F64E200D2640FE0F_", "🙎🏻‍♀️=> _1F64E1F3FB200D2640FE0F_", "🙎🏼‍♀️=> _1F64E1F3FC200D2640FE0F_", "🙎🏽‍♀️=> _1F64E1F3FD200D2640FE0F_", "🙎🏾‍♀️=> _1F64E1F3FE200D2640FE0F_", "🙎🏿‍♀️=> _1F64E1F3FF200D2640FE0F_", "🙅=> _1F645_", "🙅🏻=> _1F6451F3FB_", "🙅🏼=> _1F6451F3FC_", "🙅🏽=> _1F6451F3FD_", "🙅🏾=> _1F6451F3FE_", "🙅🏿=> _1F6451F3FF_", "🙅‍♂️=> _1F645200D2642FE0F_", "🙅🏻‍♂️=> _1F6451F3FB200D2642FE0F_", "🙅🏼‍♂️=> _1F6451F3FC200D2642FE0F_", "🙅🏽‍♂️=> _1F6451F3FD200D2642FE0F_", "🙅🏾‍♂️=> _1F6451F3FE200D2642FE0F_", "🙅🏿‍♂️=> _1F6451F3FF200D2642FE0F_", "🙅‍♀️=> _1F645200D2640FE0F_", "🙅🏻‍♀️=> _1F6451F3FB200D2640FE0F_", "🙅🏼‍♀️=> _1F6451F3FC200D2640FE0F_", "🙅🏽‍♀️=> _1F6451F3FD200D2640FE0F_", "🙅🏾‍♀️=> _1F6451F3FE200D2640FE0F_", "🙅🏿‍♀️=> _1F6451F3FF200D2640FE0F_", "🙆=> _1F646_", "🙆🏻=> _1F6461F3FB_", "🙆🏼=> _1F6461F3FC_", "🙆🏽=> _1F6461F3FD_", "🙆🏾=> _1F6461F3FE_", "🙆🏿=> _1F6461F3FF_", "🙆‍♂️=> _1F646200D2642FE0F_", "🙆🏻‍♂️=> _1F6461F3FB200D2642FE0F_", "🙆🏼‍♂️=> _1F6461F3FC200D2642FE0F_", "🙆🏽‍♂️=> _1F6461F3FD200D2642FE0F_", "🙆🏾‍♂️=> _1F6461F3FE200D2642FE0F_", "🙆🏿‍♂️=> _1F6461F3FF200D2642FE0F_", "🙆‍♀️=> _1F646200D2640FE0F_", "🙆🏻‍♀️=> _1F6461F3FB200D2640FE0F_", "🙆🏼‍♀️=> _1F6461F3FC200D2640FE0F_", "🙆🏽‍♀️=> _1F6461F3FD200D2640FE0F_", "🙆🏾‍♀️=> _1F6461F3FE200D2640FE0F_", "🙆🏿‍♀️=> _1F6461F3FF200D2640FE0F_", "💁=> _1F481_", "💁🏻=> _1F4811F3FB_", "💁🏼=> _1F4811F3FC_", "💁🏽=> _1F4811F3FD_", "💁🏾=> _1F4811F3FE_", "💁🏿=> _1F4811F3FF_", "💁‍♂️=> _1F481200D2642FE0F_", "💁🏻‍♂️=> _1F4811F3FB200D2642FE0F_", "💁🏼‍♂️=> _1F4811F3FC200D2642FE0F_", "💁🏽‍♂️=> _1F4811F3FD200D2642FE0F_", "💁🏾‍♂️=> _1F4811F3FE200D2642FE0F_", "💁🏿‍♂️=> _1F4811F3FF200D2642FE0F_", "💁‍♀️=> _1F481200D2640FE0F_", "💁🏻‍♀️=> _1F4811F3FB200D2640FE0F_", "💁🏼‍♀️=> _1F4811F3FC200D2640FE0F_", "💁🏽‍♀️=> _1F4811F3FD200D2640FE0F_", "💁🏾‍♀️=> _1F4811F3FE200D2640FE0F_", "💁🏿‍♀️=> _1F4811F3FF200D2640FE0F_", "🙋=> _1F64B_", "🙋🏻=> _1F64B1F3FB_", "🙋🏼=> _1F64B1F3FC_", "🙋🏽=> _1F64B1F3FD_", "🙋🏾=> _1F64B1F3FE_", "🙋🏿=> _1F64B1F3FF_", "🙋‍♂️=> _1F64B200D2642FE0F_", "🙋🏻‍♂️=> _1F64B1F3FB200D2642FE0F_", "🙋🏼‍♂️=> _1F64B1F3FC200D2642FE0F_", "🙋🏽‍♂️=> _1F64B1F3FD200D2642FE0F_", "🙋🏾‍♂️=> _1F64B1F3FE200D2642FE0F_", "🙋🏿‍♂️=> _1F64B1F3FF200D2642FE0F_", "🙋‍♀️=> _1F64B200D2640FE0F_", "🙋🏻‍♀️=> _1F64B1F3FB200D2640FE0F_", "🙋🏼‍♀️=> _1F64B1F3FC200D2640FE0F_", "🙋🏽‍♀️=> _1F64B1F3FD200D2640FE0F_", "🙋🏾‍♀️=> _1F64B1F3FE200D2640FE0F_", "🙋🏿‍♀️=> _1F64B1F3FF200D2640FE0F_", "🧏=> _1F9CF_", "🧏🏻=> _1F9CF1F3FB_", "🧏🏼=> _1F9CF1F3FC_", "🧏🏽=> _1F9CF1F3FD_", "🧏🏾=> _1F9CF1F3FE_", "🧏🏿=> _1F9CF1F3FF_", "🧏‍♂️=> _1F9CF200D2642FE0F_", "🧏🏻‍♂️=> _1F9CF1F3FB200D2642FE0F_", "🧏🏼‍♂️=> _1F9CF1F3FC200D2642FE0F_", "🧏🏽‍♂️=> _1F9CF1F3FD200D2642FE0F_", "🧏🏾‍♂️=> _1F9CF1F3FE200D2642FE0F_", "🧏🏿‍♂️=> _1F9CF1F3FF200D2642FE0F_", "🧏‍♀️=> _1F9CF200D2640FE0F_", "🧏🏻‍♀️=> _1F9CF1F3FB200D2640FE0F_", "🧏🏼‍♀️=> _1F9CF1F3FC200D2640FE0F_", "🧏🏽‍♀️=> _1F9CF1F3FD200D2640FE0F_", "🧏🏾‍♀️=> _1F9CF1F3FE200D2640FE0F_", "🧏🏿‍♀️=> _1F9CF1F3FF200D2640FE0F_", "🙇=> _1F647_", "🙇🏻=> _1F6471F3FB_", "🙇🏼=> _1F6471F3FC_", "🙇🏽=> _1F6471F3FD_", "🙇🏾=> _1F6471F3FE_", "🙇🏿=> _1F6471F3FF_", "🙇‍♂️=> _1F647200D2642FE0F_", "🙇🏻‍♂️=> _1F6471F3FB200D2642FE0F_", "🙇🏼‍♂️=> _1F6471F3FC200D2642FE0F_", "🙇🏽‍♂️=> _1F6471F3FD200D2642FE0F_", "🙇🏾‍♂️=> _1F6471F3FE200D2642FE0F_", "🙇🏿‍♂️=> _1F6471F3FF200D2642FE0F_", "🙇‍♀️=> _1F647200D2640FE0F_", "🙇🏻‍♀️=> _1F6471F3FB200D2640FE0F_", "🙇🏼‍♀️=> _1F6471F3FC200D2640FE0F_", "🙇🏽‍♀️=> _1F6471F3FD200D2640FE0F_", "🙇🏾‍♀️=> _1F6471F3FE200D2640FE0F_", "🙇🏿‍♀️=> _1F6471F3FF200D2640FE0F_", "🤦=> _1F926_", "🤦🏻=> _1F9261F3FB_", "🤦🏼=> _1F9261F3FC_", "🤦🏽=> _1F9261F3FD_", "🤦🏾=> _1F9261F3FE_", "🤦🏿=> _1F9261F3FF_", "🤦‍♂️=> _1F926200D2642FE0F_", "🤦🏻‍♂️=> _1F9261F3FB200D2642FE0F_", "🤦🏼‍♂️=> _1F9261F3FC200D2642FE0F_", "🤦🏽‍♂️=> _1F9261F3FD200D2642FE0F_", "🤦🏾‍♂️=> _1F9261F3FE200D2642FE0F_", "🤦🏿‍♂️=> _1F9261F3FF200D2642FE0F_", "🤦‍♀️=> _1F926200D2640FE0F_", "🤦🏻‍♀️=> _1F9261F3FB200D2640FE0F_", "🤦🏼‍♀️=> _1F9261F3FC200D2640FE0F_", "🤦🏽‍♀️=> _1F9261F3FD200D2640FE0F_", "🤦🏾‍♀️=> _1F9261F3FE200D2640FE0F_", "🤦🏿‍♀️=> _1F9261F3FF200D2640FE0F_", "🤷=> _1F937_", "🤷🏻=> _1F9371F3FB_", "🤷🏼=> _1F9371F3FC_", "🤷🏽=> _1F9371F3FD_", "🤷🏾=> _1F9371F3FE_", "🤷🏿=> _1F9371F3FF_", "🤷‍♂️=> _1F937200D2642FE0F_", "🤷🏻‍♂️=> _1F9371F3FB200D2642FE0F_", "🤷🏼‍♂️=> _1F9371F3FC200D2642FE0F_", "🤷🏽‍♂️=> _1F9371F3FD200D2642FE0F_", "🤷🏾‍♂️=> _1F9371F3FE200D2642FE0F_", "🤷🏿‍♂️=> _1F9371F3FF200D2642FE0F_", "🤷‍♀️=> _1F937200D2640FE0F_", "🤷🏻‍♀️=> _1F9371F3FB200D2640FE0F_", "🤷🏼‍♀️=> _1F9371F3FC200D2640FE0F_", "🤷🏽‍♀️=> _1F9371F3FD200D2640FE0F_", "🤷🏾‍♀️=> _1F9371F3FE200D2640FE0F_", "🤷🏿‍♀️=> _1F9371F3FF200D2640FE0F_", "🧑‍⚕️=> _1F9D1200D2695FE0F_", "🧑🏻‍⚕️=> _1F9D11F3FB200D2695FE0F_", "🧑🏼‍⚕️=> _1F9D11F3FC200D2695FE0F_", "🧑🏽‍⚕️=> _1F9D11F3FD200D2695FE0F_", "🧑🏾‍⚕️=> _1F9D11F3FE200D2695FE0F_", "🧑🏿‍⚕️=> _1F9D11F3FF200D2695FE0F_", "👨‍⚕️=> _1F468200D2695FE0F_", "👨🏻‍⚕️=> _1F4681F3FB200D2695FE0F_", "👨🏼‍⚕️=> _1F4681F3FC200D2695FE0F_", "👨🏽‍⚕️=> _1F4681F3FD200D2695FE0F_", "👨🏾‍⚕️=> _1F4681F3FE200D2695FE0F_", "👨🏿‍⚕️=> _1F4681F3FF200D2695FE0F_", "👩‍⚕️=> _1F469200D2695FE0F_", "👩🏻‍⚕️=> _1F4691F3FB200D2695FE0F_", "👩🏼‍⚕️=> _1F4691F3FC200D2695FE0F_", "👩🏽‍⚕️=> _1F4691F3FD200D2695FE0F_", "👩🏾‍⚕️=> _1F4691F3FE200D2695FE0F_", "👩🏿‍⚕️=> _1F4691F3FF200D2695FE0F_", "🧑‍🎓=> _1F9D1200D1F393_", "🧑🏻‍🎓=> _1F9D11F3FB200D1F393_", "🧑🏼‍🎓=> _1F9D11F3FC200D1F393_", "🧑🏽‍🎓=> _1F9D11F3FD200D1F393_", "🧑🏾‍🎓=> _1F9D11F3FE200D1F393_", "🧑🏿‍🎓=> _1F9D11F3FF200D1F393_", "👨‍🎓=> _1F468200D1F393_", "👨🏻‍🎓=> _1F4681F3FB200D1F393_", "👨🏼‍🎓=> _1F4681F3FC200D1F393_", "👨🏽‍🎓=> _1F4681F3FD200D1F393_", "👨🏾‍🎓=> _1F4681F3FE200D1F393_", "👨🏿‍🎓=> _1F4681F3FF200D1F393_", "👩‍🎓=> _1F469200D1F393_", "👩🏻‍🎓=> _1F4691F3FB200D1F393_", "👩🏼‍🎓=> _1F4691F3FC200D1F393_", "👩🏽‍🎓=> _1F4691F3FD200D1F393_", "👩🏾‍🎓=> _1F4691F3FE200D1F393_", "👩🏿‍🎓=> _1F4691F3FF200D1F393_", "🧑‍🏫=> _1F9D1200D1F3EB_", "🧑🏻‍🏫=> _1F9D11F3FB200D1F3EB_", "🧑🏼‍🏫=> _1F9D11F3FC200D1F3EB_", "🧑🏽‍🏫=> _1F9D11F3FD200D1F3EB_", "🧑🏾‍🏫=> _1F9D11F3FE200D1F3EB_", "🧑🏿‍🏫=> _1F9D11F3FF200D1F3EB_", "👨‍🏫=> _1F468200D1F3EB_", "👨🏻‍🏫=> _1F4681F3FB200D1F3EB_", "👨🏼‍🏫=> _1F4681F3FC200D1F3EB_", "👨🏽‍🏫=> _1F4681F3FD200D1F3EB_", "👨🏾‍🏫=> _1F4681F3FE200D1F3EB_", "👨🏿‍🏫=> _1F4681F3FF200D1F3EB_", "👩‍🏫=> _1F469200D1F3EB_", "👩🏻‍🏫=> _1F4691F3FB200D1F3EB_", "👩🏼‍🏫=> _1F4691F3FC200D1F3EB_", "👩🏽‍🏫=> _1F4691F3FD200D1F3EB_", "👩🏾‍🏫=> _1F4691F3FE200D1F3EB_", "👩🏿‍🏫=> _1F4691F3FF200D1F3EB_", "🧑‍⚖️=> _1F9D1200D2696FE0F_", "🧑🏻‍⚖️=> _1F9D11F3FB200D2696FE0F_", "🧑🏼‍⚖️=> _1F9D11F3FC200D2696FE0F_", "🧑🏽‍⚖️=> _1F9D11F3FD200D2696FE0F_", "🧑🏾‍⚖️=> _1F9D11F3FE200D2696FE0F_", "🧑🏿‍⚖️=> _1F9D11F3FF200D2696FE0F_", "👨‍⚖️=> _1F468200D2696FE0F_", "👨🏻‍⚖️=> _1F4681F3FB200D2696FE0F_", "👨🏼‍⚖️=> _1F4681F3FC200D2696FE0F_", "👨🏽‍⚖️=> _1F4681F3FD200D2696FE0F_", "👨🏾‍⚖️=> _1F4681F3FE200D2696FE0F_", "👨🏿‍⚖️=> _1F4681F3FF200D2696FE0F_", "👩‍⚖️=> _1F469200D2696FE0F_", "👩🏻‍⚖️=> _1F4691F3FB200D2696FE0F_", "👩🏼‍⚖️=> _1F4691F3FC200D2696FE0F_", "👩🏽‍⚖️=> _1F4691F3FD200D2696FE0F_", "👩🏾‍⚖️=> _1F4691F3FE200D2696FE0F_", "👩🏿‍⚖️=> _1F4691F3FF200D2696FE0F_", "🧑‍🌾=> _1F9D1200D1F33E_", "🧑🏻‍🌾=> _1F9D11F3FB200D1F33E_", "🧑🏼‍🌾=> _1F9D11F3FC200D1F33E_", "🧑🏽‍🌾=> _1F9D11F3FD200D1F33E_", "🧑🏾‍🌾=> _1F9D11F3FE200D1F33E_", "🧑🏿‍🌾=> _1F9D11F3FF200D1F33E_", "👨‍🌾=> _1F468200D1F33E_", "👨🏻‍🌾=> _1F4681F3FB200D1F33E_", "👨🏼‍🌾=> _1F4681F3FC200D1F33E_", "👨🏽‍🌾=> _1F4681F3FD200D1F33E_", "👨🏾‍🌾=> _1F4681F3FE200D1F33E_", "👨🏿‍🌾=> _1F4681F3FF200D1F33E_", "👩‍🌾=> _1F469200D1F33E_", "👩🏻‍🌾=> _1F4691F3FB200D1F33E_", "👩🏼‍🌾=> _1F4691F3FC200D1F33E_", "👩🏽‍🌾=> _1F4691F3FD200D1F33E_", "👩🏾‍🌾=> _1F4691F3FE200D1F33E_", "👩🏿‍🌾=> _1F4691F3FF200D1F33E_", "🧑‍🍳=> _1F9D1200D1F373_", "🧑🏻‍🍳=> _1F9D11F3FB200D1F373_", "🧑🏼‍🍳=> _1F9D11F3FC200D1F373_", "🧑🏽‍🍳=> _1F9D11F3FD200D1F373_", "🧑🏾‍🍳=> _1F9D11F3FE200D1F373_", "🧑🏿‍🍳=> _1F9D11F3FF200D1F373_", "👨‍🍳=> _1F468200D1F373_", "👨🏻‍🍳=> _1F4681F3FB200D1F373_", "👨🏼‍🍳=> _1F4681F3FC200D1F373_", "👨🏽‍🍳=> _1F4681F3FD200D1F373_", "👨🏾‍🍳=> _1F4681F3FE200D1F373_", "👨🏿‍🍳=> _1F4681F3FF200D1F373_", "👩‍🍳=> _1F469200D1F373_", "👩🏻‍🍳=> _1F4691F3FB200D1F373_", "👩🏼‍🍳=> _1F4691F3FC200D1F373_", "👩🏽‍🍳=> _1F4691F3FD200D1F373_", "👩🏾‍🍳=> _1F4691F3FE200D1F373_", "👩🏿‍🍳=> _1F4691F3FF200D1F373_", "🧑‍🔧=> _1F9D1200D1F527_", "🧑🏻‍🔧=> _1F9D11F3FB200D1F527_", "🧑🏼‍🔧=> _1F9D11F3FC200D1F527_", "🧑🏽‍🔧=> _1F9D11F3FD200D1F527_", "🧑🏾‍🔧=> _1F9D11F3FE200D1F527_", "🧑🏿‍🔧=> _1F9D11F3FF200D1F527_", "👨‍🔧=> _1F468200D1F527_", "👨🏻‍🔧=> _1F4681F3FB200D1F527_", "👨🏼‍🔧=> _1F4681F3FC200D1F527_", "👨🏽‍🔧=> _1F4681F3FD200D1F527_", "👨🏾‍🔧=> _1F4681F3FE200D1F527_", "👨🏿‍🔧=> _1F4681F3FF200D1F527_", "👩‍🔧=> _1F469200D1F527_", "👩🏻‍🔧=> _1F4691F3FB200D1F527_", "👩🏼‍🔧=> _1F4691F3FC200D1F527_", "👩🏽‍🔧=> _1F4691F3FD200D1F527_", "👩🏾‍🔧=> _1F4691F3FE200D1F527_", "👩🏿‍🔧=> _1F4691F3FF200D1F527_", "🧑‍🏭=> _1F9D1200D1F3ED_", "🧑🏻‍🏭=> _1F9D11F3FB200D1F3ED_", "🧑🏼‍🏭=> _1F9D11F3FC200D1F3ED_", "🧑🏽‍🏭=> _1F9D11F3FD200D1F3ED_", "🧑🏾‍🏭=> _1F9D11F3FE200D1F3ED_", "🧑🏿‍🏭=> _1F9D11F3FF200D1F3ED_", "👨‍🏭=> _1F468200D1F3ED_", "👨🏻‍🏭=> _1F4681F3FB200D1F3ED_", "👨🏼‍🏭=> _1F4681F3FC200D1F3ED_", "👨🏽‍🏭=> _1F4681F3FD200D1F3ED_", "👨🏾‍🏭=> _1F4681F3FE200D1F3ED_", "👨🏿‍🏭=> _1F4681F3FF200D1F3ED_", "👩‍🏭=> _1F469200D1F3ED_", "👩🏻‍🏭=> _1F4691F3FB200D1F3ED_", "👩🏼‍🏭=> _1F4691F3FC200D1F3ED_", "👩🏽‍🏭=> _1F4691F3FD200D1F3ED_", "👩🏾‍🏭=> _1F4691F3FE200D1F3ED_", "👩🏿‍🏭=> _1F4691F3FF200D1F3ED_", "🧑‍💼=> _1F9D1200D1F4BC_", "🧑🏻‍💼=> _1F9D11F3FB200D1F4BC_", "🧑🏼‍💼=> _1F9D11F3FC200D1F4BC_", "🧑🏽‍💼=> _1F9D11F3FD200D1F4BC_", "🧑🏾‍💼=> _1F9D11F3FE200D1F4BC_", "🧑🏿‍💼=> _1F9D11F3FF200D1F4BC_", "👨‍💼=> _1F468200D1F4BC_", "👨🏻‍💼=> _1F4681F3FB200D1F4BC_", "👨🏼‍💼=> _1F4681F3FC200D1F4BC_", "👨🏽‍💼=> _1F4681F3FD200D1F4BC_", "👨🏾‍💼=> _1F4681F3FE200D1F4BC_", "👨🏿‍💼=> _1F4681F3FF200D1F4BC_", "👩‍💼=> _1F469200D1F4BC_", "👩🏻‍💼=> _1F4691F3FB200D1F4BC_", "👩🏼‍💼=> _1F4691F3FC200D1F4BC_", "👩🏽‍💼=> _1F4691F3FD200D1F4BC_", "👩🏾‍💼=> _1F4691F3FE200D1F4BC_", "👩🏿‍💼=> _1F4691F3FF200D1F4BC_", "🧑‍🔬=> _1F9D1200D1F52C_", "🧑🏻‍🔬=> _1F9D11F3FB200D1F52C_", "🧑🏼‍🔬=> _1F9D11F3FC200D1F52C_", "🧑🏽‍🔬=> _1F9D11F3FD200D1F52C_", "🧑🏾‍🔬=> _1F9D11F3FE200D1F52C_", "🧑🏿‍🔬=> _1F9D11F3FF200D1F52C_", "👨‍🔬=> _1F468200D1F52C_", "👨🏻‍🔬=> _1F4681F3FB200D1F52C_", "👨🏼‍🔬=> _1F4681F3FC200D1F52C_", "👨🏽‍🔬=> _1F4681F3FD200D1F52C_", "👨🏾‍🔬=> _1F4681F3FE200D1F52C_", "👨🏿‍🔬=> _1F4681F3FF200D1F52C_", "👩‍🔬=> _1F469200D1F52C_", "👩🏻‍🔬=> _1F4691F3FB200D1F52C_", "👩🏼‍🔬=> _1F4691F3FC200D1F52C_", "👩🏽‍🔬=> _1F4691F3FD200D1F52C_", "👩🏾‍🔬=> _1F4691F3FE200D1F52C_", "👩🏿‍🔬=> _1F4691F3FF200D1F52C_", "🧑‍💻=> _1F9D1200D1F4BB_", "🧑🏻‍💻=> _1F9D11F3FB200D1F4BB_", "🧑🏼‍💻=> _1F9D11F3FC200D1F4BB_", "🧑🏽‍💻=> _1F9D11F3FD200D1F4BB_", "🧑🏾‍💻=> _1F9D11F3FE200D1F4BB_", "🧑🏿‍💻=> _1F9D11F3FF200D1F4BB_", "👨‍💻=> _1F468200D1F4BB_", "👨🏻‍💻=> _1F4681F3FB200D1F4BB_", "👨🏼‍💻=> _1F4681F3FC200D1F4BB_", "👨🏽‍💻=> _1F4681F3FD200D1F4BB_", "👨🏾‍💻=> _1F4681F3FE200D1F4BB_", "👨🏿‍💻=> _1F4681F3FF200D1F4BB_", "👩‍💻=> _1F469200D1F4BB_", "👩🏻‍💻=> _1F4691F3FB200D1F4BB_", "👩🏼‍💻=> _1F4691F3FC200D1F4BB_", "👩🏽‍💻=> _1F4691F3FD200D1F4BB_", "👩🏾‍💻=> _1F4691F3FE200D1F4BB_", "👩🏿‍💻=> _1F4691F3FF200D1F4BB_", "🧑‍🎤=> _1F9D1200D1F3A4_", "🧑🏻‍🎤=> _1F9D11F3FB200D1F3A4_", "🧑🏼‍🎤=> _1F9D11F3FC200D1F3A4_", "🧑🏽‍🎤=> _1F9D11F3FD200D1F3A4_", "🧑🏾‍🎤=> _1F9D11F3FE200D1F3A4_", "🧑🏿‍🎤=> _1F9D11F3FF200D1F3A4_", "👨‍🎤=> _1F468200D1F3A4_", "👨🏻‍🎤=> _1F4681F3FB200D1F3A4_", "👨🏼‍🎤=> _1F4681F3FC200D1F3A4_", "👨🏽‍🎤=> _1F4681F3FD200D1F3A4_", "👨🏾‍🎤=> _1F4681F3FE200D1F3A4_", "👨🏿‍🎤=> _1F4681F3FF200D1F3A4_", "👩‍🎤=> _1F469200D1F3A4_", "👩🏻‍🎤=> _1F4691F3FB200D1F3A4_", "👩🏼‍🎤=> _1F4691F3FC200D1F3A4_", "👩🏽‍🎤=> _1F4691F3FD200D1F3A4_", "👩🏾‍🎤=> _1F4691F3FE200D1F3A4_", "👩🏿‍🎤=> _1F4691F3FF200D1F3A4_", "🧑‍🎨=> _1F9D1200D1F3A8_", "🧑🏻‍🎨=> _1F9D11F3FB200D1F3A8_", "🧑🏼‍🎨=> _1F9D11F3FC200D1F3A8_", "🧑🏽‍🎨=> _1F9D11F3FD200D1F3A8_", "🧑🏾‍🎨=> _1F9D11F3FE200D1F3A8_", "🧑🏿‍🎨=> _1F9D11F3FF200D1F3A8_", "👨‍🎨=> _1F468200D1F3A8_", "👨🏻‍🎨=> _1F4681F3FB200D1F3A8_", "👨🏼‍🎨=> _1F4681F3FC200D1F3A8_", "👨🏽‍🎨=> _1F4681F3FD200D1F3A8_", "👨🏾‍🎨=> _1F4681F3FE200D1F3A8_", "👨🏿‍🎨=> _1F4681F3FF200D1F3A8_", "👩‍🎨=> _1F469200D1F3A8_", "👩🏻‍🎨=> _1F4691F3FB200D1F3A8_", "👩🏼‍🎨=> _1F4691F3FC200D1F3A8_", "👩🏽‍🎨=> _1F4691F3FD200D1F3A8_", "👩🏾‍🎨=> _1F4691F3FE200D1F3A8_", "👩🏿‍🎨=> _1F4691F3FF200D1F3A8_", "🧑‍✈️=> _1F9D1200D2708FE0F_", "🧑🏻‍✈️=> _1F9D11F3FB200D2708FE0F_", "🧑🏼‍✈️=> _1F9D11F3FC200D2708FE0F_", "🧑🏽‍✈️=> _1F9D11F3FD200D2708FE0F_", "🧑🏾‍✈️=> _1F9D11F3FE200D2708FE0F_", "🧑🏿‍✈️=> _1F9D11F3FF200D2708FE0F_", "👨‍✈️=> _1F468200D2708FE0F_", "👨🏻‍✈️=> _1F4681F3FB200D2708FE0F_", "👨🏼‍✈️=> _1F4681F3FC200D2708FE0F_", "👨🏽‍✈️=> _1F4681F3FD200D2708FE0F_", "👨🏾‍✈️=> _1F4681F3FE200D2708FE0F_", "👨🏿‍✈️=> _1F4681F3FF200D2708FE0F_", "👩‍✈️=> _1F469200D2708FE0F_", "👩🏻‍✈️=> _1F4691F3FB200D2708FE0F_", "👩🏼‍✈️=> _1F4691F3FC200D2708FE0F_", "👩🏽‍✈️=> _1F4691F3FD200D2708FE0F_", "👩🏾‍✈️=> _1F4691F3FE200D2708FE0F_", "👩🏿‍✈️=> _1F4691F3FF200D2708FE0F_", "🧑‍🚀=> _1F9D1200D1F680_", "🧑🏻‍🚀=> _1F9D11F3FB200D1F680_", "🧑🏼‍🚀=> _1F9D11F3FC200D1F680_", "🧑🏽‍🚀=> _1F9D11F3FD200D1F680_", "🧑🏾‍🚀=> _1F9D11F3FE200D1F680_", "🧑🏿‍🚀=> _1F9D11F3FF200D1F680_", "👨‍🚀=> _1F468200D1F680_", "👨🏻‍🚀=> _1F4681F3FB200D1F680_", "👨🏼‍🚀=> _1F4681F3FC200D1F680_", "👨🏽‍🚀=> _1F4681F3FD200D1F680_", "👨🏾‍🚀=> _1F4681F3FE200D1F680_", "👨🏿‍🚀=> _1F4681F3FF200D1F680_", "👩‍🚀=> _1F469200D1F680_", "👩🏻‍🚀=> _1F4691F3FB200D1F680_", "👩🏼‍🚀=> _1F4691F3FC200D1F680_", "👩🏽‍🚀=> _1F4691F3FD200D1F680_", "👩🏾‍🚀=> _1F4691F3FE200D1F680_", "👩🏿‍🚀=> _1F4691F3FF200D1F680_", "🧑‍🚒=> _1F9D1200D1F692_", "🧑🏻‍🚒=> _1F9D11F3FB200D1F692_", "🧑🏼‍🚒=> _1F9D11F3FC200D1F692_", "🧑🏽‍🚒=> _1F9D11F3FD200D1F692_", "🧑🏾‍🚒=> _1F9D11F3FE200D1F692_", "🧑🏿‍🚒=> _1F9D11F3FF200D1F692_", "👨‍🚒=> _1F468200D1F692_", "👨🏻‍🚒=> _1F4681F3FB200D1F692_", "👨🏼‍🚒=> _1F4681F3FC200D1F692_", "👨🏽‍🚒=> _1F4681F3FD200D1F692_", "👨🏾‍🚒=> _1F4681F3FE200D1F692_", "👨🏿‍🚒=> _1F4681F3FF200D1F692_", "👩‍🚒=> _1F469200D1F692_", "👩🏻‍🚒=> _1F4691F3FB200D1F692_", "👩🏼‍🚒=> _1F4691F3FC200D1F692_", "👩🏽‍🚒=> _1F4691F3FD200D1F692_", "👩🏾‍🚒=> _1F4691F3FE200D1F692_", "👩🏿‍🚒=> _1F4691F3FF200D1F692_", "👮=> _1F46E_", "👮🏻=> _1F46E1F3FB_", "👮🏼=> _1F46E1F3FC_", "👮🏽=> _1F46E1F3FD_", "👮🏾=> _1F46E1F3FE_", "👮🏿=> _1F46E1F3FF_", "👮‍♂️=> _1F46E200D2642FE0F_", "👮🏻‍♂️=> _1F46E1F3FB200D2642FE0F_", "👮🏼‍♂️=> _1F46E1F3FC200D2642FE0F_", "👮🏽‍♂️=> _1F46E1F3FD200D2642FE0F_", "👮🏾‍♂️=> _1F46E1F3FE200D2642FE0F_", "👮🏿‍♂️=> _1F46E1F3FF200D2642FE0F_", "👮‍♀️=> _1F46E200D2640FE0F_", "👮🏻‍♀️=> _1F46E1F3FB200D2640FE0F_", "👮🏼‍♀️=> _1F46E1F3FC200D2640FE0F_", "👮🏽‍♀️=> _1F46E1F3FD200D2640FE0F_", "👮🏾‍♀️=> _1F46E1F3FE200D2640FE0F_", "👮🏿‍♀️=> _1F46E1F3FF200D2640FE0F_", "🕵️=> _1F575FE0F_", "🕵=> _1F575_", "🕵🏻=> _1F5751F3FB_", "🕵🏼=> _1F5751F3FC_", "🕵🏽=> _1F5751F3FD_", "🕵🏾=> _1F5751F3FE_", "🕵🏿=> _1F5751F3FF_", "🕵️‍♂️=> _1F575FE0F200D2642FE0F_", "🕵‍♂️=> _1F575200D2642FE0F_", "🕵️‍♂=> _1F575FE0F200D2642_", "🕵‍♂=> _1F575200D2642_", "🕵🏻‍♂️=> _1F5751F3FB200D2642FE0F_", "🕵🏼‍♂️=> _1F5751F3FC200D2642FE0F_", "🕵🏽‍♂️=> _1F5751F3FD200D2642FE0F_", "🕵🏾‍♂️=> _1F5751F3FE200D2642FE0F_", "🕵🏿‍♂️=> _1F5751F3FF200D2642FE0F_", "🕵️‍♀️=> _1F575FE0F200D2640FE0F_", "🕵‍♀️=> _1F575200D2640FE0F_", "🕵️‍♀=> _1F575FE0F200D2640_", "🕵‍♀=> _1F575200D2640_", "🕵🏻‍♀️=> _1F5751F3FB200D2640FE0F_", "🕵🏼‍♀️=> _1F5751F3FC200D2640FE0F_", "🕵🏽‍♀️=> _1F5751F3FD200D2640FE0F_", "🕵🏾‍♀️=> _1F5751F3FE200D2640FE0F_", "🕵🏿‍♀️=> _1F5751F3FF200D2640FE0F_", "💂=> _1F482_", "💂🏻=> _1F4821F3FB_", "💂🏼=> _1F4821F3FC_", "💂🏽=> _1F4821F3FD_", "💂🏾=> _1F4821F3FE_", "💂🏿=> _1F4821F3FF_", "💂‍♂️=> _1F482200D2642FE0F_", "💂🏻‍♂️=> _1F4821F3FB200D2642FE0F_", "💂🏼‍♂️=> _1F4821F3FC200D2642FE0F_", "💂🏽‍♂️=> _1F4821F3FD200D2642FE0F_", "💂🏾‍♂️=> _1F4821F3FE200D2642FE0F_", "💂🏿‍♂️=> _1F4821F3FF200D2642FE0F_", "💂‍♀️=> _1F482200D2640FE0F_", "💂🏻‍♀️=> _1F4821F3FB200D2640FE0F_", "💂🏼‍♀️=> _1F4821F3FC200D2640FE0F_", "💂🏽‍♀️=> _1F4821F3FD200D2640FE0F_", "💂🏾‍♀️=> _1F4821F3FE200D2640FE0F_", "💂🏿‍♀️=> _1F4821F3FF200D2640FE0F_", "🥷=> _1F977_", "🥷🏻=> _1F9771F3FB_", "🥷🏼=> _1F9771F3FC_", "🥷🏽=> _1F9771F3FD_", "🥷🏾=> _1F9771F3FE_", "🥷🏿=> _1F9771F3FF_", "👷=> _1F477_", "👷🏻=> _1F4771F3FB_", "👷🏼=> _1F4771F3FC_", "👷🏽=> _1F4771F3FD_", "👷🏾=> _1F4771F3FE_", "👷🏿=> _1F4771F3FF_", "👷‍♂️=> _1F477200D2642FE0F_", "👷🏻‍♂️=> _1F4771F3FB200D2642FE0F_", "👷🏼‍♂️=> _1F4771F3FC200D2642FE0F_", "👷🏽‍♂️=> _1F4771F3FD200D2642FE0F_", "👷🏾‍♂️=> _1F4771F3FE200D2642FE0F_", "👷🏿‍♂️=> _1F4771F3FF200D2642FE0F_", "👷‍♀️=> _1F477200D2640FE0F_", "👷🏻‍♀️=> _1F4771F3FB200D2640FE0F_", "👷🏼‍♀️=> _1F4771F3FC200D2640FE0F_", "👷🏽‍♀️=> _1F4771F3FD200D2640FE0F_", "👷🏾‍♀️=> _1F4771F3FE200D2640FE0F_", "👷🏿‍♀️=> _1F4771F3FF200D2640FE0F_", "🤴=> _1F934_", "🤴🏻=> _1F9341F3FB_", "🤴🏼=> _1F9341F3FC_", "🤴🏽=> _1F9341F3FD_", "🤴🏾=> _1F9341F3FE_", "🤴🏿=> _1F9341F3FF_", "👸=> _1F478_", "👸🏻=> _1F4781F3FB_", "👸🏼=> _1F4781F3FC_", "👸🏽=> _1F4781F3FD_", "👸🏾=> _1F4781F3FE_", "👸🏿=> _1F4781F3FF_", "👳=> _1F473_", "👳🏻=> _1F4731F3FB_", "👳🏼=> _1F4731F3FC_", "👳🏽=> _1F4731F3FD_", "👳🏾=> _1F4731F3FE_", "👳🏿=> _1F4731F3FF_", "👳‍♂️=> _1F473200D2642FE0F_", "👳🏻‍♂️=> _1F4731F3FB200D2642FE0F_", "👳🏼‍♂️=> _1F4731F3FC200D2642FE0F_", "👳🏽‍♂️=> _1F4731F3FD200D2642FE0F_", "👳🏾‍♂️=> _1F4731F3FE200D2642FE0F_", "👳🏿‍♂️=> _1F4731F3FF200D2642FE0F_", "👳‍♀️=> _1F473200D2640FE0F_", "👳🏻‍♀️=> _1F4731F3FB200D2640FE0F_", "👳🏼‍♀️=> _1F4731F3FC200D2640FE0F_", "👳🏽‍♀️=> _1F4731F3FD200D2640FE0F_", "👳🏾‍♀️=> _1F4731F3FE200D2640FE0F_", "👳🏿‍♀️=> _1F4731F3FF200D2640FE0F_", "👲=> _1F472_", "👲🏻=> _1F4721F3FB_", "👲🏼=> _1F4721F3FC_", "👲🏽=> _1F4721F3FD_", "👲🏾=> _1F4721F3FE_", "👲🏿=> _1F4721F3FF_", "🧕=> _1F9D5_", "🧕🏻=> _1F9D51F3FB_", "🧕🏼=> _1F9D51F3FC_", "🧕🏽=> _1F9D51F3FD_", "🧕🏾=> _1F9D51F3FE_", "🧕🏿=> _1F9D51F3FF_", "🤵=> _1F935_", "🤵🏻=> _1F9351F3FB_", "🤵🏼=> _1F9351F3FC_", "🤵🏽=> _1F9351F3FD_", "🤵🏾=> _1F9351F3FE_", "🤵🏿=> _1F9351F3FF_", "🤵‍♂️=> _1F935200D2642FE0F_", "🤵🏻‍♂️=> _1F9351F3FB200D2642FE0F_", "🤵🏼‍♂️=> _1F9351F3FC200D2642FE0F_", "🤵🏽‍♂️=> _1F9351F3FD200D2642FE0F_", "🤵🏾‍♂️=> _1F9351F3FE200D2642FE0F_", "🤵🏿‍♂️=> _1F9351F3FF200D2642FE0F_", "🤵‍♀️=> _1F935200D2640FE0F_", "🤵🏻‍♀️=> _1F9351F3FB200D2640FE0F_", "🤵🏼‍♀️=> _1F9351F3FC200D2640FE0F_", "🤵🏽‍♀️=> _1F9351F3FD200D2640FE0F_", "🤵🏾‍♀️=> _1F9351F3FE200D2640FE0F_", "🤵🏿‍♀️=> _1F9351F3FF200D2640FE0F_", "👰=> _1F470_", "👰🏻=> _1F4701F3FB_", "👰🏼=> _1F4701F3FC_", "👰🏽=> _1F4701F3FD_", "👰🏾=> _1F4701F3FE_", "👰🏿=> _1F4701F3FF_", "👰‍♂️=> _1F470200D2642FE0F_", "👰🏻‍♂️=> _1F4701F3FB200D2642FE0F_", "👰🏼‍♂️=> _1F4701F3FC200D2642FE0F_", "👰🏽‍♂️=> _1F4701F3FD200D2642FE0F_", "👰🏾‍♂️=> _1F4701F3FE200D2642FE0F_", "👰🏿‍♂️=> _1F4701F3FF200D2642FE0F_", "👰‍♀️=> _1F470200D2640FE0F_", "👰🏻‍♀️=> _1F4701F3FB200D2640FE0F_", "👰🏼‍♀️=> _1F4701F3FC200D2640FE0F_", "👰🏽‍♀️=> _1F4701F3FD200D2640FE0F_", "👰🏾‍♀️=> _1F4701F3FE200D2640FE0F_", "👰🏿‍♀️=> _1F4701F3FF200D2640FE0F_", "🤰=> _1F930_", "🤰🏻=> _1F9301F3FB_", "🤰🏼=> _1F9301F3FC_", "🤰🏽=> _1F9301F3FD_", "🤰🏾=> _1F9301F3FE_", "🤰🏿=> _1F9301F3FF_", "🤱=> _1F931_", "🤱🏻=> _1F9311F3FB_", "🤱🏼=> _1F9311F3FC_", "🤱🏽=> _1F9311F3FD_", "🤱🏾=> _1F9311F3FE_", "🤱🏿=> _1F9311F3FF_", "👩‍🍼=> _1F469200D1F37C_", "👩🏻‍🍼=> _1F4691F3FB200D1F37C_", "👩🏼‍🍼=> _1F4691F3FC200D1F37C_", "👩🏽‍🍼=> _1F4691F3FD200D1F37C_", "👩🏾‍🍼=> _1F4691F3FE200D1F37C_", "👩🏿‍🍼=> _1F4691F3FF200D1F37C_", "👨‍🍼=> _1F468200D1F37C_", "👨🏻‍🍼=> _1F4681F3FB200D1F37C_", "👨🏼‍🍼=> _1F4681F3FC200D1F37C_", "👨🏽‍🍼=> _1F4681F3FD200D1F37C_", "👨🏾‍🍼=> _1F4681F3FE200D1F37C_", "👨🏿‍🍼=> _1F4681F3FF200D1F37C_", "🧑‍🍼=> _1F9D1200D1F37C_", "🧑🏻‍🍼=> _1F9D11F3FB200D1F37C_", "🧑🏼‍🍼=> _1F9D11F3FC200D1F37C_", "🧑🏽‍🍼=> _1F9D11F3FD200D1F37C_", "🧑🏾‍🍼=> _1F9D11F3FE200D1F37C_", "🧑🏿‍🍼=> _1F9D11F3FF200D1F37C_", "👼=> _1F47C_", "👼🏻=> _1F47C1F3FB_", "👼🏼=> _1F47C1F3FC_", "👼🏽=> _1F47C1F3FD_", "👼🏾=> _1F47C1F3FE_", "👼🏿=> _1F47C1F3FF_", "🎅=> _1F385_", "🎅🏻=> _1F3851F3FB_", "🎅🏼=> _1F3851F3FC_", "🎅🏽=> _1F3851F3FD_", "🎅🏾=> _1F3851F3FE_", "🎅🏿=> _1F3851F3FF_", "🤶=> _1F936_", "🤶🏻=> _1F9361F3FB_", "🤶🏼=> _1F9361F3FC_", "🤶🏽=> _1F9361F3FD_", "🤶🏾=> _1F9361F3FE_", "🤶🏿=> _1F9361F3FF_", "🧑‍🎄=> _1F9D1200D1F384_", "🧑🏻‍🎄=> _1F9D11F3FB200D1F384_", "🧑🏼‍🎄=> _1F9D11F3FC200D1F384_", "🧑🏽‍🎄=> _1F9D11F3FD200D1F384_", "🧑🏾‍🎄=> _1F9D11F3FE200D1F384_", "🧑🏿‍🎄=> _1F9D11F3FF200D1F384_", "🦸=> _1F9B8_", "🦸🏻=> _1F9B81F3FB_", "🦸🏼=> _1F9B81F3FC_", "🦸🏽=> _1F9B81F3FD_", "🦸🏾=> _1F9B81F3FE_", "🦸🏿=> _1F9B81F3FF_", "🦸‍♂️=> _1F9B8200D2642FE0F_", "🦸🏻‍♂️=> _1F9B81F3FB200D2642FE0F_", "🦸🏼‍♂️=> _1F9B81F3FC200D2642FE0F_", "🦸🏽‍♂️=> _1F9B81F3FD200D2642FE0F_", "🦸🏾‍♂️=> _1F9B81F3FE200D2642FE0F_", "🦸🏿‍♂️=> _1F9B81F3FF200D2642FE0F_", "🦸‍♀️=> _1F9B8200D2640FE0F_", "🦸🏻‍♀️=> _1F9B81F3FB200D2640FE0F_", "🦸🏼‍♀️=> _1F9B81F3FC200D2640FE0F_", "🦸🏽‍♀️=> _1F9B81F3FD200D2640FE0F_", "🦸🏾‍♀️=> _1F9B81F3FE200D2640FE0F_", "🦸🏿‍♀️=> _1F9B81F3FF200D2640FE0F_", "🦹=> _1F9B9_", "🦹🏻=> _1F9B91F3FB_", "🦹🏼=> _1F9B91F3FC_", "🦹🏽=> _1F9B91F3FD_", "🦹🏾=> _1F9B91F3FE_", "🦹🏿=> _1F9B91F3FF_", "🦹‍♂️=> _1F9B9200D2642FE0F_", "🦹🏻‍♂️=> _1F9B91F3FB200D2642FE0F_", "🦹🏼‍♂️=> _1F9B91F3FC200D2642FE0F_", "🦹🏽‍♂️=> _1F9B91F3FD200D2642FE0F_", "🦹🏾‍♂️=> _1F9B91F3FE200D2642FE0F_", "🦹🏿‍♂️=> _1F9B91F3FF200D2642FE0F_", "🦹‍♀️=> _1F9B9200D2640FE0F_", "🦹🏻‍♀️=> _1F9B91F3FB200D2640FE0F_", "🦹🏼‍♀️=> _1F9B91F3FC200D2640FE0F_", "🦹🏽‍♀️=> _1F9B91F3FD200D2640FE0F_", "🦹🏾‍♀️=> _1F9B91F3FE200D2640FE0F_", "🦹🏿‍♀️=> _1F9B91F3FF200D2640FE0F_", "🧙=> _1F9D9_", "🧙🏻=> _1F9D91F3FB_", "🧙🏼=> _1F9D91F3FC_", "🧙🏽=> _1F9D91F3FD_", "🧙🏾=> _1F9D91F3FE_", "🧙🏿=> _1F9D91F3FF_", "🧙‍♂️=> _1F9D9200D2642FE0F_", "🧙🏻‍♂️=> _1F9D91F3FB200D2642FE0F_", "🧙🏼‍♂️=> _1F9D91F3FC200D2642FE0F_", "🧙🏽‍♂️=> _1F9D91F3FD200D2642FE0F_", "🧙🏾‍♂️=> _1F9D91F3FE200D2642FE0F_", "🧙🏿‍♂️=> _1F9D91F3FF200D2642FE0F_", "🧙‍♀️=> _1F9D9200D2640FE0F_", "🧙🏻‍♀️=> _1F9D91F3FB200D2640FE0F_", "🧙🏼‍♀️=> _1F9D91F3FC200D2640FE0F_", "🧙🏽‍♀️=> _1F9D91F3FD200D2640FE0F_", "🧙🏾‍♀️=> _1F9D91F3FE200D2640FE0F_", "🧙🏿‍♀️=> _1F9D91F3FF200D2640FE0F_", "🧚=> _1F9DA_", "🧚🏻=> _1F9DA1F3FB_", "🧚🏼=> _1F9DA1F3FC_", "🧚🏽=> _1F9DA1F3FD_", "🧚🏾=> _1F9DA1F3FE_", "🧚🏿=> _1F9DA1F3FF_", "🧚‍♂️=> _1F9DA200D2642FE0F_", "🧚🏻‍♂️=> _1F9DA1F3FB200D2642FE0F_", "🧚🏼‍♂️=> _1F9DA1F3FC200D2642FE0F_", "🧚🏽‍♂️=> _1F9DA1F3FD200D2642FE0F_", "🧚🏾‍♂️=> _1F9DA1F3FE200D2642FE0F_", "🧚🏿‍♂️=> _1F9DA1F3FF200D2642FE0F_", "🧚‍♀️=> _1F9DA200D2640FE0F_", "🧚🏻‍♀️=> _1F9DA1F3FB200D2640FE0F_", "🧚🏼‍♀️=> _1F9DA1F3FC200D2640FE0F_", "🧚🏽‍♀️=> _1F9DA1F3FD200D2640FE0F_", "🧚🏾‍♀️=> _1F9DA1F3FE200D2640FE0F_", "🧚🏿‍♀️=> _1F9DA1F3FF200D2640FE0F_", "🧛=> _1F9DB_", "🧛🏻=> _1F9DB1F3FB_", "🧛🏼=> _1F9DB1F3FC_", "🧛🏽=> _1F9DB1F3FD_", "🧛🏾=> _1F9DB1F3FE_", "🧛🏿=> _1F9DB1F3FF_", "🧛‍♂️=> _1F9DB200D2642FE0F_", "🧛🏻‍♂️=> _1F9DB1F3FB200D2642FE0F_", "🧛🏼‍♂️=> _1F9DB1F3FC200D2642FE0F_", "🧛🏽‍♂️=> _1F9DB1F3FD200D2642FE0F_", "🧛🏾‍♂️=> _1F9DB1F3FE200D2642FE0F_", "🧛🏿‍♂️=> _1F9DB1F3FF200D2642FE0F_", "🧛‍♀️=> _1F9DB200D2640FE0F_", "🧛🏻‍♀️=> _1F9DB1F3FB200D2640FE0F_", "🧛🏼‍♀️=> _1F9DB1F3FC200D2640FE0F_", "🧛🏽‍♀️=> _1F9DB1F3FD200D2640FE0F_", "🧛🏾‍♀️=> _1F9DB1F3FE200D2640FE0F_", "🧛🏿‍♀️=> _1F9DB1F3FF200D2640FE0F_", "🧜=> _1F9DC_", "🧜🏻=> _1F9DC1F3FB_", "🧜🏼=> _1F9DC1F3FC_", "🧜🏽=> _1F9DC1F3FD_", "🧜🏾=> _1F9DC1F3FE_", "🧜🏿=> _1F9DC1F3FF_", "🧜‍♂️=> _1F9DC200D2642FE0F_", "🧜🏻‍♂️=> _1F9DC1F3FB200D2642FE0F_", "🧜🏼‍♂️=> _1F9DC1F3FC200D2642FE0F_", "🧜🏽‍♂️=> _1F9DC1F3FD200D2642FE0F_", "🧜🏾‍♂️=> _1F9DC1F3FE200D2642FE0F_", "🧜🏿‍♂️=> _1F9DC1F3FF200D2642FE0F_", "🧜‍♀️=> _1F9DC200D2640FE0F_", "🧜🏻‍♀️=> _1F9DC1F3FB200D2640FE0F_", "🧜🏼‍♀️=> _1F9DC1F3FC200D2640FE0F_", "🧜🏽‍♀️=> _1F9DC1F3FD200D2640FE0F_", "🧜🏾‍♀️=> _1F9DC1F3FE200D2640FE0F_", "🧜🏿‍♀️=> _1F9DC1F3FF200D2640FE0F_", "🧝=> _1F9DD_", "🧝🏻=> _1F9DD1F3FB_", "🧝🏼=> _1F9DD1F3FC_", "🧝🏽=> _1F9DD1F3FD_", "🧝🏾=> _1F9DD1F3FE_", "🧝🏿=> _1F9DD1F3FF_", "🧝‍♂️=> _1F9DD200D2642FE0F_", "🧝🏻‍♂️=> _1F9DD1F3FB200D2642FE0F_", "🧝🏼‍♂️=> _1F9DD1F3FC200D2642FE0F_", "🧝🏽‍♂️=> _1F9DD1F3FD200D2642FE0F_", "🧝🏾‍♂️=> _1F9DD1F3FE200D2642FE0F_", "🧝🏿‍♂️=> _1F9DD1F3FF200D2642FE0F_", "🧝‍♀️=> _1F9DD200D2640FE0F_", "🧝🏻‍♀️=> _1F9DD1F3FB200D2640FE0F_", "🧝🏼‍♀️=> _1F9DD1F3FC200D2640FE0F_", "🧝🏽‍♀️=> _1F9DD1F3FD200D2640FE0F_", "🧝🏾‍♀️=> _1F9DD1F3FE200D2640FE0F_", "🧝🏿‍♀️=> _1F9DD1F3FF200D2640FE0F_", "🧞=> _1F9DE_", "🧞‍♂️=> _1F9DE200D2642FE0F_", "🧞‍♀️=> _1F9DE200D2640FE0F_", "🧟=> _1F9DF_", "🧟‍♂️=> _1F9DF200D2642FE0F_", "🧟‍♀️=> _1F9DF200D2640FE0F_", "💆=> _1F486_", "💆🏻=> _1F4861F3FB_", "💆🏼=> _1F4861F3FC_", "💆🏽=> _1F4861F3FD_", "💆🏾=> _1F4861F3FE_", "💆🏿=> _1F4861F3FF_", "💆‍♂️=> _1F486200D2642FE0F_", "💆🏻‍♂️=> _1F4861F3FB200D2642FE0F_", "💆🏼‍♂️=> _1F4861F3FC200D2642FE0F_", "💆🏽‍♂️=> _1F4861F3FD200D2642FE0F_", "💆🏾‍♂️=> _1F4861F3FE200D2642FE0F_", "💆🏿‍♂️=> _1F4861F3FF200D2642FE0F_", "💆‍♀️=> _1F486200D2640FE0F_", "💆🏻‍♀️=> _1F4861F3FB200D2640FE0F_", "💆🏼‍♀️=> _1F4861F3FC200D2640FE0F_", "💆🏽‍♀️=> _1F4861F3FD200D2640FE0F_", "💆🏾‍♀️=> _1F4861F3FE200D2640FE0F_", "💆🏿‍♀️=> _1F4861F3FF200D2640FE0F_", "💇=> _1F487_", "💇🏻=> _1F4871F3FB_", "💇🏼=> _1F4871F3FC_", "💇🏽=> _1F4871F3FD_", "💇🏾=> _1F4871F3FE_", "💇🏿=> _1F4871F3FF_", "💇‍♂️=> _1F487200D2642FE0F_", "💇🏻‍♂️=> _1F4871F3FB200D2642FE0F_", "💇🏼‍♂️=> _1F4871F3FC200D2642FE0F_", "💇🏽‍♂️=> _1F4871F3FD200D2642FE0F_", "💇🏾‍♂️=> _1F4871F3FE200D2642FE0F_", "💇🏿‍♂️=> _1F4871F3FF200D2642FE0F_", "💇‍♀️=> _1F487200D2640FE0F_", "💇🏻‍♀️=> _1F4871F3FB200D2640FE0F_", "💇🏼‍♀️=> _1F4871F3FC200D2640FE0F_", "💇🏽‍♀️=> _1F4871F3FD200D2640FE0F_", "💇🏾‍♀️=> _1F4871F3FE200D2640FE0F_", "💇🏿‍♀️=> _1F4871F3FF200D2640FE0F_", "🚶=> _1F6B6_", "🚶🏻=> _1F6B61F3FB_", "🚶🏼=> _1F6B61F3FC_", "🚶🏽=> _1F6B61F3FD_", "🚶🏾=> _1F6B61F3FE_", "🚶🏿=> _1F6B61F3FF_", "🚶‍♂️=> _1F6B6200D2642FE0F_", "🚶🏻‍♂️=> _1F6B61F3FB200D2642FE0F_", "🚶🏼‍♂️=> _1F6B61F3FC200D2642FE0F_", "🚶🏽‍♂️=> _1F6B61F3FD200D2642FE0F_", "🚶🏾‍♂️=> _1F6B61F3FE200D2642FE0F_", "🚶🏿‍♂️=> _1F6B61F3FF200D2642FE0F_", "🚶‍♀️=> _1F6B6200D2640FE0F_", "🚶🏻‍♀️=> _1F6B61F3FB200D2640FE0F_", "🚶🏼‍♀️=> _1F6B61F3FC200D2640FE0F_", "🚶🏽‍♀️=> _1F6B61F3FD200D2640FE0F_", "🚶🏾‍♀️=> _1F6B61F3FE200D2640FE0F_", "🚶🏿‍♀️=> _1F6B61F3FF200D2640FE0F_", "🧍=> _1F9CD_", "🧍🏻=> _1F9CD1F3FB_", "🧍🏼=> _1F9CD1F3FC_", "🧍🏽=> _1F9CD1F3FD_", "🧍🏾=> _1F9CD1F3FE_", "🧍🏿=> _1F9CD1F3FF_", "🧍‍♂️=> _1F9CD200D2642FE0F_", "🧍🏻‍♂️=> _1F9CD1F3FB200D2642FE0F_", "🧍🏼‍♂️=> _1F9CD1F3FC200D2642FE0F_", "🧍🏽‍♂️=> _1F9CD1F3FD200D2642FE0F_", "🧍🏾‍♂️=> _1F9CD1F3FE200D2642FE0F_", "🧍🏿‍♂️=> _1F9CD1F3FF200D2642FE0F_", "🧍‍♀️=> _1F9CD200D2640FE0F_", "🧍🏻‍♀️=> _1F9CD1F3FB200D2640FE0F_", "🧍🏼‍♀️=> _1F9CD1F3FC200D2640FE0F_", "🧍🏽‍♀️=> _1F9CD1F3FD200D2640FE0F_", "🧍🏾‍♀️=> _1F9CD1F3FE200D2640FE0F_", "🧍🏿‍♀️=> _1F9CD1F3FF200D2640FE0F_", "🧎=> _1F9CE_", "🧎🏻=> _1F9CE1F3FB_", "🧎🏼=> _1F9CE1F3FC_", "🧎🏽=> _1F9CE1F3FD_", "🧎🏾=> _1F9CE1F3FE_", "🧎🏿=> _1F9CE1F3FF_", "🧎‍♂️=> _1F9CE200D2642FE0F_", "🧎🏻‍♂️=> _1F9CE1F3FB200D2642FE0F_", "🧎🏼‍♂️=> _1F9CE1F3FC200D2642FE0F_", "🧎🏽‍♂️=> _1F9CE1F3FD200D2642FE0F_", "🧎🏾‍♂️=> _1F9CE1F3FE200D2642FE0F_", "🧎🏿‍♂️=> _1F9CE1F3FF200D2642FE0F_", "🧎‍♀️=> _1F9CE200D2640FE0F_", "🧎🏻‍♀️=> _1F9CE1F3FB200D2640FE0F_", "🧎🏼‍♀️=> _1F9CE1F3FC200D2640FE0F_", "🧎🏽‍♀️=> _1F9CE1F3FD200D2640FE0F_", "🧎🏾‍♀️=> _1F9CE1F3FE200D2640FE0F_", "🧎🏿‍♀️=> _1F9CE1F3FF200D2640FE0F_", "🧑‍🦯=> _1F9D1200D1F9AF_", "🧑🏻‍🦯=> _1F9D11F3FB200D1F9AF_", "🧑🏼‍🦯=> _1F9D11F3FC200D1F9AF_", "🧑🏽‍🦯=> _1F9D11F3FD200D1F9AF_", "🧑🏾‍🦯=> _1F9D11F3FE200D1F9AF_", "🧑🏿‍🦯=> _1F9D11F3FF200D1F9AF_", "👨‍🦯=> _1F468200D1F9AF_", "👨🏻‍🦯=> _1F4681F3FB200D1F9AF_", "👨🏼‍🦯=> _1F4681F3FC200D1F9AF_", "👨🏽‍🦯=> _1F4681F3FD200D1F9AF_", "👨🏾‍🦯=> _1F4681F3FE200D1F9AF_", "👨🏿‍🦯=> _1F4681F3FF200D1F9AF_", "👩‍🦯=> _1F469200D1F9AF_", "👩🏻‍🦯=> _1F4691F3FB200D1F9AF_", "👩🏼‍🦯=> _1F4691F3FC200D1F9AF_", "👩🏽‍🦯=> _1F4691F3FD200D1F9AF_", "👩🏾‍🦯=> _1F4691F3FE200D1F9AF_", "👩🏿‍🦯=> _1F4691F3FF200D1F9AF_", "🧑‍🦼=> _1F9D1200D1F9BC_", "🧑🏻‍🦼=> _1F9D11F3FB200D1F9BC_", "🧑🏼‍🦼=> _1F9D11F3FC200D1F9BC_", "🧑🏽‍🦼=> _1F9D11F3FD200D1F9BC_", "🧑🏾‍🦼=> _1F9D11F3FE200D1F9BC_", "🧑🏿‍🦼=> _1F9D11F3FF200D1F9BC_", "👨‍🦼=> _1F468200D1F9BC_", "👨🏻‍🦼=> _1F4681F3FB200D1F9BC_", "👨🏼‍🦼=> _1F4681F3FC200D1F9BC_", "👨🏽‍🦼=> _1F4681F3FD200D1F9BC_", "👨🏾‍🦼=> _1F4681F3FE200D1F9BC_", "👨🏿‍🦼=> _1F4681F3FF200D1F9BC_", "👩‍🦼=> _1F469200D1F9BC_", "👩🏻‍🦼=> _1F4691F3FB200D1F9BC_", "👩🏼‍🦼=> _1F4691F3FC200D1F9BC_", "👩🏽‍🦼=> _1F4691F3FD200D1F9BC_", "👩🏾‍🦼=> _1F4691F3FE200D1F9BC_", "👩🏿‍🦼=> _1F4691F3FF200D1F9BC_", "🧑‍🦽=> _1F9D1200D1F9BD_", "🧑🏻‍🦽=> _1F9D11F3FB200D1F9BD_", "🧑🏼‍🦽=> _1F9D11F3FC200D1F9BD_", "🧑🏽‍🦽=> _1F9D11F3FD200D1F9BD_", "🧑🏾‍🦽=> _1F9D11F3FE200D1F9BD_", "🧑🏿‍🦽=> _1F9D11F3FF200D1F9BD_", "👨‍🦽=> _1F468200D1F9BD_", "👨🏻‍🦽=> _1F4681F3FB200D1F9BD_", "👨🏼‍🦽=> _1F4681F3FC200D1F9BD_", "👨🏽‍🦽=> _1F4681F3FD200D1F9BD_", "👨🏾‍🦽=> _1F4681F3FE200D1F9BD_", "👨🏿‍🦽=> _1F4681F3FF200D1F9BD_", "👩‍🦽=> _1F469200D1F9BD_", "👩🏻‍🦽=> _1F4691F3FB200D1F9BD_", "👩🏼‍🦽=> _1F4691F3FC200D1F9BD_", "👩🏽‍🦽=> _1F4691F3FD200D1F9BD_", "👩🏾‍🦽=> _1F4691F3FE200D1F9BD_", "👩🏿‍🦽=> _1F4691F3FF200D1F9BD_", "🏃=> _1F3C3_", "🏃🏻=> _1F3C31F3FB_", "🏃🏼=> _1F3C31F3FC_", "🏃🏽=> _1F3C31F3FD_", "🏃🏾=> _1F3C31F3FE_", "🏃🏿=> _1F3C31F3FF_", "🏃‍♂️=> _1F3C3200D2642FE0F_", "🏃🏻‍♂️=> _1F3C31F3FB200D2642FE0F_", "🏃🏼‍♂️=> _1F3C31F3FC200D2642FE0F_", "🏃🏽‍♂️=> _1F3C31F3FD200D2642FE0F_", "🏃🏾‍♂️=> _1F3C31F3FE200D2642FE0F_", "🏃🏿‍♂️=> _1F3C31F3FF200D2642FE0F_", "🏃‍♀️=> _1F3C3200D2640FE0F_", "🏃🏻‍♀️=> _1F3C31F3FB200D2640FE0F_", "🏃🏼‍♀️=> _1F3C31F3FC200D2640FE0F_", "🏃🏽‍♀️=> _1F3C31F3FD200D2640FE0F_", "🏃🏾‍♀️=> _1F3C31F3FE200D2640FE0F_", "🏃🏿‍♀️=> _1F3C31F3FF200D2640FE0F_", "💃=> _1F483_", "💃🏻=> _1F4831F3FB_", "💃🏼=> _1F4831F3FC_", "💃🏽=> _1F4831F3FD_", "💃🏾=> _1F4831F3FE_", "💃🏿=> _1F4831F3FF_", "🕺=> _1F57A_", "🕺🏻=> _1F57A1F3FB_", "🕺🏼=> _1F57A1F3FC_", "🕺🏽=> _1F57A1F3FD_", "🕺🏾=> _1F57A1F3FE_", "🕺🏿=> _1F57A1F3FF_", "🕴️=> _1F574FE0F_", "🕴=> _1F574_", "🕴🏻=> _1F5741F3FB_", "🕴🏼=> _1F5741F3FC_", "🕴🏽=> _1F5741F3FD_", "🕴🏾=> _1F5741F3FE_", "🕴🏿=> _1F5741F3FF_", "👯=> _1F46F_", "👯‍♂️=> _1F46F200D2642FE0F_", "👯‍♀️=> _1F46F200D2640FE0F_", "🧖=> _1F9D6_", "🧖🏻=> _1F9D61F3FB_", "🧖🏼=> _1F9D61F3FC_", "🧖🏽=> _1F9D61F3FD_", "🧖🏾=> _1F9D61F3FE_", "🧖🏿=> _1F9D61F3FF_", "🧖‍♂️=> _1F9D6200D2642FE0F_", "🧖🏻‍♂️=> _1F9D61F3FB200D2642FE0F_", "🧖🏼‍♂️=> _1F9D61F3FC200D2642FE0F_", "🧖🏽‍♂️=> _1F9D61F3FD200D2642FE0F_", "🧖🏾‍♂️=> _1F9D61F3FE200D2642FE0F_", "🧖🏿‍♂️=> _1F9D61F3FF200D2642FE0F_", "🧖‍♀️=> _1F9D6200D2640FE0F_", "🧖🏻‍♀️=> _1F9D61F3FB200D2640FE0F_", "🧖🏼‍♀️=> _1F9D61F3FC200D2640FE0F_", "🧖🏽‍♀️=> _1F9D61F3FD200D2640FE0F_", "🧖🏾‍♀️=> _1F9D61F3FE200D2640FE0F_", "🧖🏿‍♀️=> _1F9D61F3FF200D2640FE0F_", "🧗=> _1F9D7_", "🧗🏻=> _1F9D71F3FB_", "🧗🏼=> _1F9D71F3FC_", "🧗🏽=> _1F9D71F3FD_", "🧗🏾=> _1F9D71F3FE_", "🧗🏿=> _1F9D71F3FF_", "🧗‍♂️=> _1F9D7200D2642FE0F_", "🧗🏻‍♂️=> _1F9D71F3FB200D2642FE0F_", "🧗🏼‍♂️=> _1F9D71F3FC200D2642FE0F_", "🧗🏽‍♂️=> _1F9D71F3FD200D2642FE0F_", "🧗🏾‍♂️=> _1F9D71F3FE200D2642FE0F_", "🧗🏿‍♂️=> _1F9D71F3FF200D2642FE0F_", "🧗‍♀️=> _1F9D7200D2640FE0F_", "🧗🏻‍♀️=> _1F9D71F3FB200D2640FE0F_", "🧗🏼‍♀️=> _1F9D71F3FC200D2640FE0F_", "🧗🏽‍♀️=> _1F9D71F3FD200D2640FE0F_", "🧗🏾‍♀️=> _1F9D71F3FE200D2640FE0F_", "🧗🏿‍♀️=> _1F9D71F3FF200D2640FE0F_", "🤺=> _1F93A_", "🏇=> _1F3C7_", "🏇🏻=> _1F3C71F3FB_", "🏇🏼=> _1F3C71F3FC_", "🏇🏽=> _1F3C71F3FD_", "🏇🏾=> _1F3C71F3FE_", "🏇🏿=> _1F3C71F3FF_", "⛷️=> _26F7FE0F_", "⛷=> _26F7_", "🏂=> _1F3C2_", "🏂🏻=> _1F3C21F3FB_", "🏂🏼=> _1F3C21F3FC_", "🏂🏽=> _1F3C21F3FD_", "🏂🏾=> _1F3C21F3FE_", "🏂🏿=> _1F3C21F3FF_", "🏌️=> _1F3CCFE0F_", "🏌=> _1F3CC_", "🏌🏻=> _1F3CC1F3FB_", "🏌🏼=> _1F3CC1F3FC_", "🏌🏽=> _1F3CC1F3FD_", "🏌🏾=> _1F3CC1F3FE_", "🏌🏿=> _1F3CC1F3FF_", "🏌️‍♂️=> _1F3CCFE0F200D2642FE0F_", "🏌‍♂️=> _1F3CC200D2642FE0F_", "🏌️‍♂=> _1F3CCFE0F200D2642_", "🏌‍♂=> _1F3CC200D2642_", "🏌🏻‍♂️=> _1F3CC1F3FB200D2642FE0F_", "🏌🏼‍♂️=> _1F3CC1F3FC200D2642FE0F_", "🏌🏽‍♂️=> _1F3CC1F3FD200D2642FE0F_", "🏌🏾‍♂️=> _1F3CC1F3FE200D2642FE0F_", "🏌🏿‍♂️=> _1F3CC1F3FF200D2642FE0F_", "🏌️‍♀️=> _1F3CCFE0F200D2640FE0F_", "🏌‍♀️=> _1F3CC200D2640FE0F_", "🏌️‍♀=> _1F3CCFE0F200D2640_", "🏌‍♀=> _1F3CC200D2640_", "🏌🏻‍♀️=> _1F3CC1F3FB200D2640FE0F_", "🏌🏼‍♀️=> _1F3CC1F3FC200D2640FE0F_", "🏌🏽‍♀️=> _1F3CC1F3FD200D2640FE0F_", "🏌🏾‍♀️=> _1F3CC1F3FE200D2640FE0F_", "🏌🏿‍♀️=> _1F3CC1F3FF200D2640FE0F_", "🏄=> _1F3C4_", "🏄🏻=> _1F3C41F3FB_", "🏄🏼=> _1F3C41F3FC_", "🏄🏽=> _1F3C41F3FD_", "🏄🏾=> _1F3C41F3FE_", "🏄🏿=> _1F3C41F3FF_", "🏄‍♂️=> _1F3C4200D2642FE0F_", "🏄🏻‍♂️=> _1F3C41F3FB200D2642FE0F_", "🏄🏼‍♂️=> _1F3C41F3FC200D2642FE0F_", "🏄🏽‍♂️=> _1F3C41F3FD200D2642FE0F_", "🏄🏾‍♂️=> _1F3C41F3FE200D2642FE0F_", "🏄🏿‍♂️=> _1F3C41F3FF200D2642FE0F_", "🏄‍♀️=> _1F3C4200D2640FE0F_", "🏄🏻‍♀️=> _1F3C41F3FB200D2640FE0F_", "🏄🏼‍♀️=> _1F3C41F3FC200D2640FE0F_", "🏄🏽‍♀️=> _1F3C41F3FD200D2640FE0F_", "🏄🏾‍♀️=> _1F3C41F3FE200D2640FE0F_", "🏄🏿‍♀️=> _1F3C41F3FF200D2640FE0F_", "🚣=> _1F6A3_", "🚣🏻=> _1F6A31F3FB_", "🚣🏼=> _1F6A31F3FC_", "🚣🏽=> _1F6A31F3FD_", "🚣🏾=> _1F6A31F3FE_", "🚣🏿=> _1F6A31F3FF_", "🚣‍♂️=> _1F6A3200D2642FE0F_", "🚣🏻‍♂️=> _1F6A31F3FB200D2642FE0F_", "🚣🏼‍♂️=> _1F6A31F3FC200D2642FE0F_", "🚣🏽‍♂️=> _1F6A31F3FD200D2642FE0F_", "🚣🏾‍♂️=> _1F6A31F3FE200D2642FE0F_", "🚣🏿‍♂️=> _1F6A31F3FF200D2642FE0F_", "🚣‍♀️=> _1F6A3200D2640FE0F_", "🚣🏻‍♀️=> _1F6A31F3FB200D2640FE0F_", "🚣🏼‍♀️=> _1F6A31F3FC200D2640FE0F_", "🚣🏽‍♀️=> _1F6A31F3FD200D2640FE0F_", "🚣🏾‍♀️=> _1F6A31F3FE200D2640FE0F_", "🚣🏿‍♀️=> _1F6A31F3FF200D2640FE0F_", "🏊=> _1F3CA_", "🏊🏻=> _1F3CA1F3FB_", "🏊🏼=> _1F3CA1F3FC_", "🏊🏽=> _1F3CA1F3FD_", "🏊🏾=> _1F3CA1F3FE_", "🏊🏿=> _1F3CA1F3FF_", "🏊‍♂️=> _1F3CA200D2642FE0F_", "🏊🏻‍♂️=> _1F3CA1F3FB200D2642FE0F_", "🏊🏼‍♂️=> _1F3CA1F3FC200D2642FE0F_", "🏊🏽‍♂️=> _1F3CA1F3FD200D2642FE0F_", "🏊🏾‍♂️=> _1F3CA1F3FE200D2642FE0F_", "🏊🏿‍♂️=> _1F3CA1F3FF200D2642FE0F_", "🏊‍♀️=> _1F3CA200D2640FE0F_", "🏊🏻‍♀️=> _1F3CA1F3FB200D2640FE0F_", "🏊🏼‍♀️=> _1F3CA1F3FC200D2640FE0F_", "🏊🏽‍♀️=> _1F3CA1F3FD200D2640FE0F_", "🏊🏾‍♀️=> _1F3CA1F3FE200D2640FE0F_", "🏊🏿‍♀️=> _1F3CA1F3FF200D2640FE0F_", "⛹️=> _26F9FE0F_", "⛹=> _26F9_", "⛹🏻=> _26F91F3FB_", "⛹🏼=> _26F91F3FC_", "⛹🏽=> _26F91F3FD_", "⛹🏾=> _26F91F3FE_", "⛹🏿=> _26F91F3FF_", "⛹️‍♂️=> _26F9FE0F200D2642FE0F_", "⛹‍♂️=> _26F9200D2642FE0F_", "⛹️‍♂=> _26F9FE0F200D2642_", "⛹‍♂=> _26F9200D2642_", "⛹🏻‍♂️=> _26F91F3FB200D2642FE0F_", "⛹🏼‍♂️=> _26F91F3FC200D2642FE0F_", "⛹🏽‍♂️=> _26F91F3FD200D2642FE0F_", "⛹🏾‍♂️=> _26F91F3FE200D2642FE0F_", "⛹🏿‍♂️=> _26F91F3FF200D2642FE0F_", "⛹️‍♀️=> _26F9FE0F200D2640FE0F_", "⛹‍♀️=> _26F9200D2640FE0F_", "⛹️‍♀=> _26F9FE0F200D2640_", "⛹‍♀=> _26F9200D2640_", "⛹🏻‍♀️=> _26F91F3FB200D2640FE0F_", "⛹🏼‍♀️=> _26F91F3FC200D2640FE0F_", "⛹🏽‍♀️=> _26F91F3FD200D2640FE0F_", "⛹🏾‍♀️=> _26F91F3FE200D2640FE0F_", "⛹🏿‍♀️=> _26F91F3FF200D2640FE0F_", "🏋️=> _1F3CBFE0F_", "🏋=> _1F3CB_", "🏋🏻=> _1F3CB1F3FB_", "🏋🏼=> _1F3CB1F3FC_", "🏋🏽=> _1F3CB1F3FD_", "🏋🏾=> _1F3CB1F3FE_", "🏋🏿=> _1F3CB1F3FF_", "🏋️‍♂️=> _1F3CBFE0F200D2642FE0F_", "🏋‍♂️=> _1F3CB200D2642FE0F_", "🏋️‍♂=> _1F3CBFE0F200D2642_", "🏋‍♂=> _1F3CB200D2642_", "🏋🏻‍♂️=> _1F3CB1F3FB200D2642FE0F_", "🏋🏼‍♂️=> _1F3CB1F3FC200D2642FE0F_", "🏋🏽‍♂️=> _1F3CB1F3FD200D2642FE0F_", "🏋🏾‍♂️=> _1F3CB1F3FE200D2642FE0F_", "🏋🏿‍♂️=> _1F3CB1F3FF200D2642FE0F_", "🏋️‍♀️=> _1F3CBFE0F200D2640FE0F_", "🏋‍♀️=> _1F3CB200D2640FE0F_", "🏋️‍♀=> _1F3CBFE0F200D2640_", "🏋‍♀=> _1F3CB200D2640_", "🏋🏻‍♀️=> _1F3CB1F3FB200D2640FE0F_", "🏋🏼‍♀️=> _1F3CB1F3FC200D2640FE0F_", "🏋🏽‍♀️=> _1F3CB1F3FD200D2640FE0F_", "🏋🏾‍♀️=> _1F3CB1F3FE200D2640FE0F_", "🏋🏿‍♀️=> _1F3CB1F3FF200D2640FE0F_", "🚴=> _1F6B4_", "🚴🏻=> _1F6B41F3FB_", "🚴🏼=> _1F6B41F3FC_", "🚴🏽=> _1F6B41F3FD_", "🚴🏾=> _1F6B41F3FE_", "🚴🏿=> _1F6B41F3FF_", "🚴‍♂️=> _1F6B4200D2642FE0F_", "🚴🏻‍♂️=> _1F6B41F3FB200D2642FE0F_", "🚴🏼‍♂️=> _1F6B41F3FC200D2642FE0F_", "🚴🏽‍♂️=> _1F6B41F3FD200D2642FE0F_", "🚴🏾‍♂️=> _1F6B41F3FE200D2642FE0F_", "🚴🏿‍♂️=> _1F6B41F3FF200D2642FE0F_", "🚴‍♀️=> _1F6B4200D2640FE0F_", "🚴🏻‍♀️=> _1F6B41F3FB200D2640FE0F_", "🚴🏼‍♀️=> _1F6B41F3FC200D2640FE0F_", "🚴🏽‍♀️=> _1F6B41F3FD200D2640FE0F_", "🚴🏾‍♀️=> _1F6B41F3FE200D2640FE0F_", "🚴🏿‍♀️=> _1F6B41F3FF200D2640FE0F_", "🚵=> _1F6B5_", "🚵🏻=> _1F6B51F3FB_", "🚵🏼=> _1F6B51F3FC_", "🚵🏽=> _1F6B51F3FD_", "🚵🏾=> _1F6B51F3FE_", "🚵🏿=> _1F6B51F3FF_", "🚵‍♂️=> _1F6B5200D2642FE0F_", "🚵🏻‍♂️=> _1F6B51F3FB200D2642FE0F_", "🚵🏼‍♂️=> _1F6B51F3FC200D2642FE0F_", "🚵🏽‍♂️=> _1F6B51F3FD200D2642FE0F_", "🚵🏾‍♂️=> _1F6B51F3FE200D2642FE0F_", "🚵🏿‍♂️=> _1F6B51F3FF200D2642FE0F_", "🚵‍♀️=> _1F6B5200D2640FE0F_", "🚵🏻‍♀️=> _1F6B51F3FB200D2640FE0F_", "🚵🏼‍♀️=> _1F6B51F3FC200D2640FE0F_", "🚵🏽‍♀️=> _1F6B51F3FD200D2640FE0F_", "🚵🏾‍♀️=> _1F6B51F3FE200D2640FE0F_", "🚵🏿‍♀️=> _1F6B51F3FF200D2640FE0F_", "🤸=> _1F938_", "🤸🏻=> _1F9381F3FB_", "🤸🏼=> _1F9381F3FC_", "🤸🏽=> _1F9381F3FD_", "🤸🏾=> _1F9381F3FE_", "🤸🏿=> _1F9381F3FF_", "🤸‍♂️=> _1F938200D2642FE0F_", "🤸🏻‍♂️=> _1F9381F3FB200D2642FE0F_", "🤸🏼‍♂️=> _1F9381F3FC200D2642FE0F_", "🤸🏽‍♂️=> _1F9381F3FD200D2642FE0F_", "🤸🏾‍♂️=> _1F9381F3FE200D2642FE0F_", "🤸🏿‍♂️=> _1F9381F3FF200D2642FE0F_", "🤸‍♀️=> _1F938200D2640FE0F_", "🤸🏻‍♀️=> _1F9381F3FB200D2640FE0F_", "🤸🏼‍♀️=> _1F9381F3FC200D2640FE0F_", "🤸🏽‍♀️=> _1F9381F3FD200D2640FE0F_", "🤸🏾‍♀️=> _1F9381F3FE200D2640FE0F_", "🤸🏿‍♀️=> _1F9381F3FF200D2640FE0F_", "🤼=> _1F93C_", "🤼‍♂️=> _1F93C200D2642FE0F_", "🤼‍♀️=> _1F93C200D2640FE0F_", "🤽=> _1F93D_", "🤽🏻=> _1F93D1F3FB_", "🤽🏼=> _1F93D1F3FC_", "🤽🏽=> _1F93D1F3FD_", "🤽🏾=> _1F93D1F3FE_", "🤽🏿=> _1F93D1F3FF_", "🤽‍♂️=> _1F93D200D2642FE0F_", "🤽🏻‍♂️=> _1F93D1F3FB200D2642FE0F_", "🤽🏼‍♂️=> _1F93D1F3FC200D2642FE0F_", "🤽🏽‍♂️=> _1F93D1F3FD200D2642FE0F_", "🤽🏾‍♂️=> _1F93D1F3FE200D2642FE0F_", "🤽🏿‍♂️=> _1F93D1F3FF200D2642FE0F_", "🤽‍♀️=> _1F93D200D2640FE0F_", "🤽🏻‍♀️=> _1F93D1F3FB200D2640FE0F_", "🤽🏼‍♀️=> _1F93D1F3FC200D2640FE0F_", "🤽🏽‍♀️=> _1F93D1F3FD200D2640FE0F_", "🤽🏾‍♀️=> _1F93D1F3FE200D2640FE0F_", "🤽🏿‍♀️=> _1F93D1F3FF200D2640FE0F_", "🤾=> _1F93E_", "🤾🏻=> _1F93E1F3FB_", "🤾🏼=> _1F93E1F3FC_", "🤾🏽=> _1F93E1F3FD_", "🤾🏾=> _1F93E1F3FE_", "🤾🏿=> _1F93E1F3FF_", "🤾‍♂️=> _1F93E200D2642FE0F_", "🤾🏻‍♂️=> _1F93E1F3FB200D2642FE0F_", "🤾🏼‍♂️=> _1F93E1F3FC200D2642FE0F_", "🤾🏽‍♂️=> _1F93E1F3FD200D2642FE0F_", "🤾🏾‍♂️=> _1F93E1F3FE200D2642FE0F_", "🤾🏿‍♂️=> _1F93E1F3FF200D2642FE0F_", "🤾‍♀️=> _1F93E200D2640FE0F_", "🤾🏻‍♀️=> _1F93E1F3FB200D2640FE0F_", "🤾🏼‍♀️=> _1F93E1F3FC200D2640FE0F_", "🤾🏽‍♀️=> _1F93E1F3FD200D2640FE0F_", "🤾🏾‍♀️=> _1F93E1F3FE200D2640FE0F_", "🤾🏿‍♀️=> _1F93E1F3FF200D2640FE0F_", "🤹=> _1F939_", "🤹🏻=> _1F9391F3FB_", "🤹🏼=> _1F9391F3FC_", "🤹🏽=> _1F9391F3FD_", "🤹🏾=> _1F9391F3FE_", "🤹🏿=> _1F9391F3FF_", "🤹‍♂️=> _1F939200D2642FE0F_", "🤹🏻‍♂️=> _1F9391F3FB200D2642FE0F_", "🤹🏼‍♂️=> _1F9391F3FC200D2642FE0F_", "🤹🏽‍♂️=> _1F9391F3FD200D2642FE0F_", "🤹🏾‍♂️=> _1F9391F3FE200D2642FE0F_", "🤹🏿‍♂️=> _1F9391F3FF200D2642FE0F_", "🤹‍♀️=> _1F939200D2640FE0F_", "🤹🏻‍♀️=> _1F9391F3FB200D2640FE0F_", "🤹🏼‍♀️=> _1F9391F3FC200D2640FE0F_", "🤹🏽‍♀️=> _1F9391F3FD200D2640FE0F_", "🤹🏾‍♀️=> _1F9391F3FE200D2640FE0F_", "🤹🏿‍♀️=> _1F9391F3FF200D2640FE0F_", "🧘=> _1F9D8_", "🧘🏻=> _1F9D81F3FB_", "🧘🏼=> _1F9D81F3FC_", "🧘🏽=> _1F9D81F3FD_", "🧘🏾=> _1F9D81F3FE_", "🧘🏿=> _1F9D81F3FF_", "🧘‍♂️=> _1F9D8200D2642FE0F_", "🧘🏻‍♂️=> _1F9D81F3FB200D2642FE0F_", "🧘🏼‍♂️=> _1F9D81F3FC200D2642FE0F_", "🧘🏽‍♂️=> _1F9D81F3FD200D2642FE0F_", "🧘🏾‍♂️=> _1F9D81F3FE200D2642FE0F_", "🧘🏿‍♂️=> _1F9D81F3FF200D2642FE0F_", "🧘‍♀️=> _1F9D8200D2640FE0F_", "🧘🏻‍♀️=> _1F9D81F3FB200D2640FE0F_", "🧘🏼‍♀️=> _1F9D81F3FC200D2640FE0F_", "🧘🏽‍♀️=> _1F9D81F3FD200D2640FE0F_", "🧘🏾‍♀️=> _1F9D81F3FE200D2640FE0F_", "🧘🏿‍♀️=> _1F9D81F3FF200D2640FE0F_", "🛀=> _1F6C0_", "🛀🏻=> _1F6C01F3FB_", "🛀🏼=> _1F6C01F3FC_", "🛀🏽=> _1F6C01F3FD_", "🛀🏾=> _1F6C01F3FE_", "🛀🏿=> _1F6C01F3FF_", "🛌=> _1F6CC_", "🛌🏻=> _1F6CC1F3FB_", "🛌🏼=> _1F6CC1F3FC_", "🛌🏽=> _1F6CC1F3FD_", "🛌🏾=> _1F6CC1F3FE_", "🛌🏿=> _1F6CC1F3FF_", "🧑‍🤝‍🧑=> _1F9D1200D1F91D200D1F9D1_", "🧑🏻‍🤝‍🧑🏻=> _1F9D11F3FB200D1F91D200D1F9D11F3FB_", "🧑🏻‍🤝‍🧑🏼=> _1F9D11F3FB200D1F91D200D1F9D11F3FC_", "🧑🏻‍🤝‍🧑🏽=> _1F9D11F3FB200D1F91D200D1F9D11F3FD_", "🧑🏻‍🤝‍🧑🏾=> _1F9D11F3FB200D1F91D200D1F9D11F3FE_", "🧑🏻‍🤝‍🧑🏿=> _1F9D11F3FB200D1F91D200D1F9D11F3FF_", "🧑🏼‍🤝‍🧑🏻=> _1F9D11F3FC200D1F91D200D1F9D11F3FB_", "🧑🏼‍🤝‍🧑🏼=> _1F9D11F3FC200D1F91D200D1F9D11F3FC_", "🧑🏼‍🤝‍🧑🏽=> _1F9D11F3FC200D1F91D200D1F9D11F3FD_", "🧑🏼‍🤝‍🧑🏾=> _1F9D11F3FC200D1F91D200D1F9D11F3FE_", "🧑🏼‍🤝‍🧑🏿=> _1F9D11F3FC200D1F91D200D1F9D11F3FF_", "🧑🏽‍🤝‍🧑🏻=> _1F9D11F3FD200D1F91D200D1F9D11F3FB_", "🧑🏽‍🤝‍🧑🏼=> _1F9D11F3FD200D1F91D200D1F9D11F3FC_", "🧑🏽‍🤝‍🧑🏽=> _1F9D11F3FD200D1F91D200D1F9D11F3FD_", "🧑🏽‍🤝‍🧑🏾=> _1F9D11F3FD200D1F91D200D1F9D11F3FE_", "🧑🏽‍🤝‍🧑🏿=> _1F9D11F3FD200D1F91D200D1F9D11F3FF_", "🧑🏾‍🤝‍🧑🏻=> _1F9D11F3FE200D1F91D200D1F9D11F3FB_", "🧑🏾‍🤝‍🧑🏼=> _1F9D11F3FE200D1F91D200D1F9D11F3FC_", "🧑🏾‍🤝‍🧑🏽=> _1F9D11F3FE200D1F91D200D1F9D11F3FD_", "🧑🏾‍🤝‍🧑🏾=> _1F9D11F3FE200D1F91D200D1F9D11F3FE_", "🧑🏾‍🤝‍🧑🏿=> _1F9D11F3FE200D1F91D200D1F9D11F3FF_", "🧑🏿‍🤝‍🧑🏻=> _1F9D11F3FF200D1F91D200D1F9D11F3FB_", "🧑🏿‍🤝‍🧑🏼=> _1F9D11F3FF200D1F91D200D1F9D11F3FC_", "🧑🏿‍🤝‍🧑🏽=> _1F9D11F3FF200D1F91D200D1F9D11F3FD_", "🧑🏿‍🤝‍🧑🏾=> _1F9D11F3FF200D1F91D200D1F9D11F3FE_", "🧑🏿‍🤝‍🧑🏿=> _1F9D11F3FF200D1F91D200D1F9D11F3FF_", "👭=> _1F46D_", "👭🏻=> _1F46D1F3FB_", "👩🏻‍🤝‍👩🏼=> _1F4691F3FB200D1F91D200D1F4691F3FC_", "👩🏻‍🤝‍👩🏽=> _1F4691F3FB200D1F91D200D1F4691F3FD_", "👩🏻‍🤝‍👩🏾=> _1F4691F3FB200D1F91D200D1F4691F3FE_", "👩🏻‍🤝‍👩🏿=> _1F4691F3FB200D1F91D200D1F4691F3FF_", "👩🏼‍🤝‍👩🏻=> _1F4691F3FC200D1F91D200D1F4691F3FB_", "👭🏼=> _1F46D1F3FC_", "👩🏼‍🤝‍👩🏽=> _1F4691F3FC200D1F91D200D1F4691F3FD_", "👩🏼‍🤝‍👩🏾=> _1F4691F3FC200D1F91D200D1F4691F3FE_", "👩🏼‍🤝‍👩🏿=> _1F4691F3FC200D1F91D200D1F4691F3FF_", "👩🏽‍🤝‍👩🏻=> _1F4691F3FD200D1F91D200D1F4691F3FB_", "👩🏽‍🤝‍👩🏼=> _1F4691F3FD200D1F91D200D1F4691F3FC_", "👭🏽=> _1F46D1F3FD_", "👩🏽‍🤝‍👩🏾=> _1F4691F3FD200D1F91D200D1F4691F3FE_", "👩🏽‍🤝‍👩🏿=> _1F4691F3FD200D1F91D200D1F4691F3FF_", "👩🏾‍🤝‍👩🏻=> _1F4691F3FE200D1F91D200D1F4691F3FB_", "👩🏾‍🤝‍👩🏼=> _1F4691F3FE200D1F91D200D1F4691F3FC_", "👩🏾‍🤝‍👩🏽=> _1F4691F3FE200D1F91D200D1F4691F3FD_", "👭🏾=> _1F46D1F3FE_", "👩🏾‍🤝‍👩🏿=> _1F4691F3FE200D1F91D200D1F4691F3FF_", "👩🏿‍🤝‍👩🏻=> _1F4691F3FF200D1F91D200D1F4691F3FB_", "👩🏿‍🤝‍👩🏼=> _1F4691F3FF200D1F91D200D1F4691F3FC_", "👩🏿‍🤝‍👩🏽=> _1F4691F3FF200D1F91D200D1F4691F3FD_", "👩🏿‍🤝‍👩🏾=> _1F4691F3FF200D1F91D200D1F4691F3FE_", "👭🏿=> _1F46D1F3FF_", "👫=> _1F46B_", "👫🏻=> _1F46B1F3FB_", "👩🏻‍🤝‍👨🏼=> _1F4691F3FB200D1F91D200D1F4681F3FC_", "👩🏻‍🤝‍👨🏽=> _1F4691F3FB200D1F91D200D1F4681F3FD_", "👩🏻‍🤝‍👨🏾=> _1F4691F3FB200D1F91D200D1F4681F3FE_", "👩🏻‍🤝‍👨🏿=> _1F4691F3FB200D1F91D200D1F4681F3FF_", "👩🏼‍🤝‍👨🏻=> _1F4691F3FC200D1F91D200D1F4681F3FB_", "👫🏼=> _1F46B1F3FC_", "👩🏼‍🤝‍👨🏽=> _1F4691F3FC200D1F91D200D1F4681F3FD_", "👩🏼‍🤝‍👨🏾=> _1F4691F3FC200D1F91D200D1F4681F3FE_", "👩🏼‍🤝‍👨🏿=> _1F4691F3FC200D1F91D200D1F4681F3FF_", "👩🏽‍🤝‍👨🏻=> _1F4691F3FD200D1F91D200D1F4681F3FB_", "👩🏽‍🤝‍👨🏼=> _1F4691F3FD200D1F91D200D1F4681F3FC_", "👫🏽=> _1F46B1F3FD_", "👩🏽‍🤝‍👨🏾=> _1F4691F3FD200D1F91D200D1F4681F3FE_", "👩🏽‍🤝‍👨🏿=> _1F4691F3FD200D1F91D200D1F4681F3FF_", "👩🏾‍🤝‍👨🏻=> _1F4691F3FE200D1F91D200D1F4681F3FB_", "👩🏾‍🤝‍👨🏼=> _1F4691F3FE200D1F91D200D1F4681F3FC_", "👩🏾‍🤝‍👨🏽=> _1F4691F3FE200D1F91D200D1F4681F3FD_", "👫🏾=> _1F46B1F3FE_", "👩🏾‍🤝‍👨🏿=> _1F4691F3FE200D1F91D200D1F4681F3FF_", "👩🏿‍🤝‍👨🏻=> _1F4691F3FF200D1F91D200D1F4681F3FB_", "👩🏿‍🤝‍👨🏼=> _1F4691F3FF200D1F91D200D1F4681F3FC_", "👩🏿‍🤝‍👨🏽=> _1F4691F3FF200D1F91D200D1F4681F3FD_", "👩🏿‍🤝‍👨🏾=> _1F4691F3FF200D1F91D200D1F4681F3FE_", "👫🏿=> _1F46B1F3FF_", "👬=> _1F46C_", "👬🏻=> _1F46C1F3FB_", "👨🏻‍🤝‍👨🏼=> _1F4681F3FB200D1F91D200D1F4681F3FC_", "👨🏻‍🤝‍👨🏽=> _1F4681F3FB200D1F91D200D1F4681F3FD_", "👨🏻‍🤝‍👨🏾=> _1F4681F3FB200D1F91D200D1F4681F3FE_", "👨🏻‍🤝‍👨🏿=> _1F4681F3FB200D1F91D200D1F4681F3FF_", "👨🏼‍🤝‍👨🏻=> _1F4681F3FC200D1F91D200D1F4681F3FB_", "👬🏼=> _1F46C1F3FC_", "👨🏼‍🤝‍👨🏽=> _1F4681F3FC200D1F91D200D1F4681F3FD_", "👨🏼‍🤝‍👨🏾=> _1F4681F3FC200D1F91D200D1F4681F3FE_", "👨🏼‍🤝‍👨🏿=> _1F4681F3FC200D1F91D200D1F4681F3FF_", "👨🏽‍🤝‍👨🏻=> _1F4681F3FD200D1F91D200D1F4681F3FB_", "👨🏽‍🤝‍👨🏼=> _1F4681F3FD200D1F91D200D1F4681F3FC_", "👬🏽=> _1F46C1F3FD_", "👨🏽‍🤝‍👨🏾=> _1F4681F3FD200D1F91D200D1F4681F3FE_", "👨🏽‍🤝‍👨🏿=> _1F4681F3FD200D1F91D200D1F4681F3FF_", "👨🏾‍🤝‍👨🏻=> _1F4681F3FE200D1F91D200D1F4681F3FB_", "👨🏾‍🤝‍👨🏼=> _1F4681F3FE200D1F91D200D1F4681F3FC_", "👨🏾‍🤝‍👨🏽=> _1F4681F3FE200D1F91D200D1F4681F3FD_", "👬🏾=> _1F46C1F3FE_", "👨🏾‍🤝‍👨🏿=> _1F4681F3FE200D1F91D200D1F4681F3FF_", "👨🏿‍🤝‍👨🏻=> _1F4681F3FF200D1F91D200D1F4681F3FB_", "👨🏿‍🤝‍👨🏼=> _1F4681F3FF200D1F91D200D1F4681F3FC_", "👨🏿‍🤝‍👨🏽=> _1F4681F3FF200D1F91D200D1F4681F3FD_", "👨🏿‍🤝‍👨🏾=> _1F4681F3FF200D1F91D200D1F4681F3FE_", "👬🏿=> _1F46C1F3FF_", "💏=> _1F48F_", "👩‍❤️‍💋‍👨=> _1F469200D2764FE0F200D1F48B200D1F468_", "👨‍❤️‍💋‍👨=> _1F468200D2764FE0F200D1F48B200D1F468_", "👩‍❤️‍💋‍👩=> _1F469200D2764FE0F200D1F48B200D1F469_", "💑=> _1F491_", "👩‍❤️‍👨=> _1F469200D2764FE0F200D1F468_", "👨‍❤️‍👨=> _1F468200D2764FE0F200D1F468_", "👩‍❤️‍👩=> _1F469200D2764FE0F200D1F469_", "👪=> _1F46A_", "👨‍👩‍👦=> _1F468200D1F469200D1F466_", "👨‍👩‍👧=> _1F468200D1F469200D1F467_", "👨‍👩‍👧‍👦=> _1F468200D1F469200D1F467200D1F466_", "👨‍👩‍👦‍👦=> _1F468200D1F469200D1F466200D1F466_", "👨‍👩‍👧‍👧=> _1F468200D1F469200D1F467200D1F467_", "👨‍👨‍👦=> _1F468200D1F468200D1F466_", "👨‍👨‍👧=> _1F468200D1F468200D1F467_", "👨‍👨‍👧‍👦=> _1F468200D1F468200D1F467200D1F466_", "👨‍👨‍👦‍👦=> _1F468200D1F468200D1F466200D1F466_", "👨‍👨‍👧‍👧=> _1F468200D1F468200D1F467200D1F467_", "👩‍👩‍👦=> _1F469200D1F469200D1F466_", "👩‍👩‍👧=> _1F469200D1F469200D1F467_", "👩‍👩‍👧‍👦=> _1F469200D1F469200D1F467200D1F466_", "👩‍👩‍👦‍👦=> _1F469200D1F469200D1F466200D1F466_", "👩‍👩‍👧‍👧=> _1F469200D1F469200D1F467200D1F467_", "👨‍👦=> _1F468200D1F466_", "👨‍👦‍👦=> _1F468200D1F466200D1F466_", "👨‍👧=> _1F468200D1F467_", "👨‍👧‍👦=> _1F468200D1F467200D1F466_", "👨‍👧‍👧=> _1F468200D1F467200D1F467_", "👩‍👦=> _1F469200D1F466_", "👩‍👦‍👦=> _1F469200D1F466200D1F466_", "👩‍👧=> _1F469200D1F467_", "👩‍👧‍👦=> _1F469200D1F467200D1F466_", "👩‍👧‍👧=> _1F469200D1F467200D1F467_", "🗣️=> _1F5E3FE0F_", "🗣=> _1F5E3_", "👤=> _1F464_", "👥=> _1F465_", "🫂=> _1FAC2_", "👣=> _1F463_", "🦰=> _1F9B0_", "🦱=> _1F9B1_", "🦳=> _1F9B3_", "🦲=> _1F9B2_", "🐵=> _1F435_", "🐒=> _1F412_", "🦍=> _1F98D_", "🦧=> _1F9A7_", "🐶=> _1F436_", "🐕=> _1F415_", "🦮=> _1F9AE_", "🐕‍🦺=> _1F415200D1F9BA_", "🐩=> _1F429_", "🐺=> _1F43A_", "🦊=> _1F98A_", "🦝=> _1F99D_", "🐱=> _1F431_", "🐈=> _1F408_", "🐈‍⬛=> _1F408200D2B1B_", "🦁=> _1F981_", "🐯=> _1F42F_", "🐅=> _1F405_", "🐆=> _1F406_", "🐴=> _1F434_", "🐎=> _1F40E_", "🦄=> _1F984_", "🦓=> _1F993_", "🦌=> _1F98C_", "🦬=> _1F9AC_", "🐮=> _1F42E_", "🐂=> _1F402_", "🐃=> _1F403_", "🐄=> _1F404_", "🐷=> _1F437_", "🐖=> _1F416_", "🐗=> _1F417_", "🐽=> _1F43D_", "🐏=> _1F40F_", "🐑=> _1F411_", "🐐=> _1F410_", "🐪=> _1F42A_", "🐫=> _1F42B_", "🦙=> _1F999_", "🦒=> _1F992_", "🐘=> _1F418_", "🦣=> _1F9A3_", "🦏=> _1F98F_", "🦛=> _1F99B_", "🐭=> _1F42D_", "🐁=> _1F401_", "🐀=> _1F400_", "🐹=> _1F439_", "🐰=> _1F430_", "🐇=> _1F407_", "🐿️=> _1F43FFE0F_", "🐿=> _1F43F_", "🦫=> _1F9AB_", "🦔=> _1F994_", "🦇=> _1F987_", "🐻=> _1F43B_", "🐻‍❄️=> _1F43B200D2744FE0F_", "🐨=> _1F428_", "🐼=> _1F43C_", "🦥=> _1F9A5_", "🦦=> _1F9A6_", "🦨=> _1F9A8_", "🦘=> _1F998_", "🦡=> _1F9A1_", "🐾=> _1F43E_", "🦃=> _1F983_", "🐔=> _1F414_", "🐓=> _1F413_", "🐣=> _1F423_", "🐤=> _1F424_", "🐥=> _1F425_", "🐦=> _1F426_", "🐧=> _1F427_", "🕊️=> _1F54AFE0F_", "🕊=> _1F54A_", "🦅=> _1F985_", "🦆=> _1F986_", "🦢=> _1F9A2_", "🦉=> _1F989_", "🦤=> _1F9A4_", "🪶=> _1FAB6_", "🦩=> _1F9A9_", "🦚=> _1F99A_", "🦜=> _1F99C_", "🐸=> _1F438_", "🐊=> _1F40A_", "🐢=> _1F422_", "🦎=> _1F98E_", "🐍=> _1F40D_", "🐲=> _1F432_", "🐉=> _1F409_", "🦕=> _1F995_", "🦖=> _1F996_", "🐳=> _1F433_", "🐋=> _1F40B_", "🐬=> _1F42C_", "🦭=> _1F9AD_", "🐟=> _1F41F_", "🐠=> _1F420_", "🐡=> _1F421_", "🦈=> _1F988_", "🐙=> _1F419_", "🐚=> _1F41A_", "🐌=> _1F40C_", "🦋=> _1F98B_", "🐛=> _1F41B_", "🐜=> _1F41C_", "🐝=> _1F41D_", "🪲=> _1FAB2_", "🐞=> _1F41E_", "🦗=> _1F997_", "🪳=> _1FAB3_", "🕷️=> _1F577FE0F_", "🕷=> _1F577_", "🕸️=> _1F578FE0F_", "🕸=> _1F578_", "🦂=> _1F982_", "🦟=> _1F99F_", "🪰=> _1FAB0_", "🪱=> _1FAB1_", "🦠=> _1F9A0_", "💐=> _1F490_", "🌸=> _1F338_", "💮=> _1F4AE_", "🏵️=> _1F3F5FE0F_", "🏵=> _1F3F5_", "🌹=> _1F339_", "🥀=> _1F940_", "🌺=> _1F33A_", "🌻=> _1F33B_", "🌼=> _1F33C_", "🌷=> _1F337_", "🌱=> _1F331_", "🪴=> _1FAB4_", "🌲=> _1F332_", "🌳=> _1F333_", "🌴=> _1F334_", "🌵=> _1F335_", "🌾=> _1F33E_", "🌿=> _1F33F_", "☘️=> _2618FE0F_", "☘=> _2618_", "🍀=> _1F340_", "🍁=> _1F341_", "🍂=> _1F342_", "🍃=> _1F343_", "🍇=> _1F347_", "🍈=> _1F348_", "🍉=> _1F349_", "🍊=> _1F34A_", "🍋=> _1F34B_", "🍌=> _1F34C_", "🍍=> _1F34D_", "🥭=> _1F96D_", "🍎=> _1F34E_", "🍏=> _1F34F_", "🍐=> _1F350_", "🍑=> _1F351_", "🍒=> _1F352_", "🍓=> _1F353_", "🫐=> _1FAD0_", "🥝=> _1F95D_", "🍅=> _1F345_", "🫒=> _1FAD2_", "🥥=> _1F965_", "🥑=> _1F951_", "🍆=> _1F346_", "🥔=> _1F954_", "🥕=> _1F955_", "🌽=> _1F33D_", "🌶️=> _1F336FE0F_", "🌶=> _1F336_", "🫑=> _1FAD1_", "🥒=> _1F952_", "🥬=> _1F96C_", "🥦=> _1F966_", "🧄=> _1F9C4_", "🧅=> _1F9C5_", "🍄=> _1F344_", "🥜=> _1F95C_", "🌰=> _1F330_", "🍞=> _1F35E_", "🥐=> _1F950_", "🥖=> _1F956_", "🫓=> _1FAD3_", "🥨=> _1F968_", "🥯=> _1F96F_", "🥞=> _1F95E_", "🧇=> _1F9C7_", "🧀=> _1F9C0_", "🍖=> _1F356_", "🍗=> _1F357_", "🥩=> _1F969_", "🥓=> _1F953_", "🍔=> _1F354_", "🍟=> _1F35F_", "🍕=> _1F355_", "🌭=> _1F32D_", "🥪=> _1F96A_", "🌮=> _1F32E_", "🌯=> _1F32F_", "🫔=> _1FAD4_", "🥙=> _1F959_", "🧆=> _1F9C6_", "🥚=> _1F95A_", "🍳=> _1F373_", "🥘=> _1F958_", "🍲=> _1F372_", "🫕=> _1FAD5_", "🥣=> _1F963_", "🥗=> _1F957_", "🍿=> _1F37F_", "🧈=> _1F9C8_", "🧂=> _1F9C2_", "🥫=> _1F96B_", "🍱=> _1F371_", "🍘=> _1F358_", "🍙=> _1F359_", "🍚=> _1F35A_", "🍛=> _1F35B_", "🍜=> _1F35C_", "🍝=> _1F35D_", "🍠=> _1F360_", "🍢=> _1F362_", "🍣=> _1F363_", "🍤=> _1F364_", "🍥=> _1F365_", "🥮=> _1F96E_", "🍡=> _1F361_", "🥟=> _1F95F_", "🥠=> _1F960_", "🥡=> _1F961_", "🦀=> _1F980_", "🦞=> _1F99E_", "🦐=> _1F990_", "🦑=> _1F991_", "🦪=> _1F9AA_", "🍦=> _1F366_", "🍧=> _1F367_", "🍨=> _1F368_", "🍩=> _1F369_", "🍪=> _1F36A_", "🎂=> _1F382_", "🍰=> _1F370_", "🧁=> _1F9C1_", "🥧=> _1F967_", "🍫=> _1F36B_", "🍬=> _1F36C_", "🍭=> _1F36D_", "🍮=> _1F36E_", "🍯=> _1F36F_", "🍼=> _1F37C_", "🥛=> _1F95B_", "☕=> _2615_", "🫖=> _1FAD6_", "🍵=> _1F375_", "🍶=> _1F376_", "🍾=> _1F37E_", "🍷=> _1F377_", "🍸=> _1F378_", "🍹=> _1F379_", "🍺=> _1F37A_", "🍻=> _1F37B_", "🥂=> _1F942_", "🥃=> _1F943_", "🥤=> _1F964_", "🧋=> _1F9CB_", "🧃=> _1F9C3_", "🧉=> _1F9C9_", "🧊=> _1F9CA_", "🥢=> _1F962_", "🍽️=> _1F37DFE0F_", "🍽=> _1F37D_", "🍴=> _1F374_", "🥄=> _1F944_", "🔪=> _1F52A_", "🏺=> _1F3FA_", "🌍=> _1F30D_", "🌎=> _1F30E_", "🌏=> _1F30F_", "🌐=> _1F310_", "🗺️=> _1F5FAFE0F_", "🗺=> _1F5FA_", "🗾=> _1F5FE_", "🧭=> _1F9ED_", "🏔️=> _1F3D4FE0F_", "🏔=> _1F3D4_", "⛰️=> _26F0FE0F_", "⛰=> _26F0_", "🌋=> _1F30B_", "🗻=> _1F5FB_", "🏕️=> _1F3D5FE0F_", "🏕=> _1F3D5_", "🏖️=> _1F3D6FE0F_", "🏖=> _1F3D6_", "🏜️=> _1F3DCFE0F_", "🏜=> _1F3DC_", "🏝️=> _1F3DDFE0F_", "🏝=> _1F3DD_", "🏞️=> _1F3DEFE0F_", "🏞=> _1F3DE_", "🏟️=> _1F3DFFE0F_", "🏟=> _1F3DF_", "🏛️=> _1F3DBFE0F_", "🏛=> _1F3DB_", "🏗️=> _1F3D7FE0F_", "🏗=> _1F3D7_", "🧱=> _1F9F1_", "🪨=> _1FAA8_", "🪵=> _1FAB5_", "🛖=> _1F6D6_", "🏘️=> _1F3D8FE0F_", "🏘=> _1F3D8_", "🏚️=> _1F3DAFE0F_", "🏚=> _1F3DA_", "🏠=> _1F3E0_", "🏡=> _1F3E1_", "🏢=> _1F3E2_", "🏣=> _1F3E3_", "🏤=> _1F3E4_", "🏥=> _1F3E5_", "🏦=> _1F3E6_", "🏨=> _1F3E8_", "🏩=> _1F3E9_", "🏪=> _1F3EA_", "🏫=> _1F3EB_", "🏬=> _1F3EC_", "🏭=> _1F3ED_", "🏯=> _1F3EF_", "🏰=> _1F3F0_", "💒=> _1F492_", "🗼=> _1F5FC_", "🗽=> _1F5FD_", "⛪=> _26EA_", "🕌=> _1F54C_", "🛕=> _1F6D5_", "🕍=> _1F54D_", "⛩️=> _26E9FE0F_", "⛩=> _26E9_", "🕋=> _1F54B_", "⛲=> _26F2_", "⛺=> _26FA_", "🌁=> _1F301_", "🌃=> _1F303_", "🏙️=> _1F3D9FE0F_", "🏙=> _1F3D9_", "🌄=> _1F304_", "🌅=> _1F305_", "🌆=> _1F306_", "🌇=> _1F307_", "🌉=> _1F309_", "♨️=> _2668FE0F_", "♨=> _2668_", "🎠=> _1F3A0_", "🎡=> _1F3A1_", "🎢=> _1F3A2_", "💈=> _1F488_", "🎪=> _1F3AA_", "🚂=> _1F682_", "🚃=> _1F683_", "🚄=> _1F684_", "🚅=> _1F685_", "🚆=> _1F686_", "🚇=> _1F687_", "🚈=> _1F688_", "🚉=> _1F689_", "🚊=> _1F68A_", "🚝=> _1F69D_", "🚞=> _1F69E_", "🚋=> _1F68B_", "🚌=> _1F68C_", "🚍=> _1F68D_", "🚎=> _1F68E_", "🚐=> _1F690_", "🚑=> _1F691_", "🚒=> _1F692_", "🚓=> _1F693_", "🚔=> _1F694_", "🚕=> _1F695_", "🚖=> _1F696_", "🚗=> _1F697_", "🚘=> _1F698_", "🚙=> _1F699_", "🛻=> _1F6FB_", "🚚=> _1F69A_", "🚛=> _1F69B_", "🚜=> _1F69C_", "🏎️=> _1F3CEFE0F_", "🏎=> _1F3CE_", "🏍️=> _1F3CDFE0F_", "🏍=> _1F3CD_", "🛵=> _1F6F5_", "🦽=> _1F9BD_", "🦼=> _1F9BC_", "🛺=> _1F6FA_", "🚲=> _1F6B2_", "🛴=> _1F6F4_", "🛹=> _1F6F9_", "🛼=> _1F6FC_", "🚏=> _1F68F_", "🛣️=> _1F6E3FE0F_", "🛣=> _1F6E3_", "🛤️=> _1F6E4FE0F_", "🛤=> _1F6E4_", "🛢️=> _1F6E2FE0F_", "🛢=> _1F6E2_", "⛽=> _26FD_", "🚨=> _1F6A8_", "🚥=> _1F6A5_", "🚦=> _1F6A6_", "🛑=> _1F6D1_", "🚧=> _1F6A7_", "⚓=> _2693_", "⛵=> _26F5_", "🛶=> _1F6F6_", "🚤=> _1F6A4_", "🛳️=> _1F6F3FE0F_", "🛳=> _1F6F3_", "⛴️=> _26F4FE0F_", "⛴=> _26F4_", "🛥️=> _1F6E5FE0F_", "🛥=> _1F6E5_", "🚢=> _1F6A2_", "✈️=> _2708FE0F_", "✈=> _2708_", "🛩️=> _1F6E9FE0F_", "🛩=> _1F6E9_", "🛫=> _1F6EB_", "🛬=> _1F6EC_", "🪂=> _1FA82_", "💺=> _1F4BA_", "🚁=> _1F681_", "🚟=> _1F69F_", "🚠=> _1F6A0_", "🚡=> _1F6A1_", "🛰️=> _1F6F0FE0F_", "🛰=> _1F6F0_", "🚀=> _1F680_", "🛸=> _1F6F8_", "🛎️=> _1F6CEFE0F_", "🛎=> _1F6CE_", "🧳=> _1F9F3_", "⌛=> _231B_", "⏳=> _23F3_", "⌚=> _231A_", "⏰=> _23F0_", "⏱️=> _23F1FE0F_", "⏱=> _23F1_", "⏲️=> _23F2FE0F_", "⏲=> _23F2_", "🕰️=> _1F570FE0F_", "🕰=> _1F570_", "🕛=> _1F55B_", "🕧=> _1F567_", "🕐=> _1F550_", "🕜=> _1F55C_", "🕑=> _1F551_", "🕝=> _1F55D_", "🕒=> _1F552_", "🕞=> _1F55E_", "🕓=> _1F553_", "🕟=> _1F55F_", "🕔=> _1F554_", "🕠=> _1F560_", "🕕=> _1F555_", "🕡=> _1F561_", "🕖=> _1F556_", "🕢=> _1F562_", "🕗=> _1F557_", "🕣=> _1F563_", "🕘=> _1F558_", "🕤=> _1F564_", "🕙=> _1F559_", "🕥=> _1F565_", "🕚=> _1F55A_", "🕦=> _1F566_", "🌑=> _1F311_", "🌒=> _1F312_", "🌓=> _1F313_", "🌔=> _1F314_", "🌕=> _1F315_", "🌖=> _1F316_", "🌗=> _1F317_", "🌘=> _1F318_", "🌙=> _1F319_", "🌚=> _1F31A_", "🌛=> _1F31B_", "🌜=> _1F31C_", "🌡️=> _1F321FE0F_", "🌡=> _1F321_", "☀️=> _2600FE0F_", "☀=> _2600_", "🌝=> _1F31D_", "🌞=> _1F31E_", "🪐=> _1FA90_", "⭐=> _2B50_", "🌟=> _1F31F_", "🌠=> _1F320_", "🌌=> _1F30C_", "☁️=> _2601FE0F_", "☁=> _2601_", "⛅=> _26C5_", "⛈️=> _26C8FE0F_", "⛈=> _26C8_", "🌤️=> _1F324FE0F_", "🌤=> _1F324_", "🌥️=> _1F325FE0F_", "🌥=> _1F325_", "🌦️=> _1F326FE0F_", "🌦=> _1F326_", "🌧️=> _1F327FE0F_", "🌧=> _1F327_", "🌨️=> _1F328FE0F_", "🌨=> _1F328_", "🌩️=> _1F329FE0F_", "🌩=> _1F329_", "🌪️=> _1F32AFE0F_", "🌪=> _1F32A_", "🌫️=> _1F32BFE0F_", "🌫=> _1F32B_", "🌬️=> _1F32CFE0F_", "🌬=> _1F32C_", "🌀=> _1F300_", "🌈=> _1F308_", "🌂=> _1F302_", "☂️=> _2602FE0F_", "☂=> _2602_", "☔=> _2614_", "⛱️=> _26F1FE0F_", "⛱=> _26F1_", "⚡=> _26A1_", "❄️=> _2744FE0F_", "❄=> _2744_", "☃️=> _2603FE0F_", "☃=> _2603_", "⛄=> _26C4_", "☄️=> _2604FE0F_", "☄=> _2604_", "🔥=> _1F525_", "💧=> _1F4A7_", "🌊=> _1F30A_", "🎃=> _1F383_", "🎄=> _1F384_", "🎆=> _1F386_", "🎇=> _1F387_", "🧨=> _1F9E8_", "✨=> _2728_", "🎈=> _1F388_", "🎉=> _1F389_", "🎊=> _1F38A_", "🎋=> _1F38B_", "🎍=> _1F38D_", "🎎=> _1F38E_", "🎏=> _1F38F_", "🎐=> _1F390_", "🎑=> _1F391_", "🧧=> _1F9E7_", "🎀=> _1F380_", "🎁=> _1F381_", "🎗️=> _1F397FE0F_", "🎗=> _1F397_", "🎟️=> _1F39FFE0F_", "🎟=> _1F39F_", "🎫=> _1F3AB_", "🎖️=> _1F396FE0F_", "🎖=> _1F396_", "🏆=> _1F3C6_", "🏅=> _1F3C5_", "🥇=> _1F947_", "🥈=> _1F948_", "🥉=> _1F949_", "⚽=> _26BD_", "⚾=> _26BE_", "🥎=> _1F94E_", "🏀=> _1F3C0_", "🏐=> _1F3D0_", "🏈=> _1F3C8_", "🏉=> _1F3C9_", "🎾=> _1F3BE_", "🥏=> _1F94F_", "🎳=> _1F3B3_", "🏏=> _1F3CF_", "🏑=> _1F3D1_", "🏒=> _1F3D2_", "🥍=> _1F94D_", "🏓=> _1F3D3_", "🏸=> _1F3F8_", "🥊=> _1F94A_", "🥋=> _1F94B_", "🥅=> _1F945_", "⛳=> _26F3_", "⛸️=> _26F8FE0F_", "⛸=> _26F8_", "🎣=> _1F3A3_", "🤿=> _1F93F_", "🎽=> _1F3BD_", "🎿=> _1F3BF_", "🛷=> _1F6F7_", "🥌=> _1F94C_", "🎯=> _1F3AF_", "🪀=> _1FA80_", "🪁=> _1FA81_", "🎱=> _1F3B1_", "🔮=> _1F52E_", "🪄=> _1FA84_", "🧿=> _1F9FF_", "🎮=> _1F3AE_", "🕹️=> _1F579FE0F_", "🕹=> _1F579_", "🎰=> _1F3B0_", "🎲=> _1F3B2_", "🧩=> _1F9E9_", "🧸=> _1F9F8_", "🪅=> _1FA85_", "🪆=> _1FA86_", "♠️=> _2660FE0F_", "♠=> _2660_", "♥️=> _2665FE0F_", "♥=> _2665_", "♦️=> _2666FE0F_", "♦=> _2666_", "♣️=> _2663FE0F_", "♣=> _2663_", "♟️=> _265FFE0F_", "♟=> _265F_", "🃏=> _1F0CF_", "🀄=> _1F004_", "🎴=> _1F3B4_", "🎭=> _1F3AD_", "🖼️=> _1F5BCFE0F_", "🖼=> _1F5BC_", "🎨=> _1F3A8_", "🧵=> _1F9F5_", "🪡=> _1FAA1_", "🧶=> _1F9F6_", "🪢=> _1FAA2_", "👓=> _1F453_", "🕶️=> _1F576FE0F_", "🕶=> _1F576_", "🥽=> _1F97D_", "🥼=> _1F97C_", "🦺=> _1F9BA_", "👔=> _1F454_", "👕=> _1F455_", "👖=> _1F456_", "🧣=> _1F9E3_", "🧤=> _1F9E4_", "🧥=> _1F9E5_", "🧦=> _1F9E6_", "👗=> _1F457_", "👘=> _1F458_", "🥻=> _1F97B_", "🩱=> _1FA71_", "🩲=> _1FA72_", "🩳=> _1FA73_", "👙=> _1F459_", "👚=> _1F45A_", "👛=> _1F45B_", "👜=> _1F45C_", "👝=> _1F45D_", "🛍️=> _1F6CDFE0F_", "🛍=> _1F6CD_", "🎒=> _1F392_", "🩴=> _1FA74_", "👞=> _1F45E_", "👟=> _1F45F_", "🥾=> _1F97E_", "🥿=> _1F97F_", "👠=> _1F460_", "👡=> _1F461_", "🩰=> _1FA70_", "👢=> _1F462_", "👑=> _1F451_", "👒=> _1F452_", "🎩=> _1F3A9_", "🎓=> _1F393_", "🧢=> _1F9E2_", "🪖=> _1FA96_", "⛑️=> _26D1FE0F_", "⛑=> _26D1_", "📿=> _1F4FF_", "💄=> _1F484_", "💍=> _1F48D_", "💎=> _1F48E_", "🔇=> _1F507_", "🔈=> _1F508_", "🔉=> _1F509_", "🔊=> _1F50A_", "📢=> _1F4E2_", "📣=> _1F4E3_", "📯=> _1F4EF_", "🔔=> _1F514_", "🔕=> _1F515_", "🎼=> _1F3BC_", "🎵=> _1F3B5_", "🎶=> _1F3B6_", "🎙️=> _1F399FE0F_", "🎙=> _1F399_", "🎚️=> _1F39AFE0F_", "🎚=> _1F39A_", "🎛️=> _1F39BFE0F_", "🎛=> _1F39B_", "🎤=> _1F3A4_", "🎧=> _1F3A7_", "📻=> _1F4FB_", "🎷=> _1F3B7_", "🪗=> _1FA97_", "🎸=> _1F3B8_", "🎹=> _1F3B9_", "🎺=> _1F3BA_", "🎻=> _1F3BB_", "🪕=> _1FA95_", "🥁=> _1F941_", "🪘=> _1FA98_", "📱=> _1F4F1_", "📲=> _1F4F2_", "☎️=> _260EFE0F_", "☎=> _260E_", "📞=> _1F4DE_", "📟=> _1F4DF_", "📠=> _1F4E0_", "🔋=> _1F50B_", "🔌=> _1F50C_", "💻=> _1F4BB_", "🖥️=> _1F5A5FE0F_", "🖥=> _1F5A5_", "🖨️=> _1F5A8FE0F_", "🖨=> _1F5A8_", "⌨️=> _2328FE0F_", "⌨=> _2328_", "🖱️=> _1F5B1FE0F_", "🖱=> _1F5B1_", "🖲️=> _1F5B2FE0F_", "🖲=> _1F5B2_", "💽=> _1F4BD_", "💾=> _1F4BE_", "💿=> _1F4BF_", "📀=> _1F4C0_", "🧮=> _1F9EE_", "🎥=> _1F3A5_", "🎞️=> _1F39EFE0F_", "🎞=> _1F39E_", "📽️=> _1F4FDFE0F_", "📽=> _1F4FD_", "🎬=> _1F3AC_", "📺=> _1F4FA_", "📷=> _1F4F7_", "📸=> _1F4F8_", "📹=> _1F4F9_", "📼=> _1F4FC_", "🔍=> _1F50D_", "🔎=> _1F50E_", "🕯️=> _1F56FFE0F_", "🕯=> _1F56F_", "💡=> _1F4A1_", "🔦=> _1F526_", "🏮=> _1F3EE_", "🪔=> _1FA94_", "📔=> _1F4D4_", "📕=> _1F4D5_", "📖=> _1F4D6_", "📗=> _1F4D7_", "📘=> _1F4D8_", "📙=> _1F4D9_", "📚=> _1F4DA_", "📓=> _1F4D3_", "📒=> _1F4D2_", "📃=> _1F4C3_", "📜=> _1F4DC_", "📄=> _1F4C4_", "📰=> _1F4F0_", "🗞️=> _1F5DEFE0F_", "🗞=> _1F5DE_", "📑=> _1F4D1_", "🔖=> _1F516_", "🏷️=> _1F3F7FE0F_", "🏷=> _1F3F7_", "💰=> _1F4B0_", "🪙=> _1FA99_", "💴=> _1F4B4_", "💵=> _1F4B5_", "💶=> _1F4B6_", "💷=> _1F4B7_", "💸=> _1F4B8_", "💳=> _1F4B3_", "🧾=> _1F9FE_", "💹=> _1F4B9_", "✉️=> _2709FE0F_", "✉=> _2709_", "📧=> _1F4E7_", "📨=> _1F4E8_", "📩=> _1F4E9_", "📤=> _1F4E4_", "📥=> _1F4E5_", "📦=> _1F4E6_", "📫=> _1F4EB_", "📪=> _1F4EA_", "📬=> _1F4EC_", "📭=> _1F4ED_", "📮=> _1F4EE_", "🗳️=> _1F5F3FE0F_", "🗳=> _1F5F3_", "✏️=> _270FFE0F_", "✏=> _270F_", "✒️=> _2712FE0F_", "✒=> _2712_", "🖋️=> _1F58BFE0F_", "🖋=> _1F58B_", "🖊️=> _1F58AFE0F_", "🖊=> _1F58A_", "🖌️=> _1F58CFE0F_", "🖌=> _1F58C_", "🖍️=> _1F58DFE0F_", "🖍=> _1F58D_", "📝=> _1F4DD_", "💼=> _1F4BC_", "📁=> _1F4C1_", "📂=> _1F4C2_", "🗂️=> _1F5C2FE0F_", "🗂=> _1F5C2_", "📅=> _1F4C5_", "📆=> _1F4C6_", "🗒️=> _1F5D2FE0F_", "🗒=> _1F5D2_", "🗓️=> _1F5D3FE0F_", "🗓=> _1F5D3_", "📇=> _1F4C7_", "📈=> _1F4C8_", "📉=> _1F4C9_", "📊=> _1F4CA_", "📋=> _1F4CB_", "📌=> _1F4CC_", "📍=> _1F4CD_", "📎=> _1F4CE_", "🖇️=> _1F587FE0F_", "🖇=> _1F587_", "📏=> _1F4CF_", "📐=> _1F4D0_", "✂️=> _2702FE0F_", "✂=> _2702_", "🗃️=> _1F5C3FE0F_", "🗃=> _1F5C3_", "🗄️=> _1F5C4FE0F_", "🗄=> _1F5C4_", "🗑️=> _1F5D1FE0F_", "🗑=> _1F5D1_", "🔒=> _1F512_", "🔓=> _1F513_", "🔏=> _1F50F_", "🔐=> _1F510_", "🔑=> _1F511_", "🗝️=> _1F5DDFE0F_", "🗝=> _1F5DD_", "🔨=> _1F528_", "🪓=> _1FA93_", "⛏️=> _26CFFE0F_", "⛏=> _26CF_", "⚒️=> _2692FE0F_", "⚒=> _2692_", "🛠️=> _1F6E0FE0F_", "🛠=> _1F6E0_", "🗡️=> _1F5E1FE0F_", "🗡=> _1F5E1_", "⚔️=> _2694FE0F_", "⚔=> _2694_", "🔫=> _1F52B_", "🪃=> _1FA83_", "🏹=> _1F3F9_", "🛡️=> _1F6E1FE0F_", "🛡=> _1F6E1_", "🪚=> _1FA9A_", "🔧=> _1F527_", "🪛=> _1FA9B_", "🔩=> _1F529_", "⚙️=> _2699FE0F_", "⚙=> _2699_", "🗜️=> _1F5DCFE0F_", "🗜=> _1F5DC_", "⚖️=> _2696FE0F_", "⚖=> _2696_", "🦯=> _1F9AF_", "🔗=> _1F517_", "⛓️=> _26D3FE0F_", "⛓=> _26D3_", "🪝=> _1FA9D_", "🧰=> _1F9F0_", "🧲=> _1F9F2_", "🪜=> _1FA9C_", "⚗️=> _2697FE0F_", "⚗=> _2697_", "🧪=> _1F9EA_", "🧫=> _1F9EB_", "🧬=> _1F9EC_", "🔬=> _1F52C_", "🔭=> _1F52D_", "📡=> _1F4E1_", "💉=> _1F489_", "🩸=> _1FA78_", "💊=> _1F48A_", "🩹=> _1FA79_", "🩺=> _1FA7A_", "🚪=> _1F6AA_", "🛗=> _1F6D7_", "🪞=> _1FA9E_", "🪟=> _1FA9F_", "🛏️=> _1F6CFFE0F_", "🛏=> _1F6CF_", "🛋️=> _1F6CBFE0F_", "🛋=> _1F6CB_", "🪑=> _1FA91_", "🚽=> _1F6BD_", "🪠=> _1FAA0_", "🚿=> _1F6BF_", "🛁=> _1F6C1_", "🪤=> _1FAA4_", "🪒=> _1FA92_", "🧴=> _1F9F4_", "🧷=> _1F9F7_", "🧹=> _1F9F9_", "🧺=> _1F9FA_", "🧻=> _1F9FB_", "🪣=> _1FAA3_", "🧼=> _1F9FC_", "🪥=> _1FAA5_", "🧽=> _1F9FD_", "🧯=> _1F9EF_", "🛒=> _1F6D2_", "🚬=> _1F6AC_", "⚰️=> _26B0FE0F_", "⚰=> _26B0_", "🪦=> _1FAA6_", "⚱️=> _26B1FE0F_", "⚱=> _26B1_", "🗿=> _1F5FF_", "🪧=> _1FAA7_", "🏧=> _1F3E7_", "🚮=> _1F6AE_", "🚰=> _1F6B0_", "♿=> _267F_", "🚹=> _1F6B9_", "🚺=> _1F6BA_", "🚻=> _1F6BB_", "🚼=> _1F6BC_", "🚾=> _1F6BE_", "🛂=> _1F6C2_", "🛃=> _1F6C3_", "🛄=> _1F6C4_", "🛅=> _1F6C5_", "⚠️=> _26A0FE0F_", "⚠=> _26A0_", "🚸=> _1F6B8_", "⛔=> _26D4_", "🚫=> _1F6AB_", "🚳=> _1F6B3_", "🚭=> _1F6AD_", "🚯=> _1F6AF_", "🚱=> _1F6B1_", "🚷=> _1F6B7_", "📵=> _1F4F5_", "🔞=> _1F51E_", "☢️=> _2622FE0F_", "☢=> _2622_", "☣️=> _2623FE0F_", "☣=> _2623_", "⬆️=> _2B06FE0F_", "⬆=> _2B06_", "↗️=> _2197FE0F_", "↗=> _2197_", "➡️=> _27A1FE0F_", "➡=> _27A1_", "↘️=> _2198FE0F_", "↘=> _2198_", "⬇️=> _2B07FE0F_", "⬇=> _2B07_", "↙️=> _2199FE0F_", "↙=> _2199_", "⬅️=> _2B05FE0F_", "⬅=> _2B05_", "↖️=> _2196FE0F_", "↖=> _2196_", "↕️=> _2195FE0F_", "↕=> _2195_", "↔️=> _2194FE0F_", "↔=> _2194_", "↩️=> _21A9FE0F_", "↩=> _21A9_", "↪️=> _21AAFE0F_", "↪=> _21AA_", "⤴️=> _2934FE0F_", "⤴=> _2934_", "⤵️=> _2935FE0F_", "⤵=> _2935_", "🔃=> _1F503_", "🔄=> _1F504_", "🔙=> _1F519_", "🔚=> _1F51A_", "🔛=> _1F51B_", "🔜=> _1F51C_", "🔝=> _1F51D_", "🛐=> _1F6D0_", "⚛️=> _269BFE0F_", "⚛=> _269B_", "🕉️=> _1F549FE0F_", "🕉=> _1F549_", "✡️=> _2721FE0F_", "✡=> _2721_", "☸️=> _2638FE0F_", "☸=> _2638_", "☯️=> _262FFE0F_", "☯=> _262F_", "✝️=> _271DFE0F_", "✝=> _271D_", "☦️=> _2626FE0F_", "☦=> _2626_", "☪️=> _262AFE0F_", "☪=> _262A_", "☮️=> _262EFE0F_", "☮=> _262E_", "🕎=> _1F54E_", "🔯=> _1F52F_", "♈=> _2648_", "♉=> _2649_", "♊=> _264A_", "♋=> _264B_", "♌=> _264C_", "♍=> _264D_", "♎=> _264E_", "♏=> _264F_", "♐=> _2650_", "♑=> _2651_", "♒=> _2652_", "♓=> _2653_", "⛎=> _26CE_", "🔀=> _1F500_", "🔁=> _1F501_", "🔂=> _1F502_", "▶️=> _25B6FE0F_", "▶=> _25B6_", "⏩=> _23E9_", "⏭️=> _23EDFE0F_", "⏭=> _23ED_", "⏯️=> _23EFFE0F_", "⏯=> _23EF_", "◀️=> _25C0FE0F_", "◀=> _25C0_", "⏪=> _23EA_", "⏮️=> _23EEFE0F_", "⏮=> _23EE_", "🔼=> _1F53C_", "⏫=> _23EB_", "🔽=> _1F53D_", "⏬=> _23EC_", "⏸️=> _23F8FE0F_", "⏸=> _23F8_", "⏹️=> _23F9FE0F_", "⏹=> _23F9_", "⏺️=> _23FAFE0F_", "⏺=> _23FA_", "⏏️=> _23CFFE0F_", "⏏=> _23CF_", "🎦=> _1F3A6_", "🔅=> _1F505_", "🔆=> _1F506_", "📶=> _1F4F6_", "📳=> _1F4F3_", "📴=> _1F4F4_", "♀️=> _2640FE0F_", "♀=> _2640_", "♂️=> _2642FE0F_", "♂=> _2642_", "⚧️=> _26A7FE0F_", "⚧=> _26A7_", "✖️=> _2716FE0F_", "✖=> _2716_", "➕=> _2795_", "➖=> _2796_", "➗=> _2797_", "♾️=> _267EFE0F_", "♾=> _267E_", "‼️=> _203CFE0F_", "‼=> _203C_", "⁉️=> _2049FE0F_", "⁉=> _2049_", "❓=> _2753_", "❔=> _2754_", "❕=> _2755_", "❗=> _2757_", "💱=> _1F4B1_", "💲=> _1F4B2_", "⚕️=> _2695FE0F_", "⚕=> _2695_", "♻️=> _267BFE0F_", "♻=> _267B_", "⚜️=> _269CFE0F_", "⚜=> _269C_", "🔱=> _1F531_", "📛=> _1F4DB_", "🔰=> _1F530_", "⭕=> _2B55_", "✅=> _2705_", "☑️=> _2611FE0F_", "☑=> _2611_", "✔️=> _2714FE0F_", "✔=> _2714_", "❌=> _274C_", "❎=> _274E_", "➰=> _27B0_", "➿=> _27BF_", "〽️=> _303DFE0F_", "〽=> _303D_", "✳️=> _2733FE0F_", "✳=> _2733_", "✴️=> _2734FE0F_", "✴=> _2734_", "❇️=> _2747FE0F_", "❇=> _2747_", "©️=> _00A9FE0F_", "©=> _00A9_", "®️=> _00AEFE0F_", "®=> _00AE_", "™️=> _2122FE0F_", "™=> _2122_", "#️⃣=> _0023FE0F20E3_", "0️⃣=> _0030FE0F20E3_", "1️⃣=> _0031FE0F20E3_", "2️⃣=> _0032FE0F20E3_", "3️⃣=> _0033FE0F20E3_", "4️⃣=> _0034FE0F20E3_", "5️⃣=> _0035FE0F20E3_", "6️⃣=> _0036FE0F20E3_", "7️⃣=> _0037FE0F20E3_", "8️⃣=> _0038FE0F20E3_", "9️⃣=> _0039FE0F20E3_", "🔟=> _1F51F_", "🔠=> _1F520_", "🔡=> _1F521_", "🔢=> _1F522_", "🔣=> _1F523_", "🔤=> _1F524_", "🅰️=> _1F170FE0F_", "🅰=> _1F170_", "🆎=> _1F18E_", "🅱️=> _1F171FE0F_", "🅱=> _1F171_", "🆑=> _1F191_", "🆒=> _1F192_", "🆓=> _1F193_", "ℹ️=> _2139FE0F_", "ℹ=> _2139_", "🆔=> _1F194_", "Ⓜ️=> _24C2FE0F_", "Ⓜ=> _24C2_", "🆕=> _1F195_", "🆖=> _1F196_", "🅾️=> _1F17EFE0F_", "🅾=> _1F17E_", "🆗=> _1F197_", "🅿️=> _1F17FFE0F_", "🅿=> _1F17F_", "🆘=> _1F198_", "🆙=> _1F199_", "🆚=> _1F19A_", "🈁=> _1F201_", "🈂️=> _1F202FE0F_", "🈂=> _1F202_", "🈷️=> _1F237FE0F_", "🈷=> _1F237_", "🈶=> _1F236_", "🈯=> _1F22F_", "🉐=> _1F250_", "🈹=> _1F239_", "🈚=> _1F21A_", "🈲=> _1F232_", "🉑=> _1F251_", "🈸=> _1F238_", "🈴=> _1F234_", "🈳=> _1F233_", "㊗️=> _3297FE0F_", "㊗=> _3297_", "㊙️=> _3299FE0F_", "㊙=> _3299_", "🈺=> _1F23A_", "🈵=> _1F235_", "🔴=> _1F534_", "🟠=> _1F7E0_", "🟡=> _1F7E1_", "🟢=> _1F7E2_", "🔵=> _1F535_", "🟣=> _1F7E3_", "🟤=> _1F7E4_", "⚫=> _26AB_", "⚪=> _26AA_", "🟥=> _1F7E5_", "🟧=> _1F7E7_", "🟨=> _1F7E8_", "🟩=> _1F7E9_", "🟦=> _1F7E6_", "🟪=> _1F7EA_", "🟫=> _1F7EB_", "⬛=> _2B1B_", "⬜=> _2B1C_", "◼️=> _25FCFE0F_", "◼=> _25FC_", "◻️=> _25FBFE0F_", "◻=> _25FB_", "◾=> _25FE_", "◽=> _25FD_", "▪️=> _25AAFE0F_", "▪=> _25AA_", "▫️=> _25ABFE0F_", "▫=> _25AB_", "🔶=> _1F536_", "🔷=> _1F537_", "🔸=> _1F538_", "🔹=> _1F539_", "🔺=> _1F53A_", "🔻=> _1F53B_", "💠=> _1F4A0_", "🔘=> _1F518_", "🔳=> _1F533_", "🔲=> _1F532_", "🏁=> _1F3C1_", "🚩=> _1F6A9_", "🎌=> _1F38C_", "🏴=> _1F3F4_", "🏳️=> _1F3F3FE0F_", "🏳=> _1F3F3_", "🏳️‍🌈=> _1F3F3FE0F200D1F308_", "🏳‍🌈=> _1F3F3200D1F308_", "🏳️‍⚧️=> _1F3F3FE0F200D26A7FE0F_", "🏳‍⚧️=> _1F3F3200D26A7FE0F_", "🏳️‍⚧=> _1F3F3FE0F200D26A7_", "🏳‍⚧=> _1F3F3200D26A7_", "🏴‍☠️=> _1F3F4200D2620FE0F_", "🇦🇨=> _1F1E61F1E8_", "🇦🇩=> _1F1E61F1E9_", "🇦🇪=> _1F1E61F1EA_", "🇦🇫=> _1F1E61F1EB_", "🇦🇬=> _1F1E61F1EC_", "🇦🇮=> _1F1E61F1EE_", "🇦🇱=> _1F1E61F1F1_", "🇦🇲=> _1F1E61F1F2_", "🇦🇴=> _1F1E61F1F4_", "🇦🇶=> _1F1E61F1F6_", "🇦🇷=> _1F1E61F1F7_", "🇦🇸=> _1F1E61F1F8_", "🇦🇹=> _1F1E61F1F9_", "🇦🇺=> _1F1E61F1FA_", "🇦🇼=> _1F1E61F1FC_", "🇦🇽=> _1F1E61F1FD_", "🇦🇿=> _1F1E61F1FF_", "🇧🇦=> _1F1E71F1E6_", "🇧🇧=> _1F1E71F1E7_", "🇧🇩=> _1F1E71F1E9_", "🇧🇪=> _1F1E71F1EA_", "🇧🇫=> _1F1E71F1EB_", "🇧🇬=> _1F1E71F1EC_", "🇧🇭=> _1F1E71F1ED_", "🇧🇮=> _1F1E71F1EE_", "🇧🇯=> _1F1E71F1EF_", "🇧🇱=> _1F1E71F1F1_", "🇧🇲=> _1F1E71F1F2_", "🇧🇳=> _1F1E71F1F3_", "🇧🇴=> _1F1E71F1F4_", "🇧🇶=> _1F1E71F1F6_", "🇧🇷=> _1F1E71F1F7_", "🇧🇸=> _1F1E71F1F8_", "🇧🇹=> _1F1E71F1F9_", "🇧🇻=> _1F1E71F1FB_", "🇧🇼=> _1F1E71F1FC_", "🇧🇾=> _1F1E71F1FE_", "🇧🇿=> _1F1E71F1FF_", "🇨🇦=> _1F1E81F1E6_", "🇨🇨=> _1F1E81F1E8_", "🇨🇩=> _1F1E81F1E9_", "🇨🇫=> _1F1E81F1EB_", "🇨🇬=> _1F1E81F1EC_", "🇨🇭=> _1F1E81F1ED_", "🇨🇮=> _1F1E81F1EE_", "🇨🇰=> _1F1E81F1F0_", "🇨🇱=> _1F1E81F1F1_", "🇨🇲=> _1F1E81F1F2_", "🇨🇳=> _1F1E81F1F3_", "🇨🇴=> _1F1E81F1F4_", "🇨🇵=> _1F1E81F1F5_", "🇨🇷=> _1F1E81F1F7_", "🇨🇺=> _1F1E81F1FA_", "🇨🇻=> _1F1E81F1FB_", "🇨🇼=> _1F1E81F1FC_", "🇨🇽=> _1F1E81F1FD_", "🇨🇾=> _1F1E81F1FE_", "🇨🇿=> _1F1E81F1FF_", "🇩🇪=> _1F1E91F1EA_", "🇩🇬=> _1F1E91F1EC_", "🇩🇯=> _1F1E91F1EF_", "🇩🇰=> _1F1E91F1F0_", "🇩🇲=> _1F1E91F1F2_", "🇩🇴=> _1F1E91F1F4_", "🇩🇿=> _1F1E91F1FF_", "🇪🇦=> _1F1EA1F1E6_", "🇪🇨=> _1F1EA1F1E8_", "🇪🇪=> _1F1EA1F1EA_", "🇪🇬=> _1F1EA1F1EC_", "🇪🇭=> _1F1EA1F1ED_", "🇪🇷=> _1F1EA1F1F7_", "🇪🇸=> _1F1EA1F1F8_", "🇪🇹=> _1F1EA1F1F9_", "🇪🇺=> _1F1EA1F1FA_", "🇫🇮=> _1F1EB1F1EE_", "🇫🇯=> _1F1EB1F1EF_", "🇫🇰=> _1F1EB1F1F0_", "🇫🇲=> _1F1EB1F1F2_", "🇫🇴=> _1F1EB1F1F4_", "🇫🇷=> _1F1EB1F1F7_", "🇬🇦=> _1F1EC1F1E6_", "🇬🇧=> _1F1EC1F1E7_", "🇬🇩=> _1F1EC1F1E9_", "🇬🇪=> _1F1EC1F1EA_", "🇬🇫=> _1F1EC1F1EB_", "🇬🇬=> _1F1EC1F1EC_", "🇬🇭=> _1F1EC1F1ED_", "🇬🇮=> _1F1EC1F1EE_", "🇬🇱=> _1F1EC1F1F1_", "🇬🇲=> _1F1EC1F1F2_", "🇬🇳=> _1F1EC1F1F3_", "🇬🇵=> _1F1EC1F1F5_", "🇬🇶=> _1F1EC1F1F6_", "🇬🇷=> _1F1EC1F1F7_", "🇬🇸=> _1F1EC1F1F8_", "🇬🇹=> _1F1EC1F1F9_", "🇬🇺=> _1F1EC1F1FA_", "🇬🇼=> _1F1EC1F1FC_", "🇬🇾=> _1F1EC1F1FE_", "🇭🇰=> _1F1ED1F1F0_", "🇭🇲=> _1F1ED1F1F2_", "🇭🇳=> _1F1ED1F1F3_", "🇭🇷=> _1F1ED1F1F7_", "🇭🇹=> _1F1ED1F1F9_", "🇭🇺=> _1F1ED1F1FA_", "🇮🇨=> _1F1EE1F1E8_", "🇮🇩=> _1F1EE1F1E9_", "🇮🇪=> _1F1EE1F1EA_", "🇮🇱=> _1F1EE1F1F1_", "🇮🇲=> _1F1EE1F1F2_", "🇮🇳=> _1F1EE1F1F3_", "🇮🇴=> _1F1EE1F1F4_", "🇮🇶=> _1F1EE1F1F6_", "🇮🇷=> _1F1EE1F1F7_", "🇮🇸=> _1F1EE1F1F8_", "🇮🇹=> _1F1EE1F1F9_", "🇯🇪=> _1F1EF1F1EA_", "🇯🇲=> _1F1EF1F1F2_", "🇯🇴=> _1F1EF1F1F4_", "🇯🇵=> _1F1EF1F1F5_", "🇰🇪=> _1F1F01F1EA_", "🇰🇬=> _1F1F01F1EC_", "🇰🇭=> _1F1F01F1ED_", "🇰🇮=> _1F1F01F1EE_", "🇰🇲=> _1F1F01F1F2_", "🇰🇳=> _1F1F01F1F3_", "🇰🇵=> _1F1F01F1F5_", "🇰🇷=> _1F1F01F1F7_", "🇰🇼=> _1F1F01F1FC_", "🇰🇾=> _1F1F01F1FE_", "🇰🇿=> _1F1F01F1FF_", "🇱🇦=> _1F1F11F1E6_", "🇱🇧=> _1F1F11F1E7_", "🇱🇨=> _1F1F11F1E8_", "🇱🇮=> _1F1F11F1EE_", "🇱🇰=> _1F1F11F1F0_", "🇱🇷=> _1F1F11F1F7_", "🇱🇸=> _1F1F11F1F8_", "🇱🇹=> _1F1F11F1F9_", "🇱🇺=> _1F1F11F1FA_", "🇱🇻=> _1F1F11F1FB_", "🇱🇾=> _1F1F11F1FE_", "🇲🇦=> _1F1F21F1E6_", "🇲🇨=> _1F1F21F1E8_", "🇲🇩=> _1F1F21F1E9_", "🇲🇪=> _1F1F21F1EA_", "🇲🇫=> _1F1F21F1EB_", "🇲🇬=> _1F1F21F1EC_", "🇲🇭=> _1F1F21F1ED_", "🇲🇰=> _1F1F21F1F0_", "🇲🇱=> _1F1F21F1F1_", "🇲🇲=> _1F1F21F1F2_", "🇲🇳=> _1F1F21F1F3_", "🇲🇴=> _1F1F21F1F4_", "🇲🇵=> _1F1F21F1F5_", "🇲🇶=> _1F1F21F1F6_", "🇲🇷=> _1F1F21F1F7_", "🇲🇸=> _1F1F21F1F8_", "🇲🇹=> _1F1F21F1F9_", "🇲🇺=> _1F1F21F1FA_", "🇲🇻=> _1F1F21F1FB_", "🇲🇼=> _1F1F21F1FC_", "🇲🇽=> _1F1F21F1FD_", "🇲🇾=> _1F1F21F1FE_", "🇲🇿=> _1F1F21F1FF_", "🇳🇦=> _1F1F31F1E6_", "🇳🇨=> _1F1F31F1E8_", "🇳🇪=> _1F1F31F1EA_", "🇳🇫=> _1F1F31F1EB_", "🇳🇬=> _1F1F31F1EC_", "🇳🇮=> _1F1F31F1EE_", "🇳🇱=> _1F1F31F1F1_", "🇳🇴=> _1F1F31F1F4_", "🇳🇵=> _1F1F31F1F5_", "🇳🇷=> _1F1F31F1F7_", "🇳🇺=> _1F1F31F1FA_", "🇳🇿=> _1F1F31F1FF_", "🇴🇲=> _1F1F41F1F2_", "🇵🇦=> _1F1F51F1E6_", "🇵🇪=> _1F1F51F1EA_", "🇵🇫=> _1F1F51F1EB_", "🇵🇬=> _1F1F51F1EC_", "🇵🇭=> _1F1F51F1ED_", "🇵🇰=> _1F1F51F1F0_", "🇵🇱=> _1F1F51F1F1_", "🇵🇲=> _1F1F51F1F2_", "🇵🇳=> _1F1F51F1F3_", "🇵🇷=> _1F1F51F1F7_", "🇵🇸=> _1F1F51F1F8_", "🇵🇹=> _1F1F51F1F9_", "🇵🇼=> _1F1F51F1FC_", "🇵🇾=> _1F1F51F1FE_", "🇶🇦=> _1F1F61F1E6_", "🇷🇪=> _1F1F71F1EA_", "🇷🇴=> _1F1F71F1F4_", "🇷🇸=> _1F1F71F1F8_", "🇷🇺=> _1F1F71F1FA_", "🇷🇼=> _1F1F71F1FC_", "🇸🇦=> _1F1F81F1E6_", "🇸🇧=> _1F1F81F1E7_", "🇸🇨=> _1F1F81F1E8_", "🇸🇩=> _1F1F81F1E9_", "🇸🇪=> _1F1F81F1EA_", "🇸🇬=> _1F1F81F1EC_", "🇸🇭=> _1F1F81F1ED_", "🇸🇮=> _1F1F81F1EE_", "🇸🇯=> _1F1F81F1EF_", "🇸🇰=> _1F1F81F1F0_", "🇸🇱=> _1F1F81F1F1_", "🇸🇲=> _1F1F81F1F2_", "🇸🇳=> _1F1F81F1F3_", "🇸🇴=> _1F1F81F1F4_", "🇸🇷=> _1F1F81F1F7_", "🇸🇸=> _1F1F81F1F8_", "🇸🇹=> _1F1F81F1F9_", "🇸🇻=> _1F1F81F1FB_", "🇸🇽=> _1F1F81F1FD_", "🇸🇾=> _1F1F81F1FE_", "🇸🇿=> _1F1F81F1FF_", "🇹🇦=> _1F1F91F1E6_", "🇹🇨=> _1F1F91F1E8_", "🇹🇩=> _1F1F91F1E9_", "🇹🇫=> _1F1F91F1EB_", "🇹🇬=> _1F1F91F1EC_", "🇹🇭=> _1F1F91F1ED_", "🇹🇯=> _1F1F91F1EF_", "🇹🇰=> _1F1F91F1F0_", "🇹🇱=> _1F1F91F1F1_", "🇹🇲=> _1F1F91F1F2_", "🇹🇳=> _1F1F91F1F3_", "🇹🇴=> _1F1F91F1F4_", "🇹🇷=> _1F1F91F1F7_", "🇹🇹=> _1F1F91F1F9_", "🇹🇻=> _1F1F91F1FB_", "🇹🇼=> _1F1F91F1FC_", "🇹🇿=> _1F1F91F1FF_", "🇺🇦=> _1F1FA1F1E6_", "🇺🇬=> _1F1FA1F1EC_", "🇺🇲=> _1F1FA1F1F2_", "🇺🇳=> _1F1FA1F1F3_", "🇺🇸=> _1F1FA1F1F8_", "🇺🇾=> _1F1FA1F1FE_", "🇺🇿=> _1F1FA1F1FF_", "🇻🇦=> _1F1FB1F1E6_", "🇻🇨=> _1F1FB1F1E8_", "🇻🇪=> _1F1FB1F1EA_", "🇻🇬=> _1F1FB1F1EC_", "🇻🇮=> _1F1FB1F1EE_", "🇻🇳=> _1F1FB1F1F3_", "🇻🇺=> _1F1FB1F1FA_", "🇼🇫=> _1F1FC1F1EB_", "🇼🇸=> _1F1FC1F1F8_", "🇽🇰=> _1F1FD1F1F0_", "🇾🇪=> _1F1FE1F1EA_", "🇾🇹=> _1F1FE1F1F9_", "🇿🇦=> _1F1FF1F1E6_", "🇿🇲=> _1F1FF1F1F2_", "🇿🇼=> _1F1FF1F1FC_", "\\u00A0 => _space1_", "\\u3000 => _space3_", "\\\\n => _newline_", "\\n => _newline_"]}}, "filter": {"han_bigrams_filter": {"type": "cjk_bigram", "output_unigrams": false}, "remove_empty": {"type": "length", "min": 1}, "truncate_filter": {"type": "truncate", "length": 8191}}}, "number_of_shards": 1, "number_of_replicas": 0, "index.codec": "best_compression"}
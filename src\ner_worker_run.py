import argparse
import datetime
import json
import os
import sys

from dotenv import load_dotenv

from helper import initLogger
from repository.elasticsearch_repo import ElasticsearchRepository
from tools.utils import send_line_notify


def parse_input_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-s", "--start_date", default=[""], nargs=1, type=str, help="請輸入起始日期"
    )
    parser.add_argument(
        "-e", "--end_date", default=[""], nargs=1, type=str, help="請輸入結束日期"
    )
    args = parser.parse_args()
    logger.info(f"args: {args}")

    dct_input_args = {k: v[0] for k, v in vars(args).items() if v[0]}

    return dct_input_args


def get_env_vars():
    str_start_date = os.getenv("DATA_API_START_DATE", "")
    str_end_date = os.getenv("DATA_API_END_DATE", "")
    lst_fetch_date = json.loads(os.getenv("DATA_API_DATE", "[]"))

    return {
        "start_date": str_start_date,
        "end_date": str_end_date,
        "lst_fetch_date": lst_fetch_date,
    }


if __name__ == "__main__":
    try:
        # ================== initialize logger ====================
        logDirPath = "logs"
        logger = initLogger(logDirPath, "DEBUG")
        logger.info("logger being initialized successfully")

        # ================== Parse argument ====================
        load_dotenv(".env")

        dct_vars = get_env_vars()
        dct_input_args = parse_input_args()
        dct_vars.update(dct_input_args)

        str_start_date = dct_vars["start_date"]
        str_end_date = dct_vars["end_date"]

        str_start_date = (
            str_start_date
            if str_start_date
            else datetime.datetime.today().strftime("%Y/%m/%d")
        )
        str_end_date = (
            str_end_date
            if str_end_date
            else datetime.datetime.today().strftime("%Y/%m/%d")
        )

        logger.info(f"str_start_date: {str_start_date}, str_end_date: {str_end_date}")

        if str_start_date and str_end_date:
            date_format = "%Y/%m/%d"
            try:
                t_start_date = datetime.datetime.strptime(str_start_date, date_format)
                t_end_date = datetime.datetime.strptime(str_end_date, date_format)
                if t_end_date < t_start_date:
                    raise ValueError("End time must be greater than start time")
            except ValueError as e:
                logger.error(f"{e}")
                sys.exit(1)
        elif not dct_vars["lst_fetch_date"]:
            logger.error(
                "Please set 'DATA_API_START_DATE' and 'DATA_API_END_DATE' or 'DATA_API_DATE'"
            )
            sys.exit(1)

        # ================== Elasticsearch Operation ====================
        es_repo = ElasticsearchRepository()

        # Prepare the query that searches for documents that haven't been done ner indexing
        lst_pos_fields = ["petition_content_pos", "petition_subject_pos"]
        n_es_return_docs_size = 10
        lst_es_source = ["case_id", "petition_content", "petition_subject"]
        str_es_scroll_time = "5m"  # length of time to keep the scroll window open
        t_delta = datetime.timedelta(days=1)
        dct_total_error_doc_id = {}
        n_total_docs = 0
        n_total_docs_success = 0
        n_total_docs_error = 0
        if str_start_date and str_end_date:
            while t_start_date <= t_end_date:
                str_start_date = t_start_date.strftime("%Y/%m/%d")
                str_end_date = t_start_date.strftime("%Y/%m/%d")

                logger.info(f"[{str_start_date}]~[{str_end_date}] Start processing...")
                dct_update_ner_result = es_repo.update_ner_info_to_es(
                    **{
                        "str_start_date": str_start_date,
                        "str_end_date": str_end_date,
                        "lst_pos_fields": lst_pos_fields,
                        "n_es_return_docs_size": n_es_return_docs_size,
                        "lst_es_source": lst_es_source,
                        "str_es_scroll_time": str_es_scroll_time,
                    }
                )

                for str_date, lst_error_doc_id in dct_update_ner_result[
                    "dct_total_error_doc_id"
                ].items():
                    if str_date in dct_total_error_doc_id:
                        dct_total_error_doc_id[str_date].extend(lst_error_doc_id)
                    else:
                        dct_total_error_doc_id[str_date] = lst_error_doc_id

                str_start_date = dct_update_ner_result["str_start_date"]
                str_end_date = dct_update_ner_result["str_end_date"]
                n_docs_total = dct_update_ner_result["n_docs_total"]
                n_docs_success = dct_update_ner_result["n_docs_success"]
                n_docs_error = dct_update_ner_result["n_docs_error"]

                n_total_docs += n_docs_total
                n_total_docs_success += n_docs_success
                n_total_docs_error += n_docs_error

                # f_success_rate = (
                #     (n_docs_success / n_docs_total) * 100 if n_docs_total != 0 else 0
                # )
                # f_error_rate = (
                #     (n_docs_error / n_docs_total) * 100 if n_docs_total != 0 else 0
                # )

                # logger.info(
                #     f"[{str_start_date}]~[{str_end_date}] Total:[{n_docs_total}] / Success:[{n_docs_success}] / Error:[{n_docs_error}] / Success_rate:[{f_success_rate:.2f}%] / Error_rate:[{f_error_rate:.2f}%]"
                # )
                logger.info(
                    f"[{str_start_date}]~[{str_end_date}] Total:[{n_docs_total}] / Success:[{n_docs_success}] / Error:[{n_docs_error}]"
                )
                logger.info(
                    f"[{str_start_date}]~[{str_end_date}] Total_error_doc_ids: {dct_update_ner_result['dct_total_error_doc_id']}"
                )

                t_start_date += t_delta

        if dct_vars["lst_fetch_date"]:
            logger.info(f"lst_fetch_date: {dct_vars['lst_fetch_date']}")
            for str_fetch_date in dct_vars["lst_fetch_date"]:
                logger.info(f"[{str_fetch_date}] Start processing...")
                dct_update_ner_result = es_repo.update_ner_info_to_es(
                    **{
                        "str_start_date": str_fetch_date,
                        "str_end_date": str_fetch_date,
                        "lst_pos_fields": lst_pos_fields,
                        "n_es_return_docs_size": n_es_return_docs_size,
                        "lst_es_source": lst_es_source,
                        "str_es_scroll_time": str_es_scroll_time,
                    }
                )

                for str_date, lst_error_doc_id in dct_update_ner_result[
                    "dct_total_error_doc_id"
                ].items():
                    if str_date in dct_total_error_doc_id:
                        dct_total_error_doc_id[str_date].extend(lst_error_doc_id)
                    else:
                        dct_total_error_doc_id[str_date] = lst_error_doc_id

                # str_start_date = dct_update_ner_result["str_start_date"]
                # str_end_date = dct_update_ner_result["str_end_date"]
                n_docs_total = dct_update_ner_result["n_docs_total"]
                n_docs_success = dct_update_ner_result["n_docs_success"]
                n_docs_error = dct_update_ner_result["n_docs_error"]

                n_total_docs += n_docs_total
                n_total_docs_success += n_docs_success
                n_total_docs_error += n_docs_error

                # f_success_rate = (
                #     (n_docs_success / n_docs_total) * 100 if n_docs_total != 0 else 0
                # )
                # f_error_rate = (
                #     (n_docs_error / n_docs_total) * 100 if n_docs_total != 0 else 0
                # )

                # logger.info(
                #     f"[{str_fetch_date}] Total:[{n_docs_total}] / Success:[{n_docs_success}] / Error:[{n_docs_error}] / Success_rate:[{f_success_rate:.2f}%] / Error_rate:[{f_error_rate:.2f}%]"
                # )
                logger.info(
                    f"[{str_fetch_date}] Total:[{n_docs_total}] / Success:[{n_docs_success}] / Error:[{n_docs_error}]"
                )
                logger.info(
                    f"[{str_fetch_date}] Total_error_doc_ids: {dct_update_ner_result['dct_total_error_doc_id']}"
                )

        f_success_rate = (
            (n_total_docs_success / n_total_docs) * 100 if n_total_docs != 0 else 0
        )
        f_error_rate = (
            (n_total_docs_error / n_total_docs) * 100 if n_total_docs != 0 else 0
        )

        logger.info(
            f"Total:[{n_total_docs}] / Success:[{n_total_docs_success}] / Error:[{n_total_docs_error}] / Success_rate:[{f_success_rate:.2f}%] / Error_rate:[{f_error_rate:.2f}%]"
        )
        logger.info(f"Total_error_doc_ids: {dct_total_error_doc_id}")

        if dct_total_error_doc_id:
            tz = datetime.timezone(datetime.timedelta(hours=+8))
            t_current_datetime = datetime.datetime.now(tz)
            str_current_datetime = t_current_datetime.strftime("%Y-%m-%d_%H:%M:%S")
            with open(
                os.path.join(
                    "/", "home", "tycg", f"error_doc_id_{str_current_datetime}.json"
                ),
                "w",
                encoding="utf8",
            ) as outfile:
                json.dump(
                    {
                        "lst_error_date": es_repo.set_error_date,
                        "dct_total_error_doc_id": dct_total_error_doc_id,
                    },
                    outfile,
                    ensure_ascii=False,
                    indent=4,
                )
    except Exception as e:
        logger.error(f"An unexpected error occurred: {repr(e)}")
        str_line_notify_message = f"""
    NER Analyzer ☁️
    start_date: [{str_start_date}]
    end_date: [{str_end_date}]
    fetch_date: {dct_vars["lst_fetch_date"]}
    --------------------------
    Exception: {repr(e)}
    """
        send_line_notify(
            message=str_line_notify_message,
            is_success=False,
            environment=os.environ["TYCG_BE_RUNTIME_ENV"],
        )
        sys.exit(1)

    else:
        is_success = (
            False
            if dct_total_error_doc_id
            or es_repo.set_error_date
            or n_total_docs_error > 0
            else True
        )
        str_line_notify_message = f"""
    NER Analyzer ☁️
    start_date: [{str_start_date}]
    end_date: [{str_end_date}]
    fetch_date: {dct_vars["lst_fetch_date"]}
    --------------------------
    Total: [{n_docs_total}]
    Success: [{n_total_docs_success}]
    Error: [{n_total_docs_error}]
    Success_rate: [{f_success_rate:.2f}%]
    Error_rate: [{f_error_rate:.2f}%]
    --------------------------
    lst_error_date: {es_repo.set_error_date},
    dct_total_error_doc_id: {dct_total_error_doc_id},
    """
        send_line_notify(
            message=str_line_notify_message,
            is_success=is_success,
            environment=os.environ["TYCG_BE_RUNTIME_ENV"],
        )

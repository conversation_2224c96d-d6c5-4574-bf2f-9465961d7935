import json
import logging
import os

import httpx

import settings

logger = logging.getLogger(settings.logger_name)


def send_line_notify(message, is_success, environment):
    webhook_url = os.environ["GOOGLE_CHAT_WEBHOOK_URL"]
    headers = {"Content-Type": "application/json; charset=UTF-8"}

    success_icon = "✅✅✅" if is_success else "❌❌❌"
    if environment == "PROD":
        environment_icon = "PROD🚀"
    elif environment == "DEV":
        environment_icon = "DEV🛠️"
    else:
        environment_icon = "LOCAL💻"
    data = {
        "text": f"""
{success_icon}
{environment_icon}
{message}
    """
    }
    response = httpx.post(
        webhook_url, headers=headers, data=json.dumps(data), timeout=10
    )
    logger.info(response.json())

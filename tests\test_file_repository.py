import json
from pathlib import Path

import pytest
from pydantic import ValidationError

from models import PetitionInput
from repository.file_repository import (
    get_ground_truth,
    get_petition_data,
    write_petition_data,
)


def test_get_petition_data_success(tmp_path: Path):
    """
    Test that petitions are successfully read and parsed from a valid JSON file.
    """
    # Arrange
    file_path = tmp_path / "test_petitions.json"
    mock_data = [
        {
            "case_id": "C01",
            "record_channel": "1999",
            "record_time": "2025/06/01 16:55:10",
            "main_category": [""],
            "sub_category": ["Category 1"],
            "petition_subject": "Subject 1",
            "petition_content": "Content 1",
        },
        {
            "case_id": "C02",
            "record_channel": "1999",
            "record_time": "2025/06/01 17:54:54",
            "main_category": ["外撥"],
            "sub_category": ["Category 2"],
            "petition_subject": "Subject 2",
            "petition_content": "Content 2",
        },
    ]
    file_path.write_text(json.dumps(mock_data, ensure_ascii=False))

    # Act
    petitions = get_petition_data(file_path)

    # Assert
    assert len(petitions) == 2
    assert isinstance(petitions[0], PetitionInput)
    assert petitions[0].petition_content == "Content 1"
    assert petitions[1].sub_category == ["Category 2"]


def test_get_petition_data_file_not_found():
    """
    Test that a FileNotFoundError is raised when the input file does not exist.
    """
    # Arrange
    non_existent_path = Path("non_existent_file.json")

    # Act & Assert
    with pytest.raises(FileNotFoundError):
        get_petition_data(non_existent_path)


def test_get_petition_data_invalid_json(tmp_path: Path):
    """
    Test that a JSONDecodeError is raised for a malformed JSON file.
    """
    # Arrange
    file_path = tmp_path / "invalid.json"
    file_path.write_text("this is not json")

    # Act & Assert
    with pytest.raises(json.JSONDecodeError):
        get_petition_data(file_path)


def test_get_petition_data_validation_error(tmp_path: Path):
    """
    Test that a Pydantic ValidationError is raised if the data
    does not match the PetitionInput schema.
    """
    # Arrange
    file_path = tmp_path / "invalid_schema.json"
    # `sub_category` is not a list, which is invalid.
    mock_data = [
        {
            "case_id": "C01",
            "record_channel": "1999",
            "record_time": "2025/06/01 16:55:10",
            "main_category": [""],
            "sub_category": "not a list",
            "petition_subject": "Subject 1",
            "petition_content": "Content 1",
        }
    ]
    file_path.write_text(json.dumps(mock_data))

    # Act & Assert
    with pytest.raises(ValidationError):
        get_petition_data(file_path)


@pytest.mark.parametrize(
    "input_categories, expected_output",
    [
        (["Some Category", "Another Category"], ["Some Category", "Another Category"]),
        (["市政諮詢", "製造噪音"], ["製造噪音"]),
        (["市政信箱"], []),
        (["轉接電話", "Some Category"], ["Some Category"]),
        ([], []),
    ],
)
def test_get_ground_truth(input_categories, expected_output):
    """
    Test that the ground truth categories are correctly filtered,
    removing any ignored values.
    """
    # Act
    result = get_ground_truth(input_categories)

    # Assert
    assert result == expected_output


from models import PetitionOutput


def test_write_petition_data_success(tmp_path: Path):
    """
    Test that a list of PetitionOutput models is correctly written to a JSON file.
    """
    # Arrange
    file_path = tmp_path / "output.json"
    mock_petitions = [
        PetitionOutput(
            original_data={"content": "abc"},
            new_category="New Cat 1",
            ground_truth_category=["GT Cat 1"],
            is_accurate=True,
        ),
        PetitionOutput(
            original_data={"content": "def"},
            new_category="New Cat 2",
            ground_truth_category=[],
            is_accurate=None,
        ),
    ]

    # Act
    write_petition_data(file_path, mock_petitions)

    # Assert
    assert file_path.exists()
    with open(file_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    assert len(data) == 2
    assert data[0]["new_category"] == "New Cat 1"
    assert data[1]["is_accurate"] is None
    assert data[1]["ground_truth_category"] == []

from repository.openai_repository import (
    format_prompt,
)


def test_format_prompt_replaces_case_content():
    """
    Test that the case_content placeholder in the prompt template
    is correctly replaced with the provided petition content.
    """
    # Arrange
    petition_content = "This is a test petition about a broken streetlight."
    expected_substring = "This is a test petition about a broken streetlight."

    # Act
    formatted_prompt = format_prompt(petition_content)

    # Assert
    assert expected_substring in formatted_prompt
    # Ensure the original placeholder is gone
    assert "{case_content}" not in formatted_prompt
    # Ensure the overall template structure is intact
    assert "核心任務" in formatted_prompt


from unittest.mock import MagicMock

import openai

from models import AICategoryResponse
from repository.openai_repository import get_ai_category_response


def test_get_ai_category_response_success(monkeypatch):
    """
    Test a successful API call to get a category response.
    It should call the OpenAI client with the correct parameters.
    """
    # Arrange
    mock_client = MagicMock(spec=openai.OpenAI)
    # The mock response should be an instance of the Pydantic model
    mock_response = AICategoryResponse(new_category="路面不平整或掏空破洞")
    mock_client.chat.completions.create.return_value = mock_response

    # Monkeypatch the from_provider method to return our mock client
    monkeypatch.setattr("instructor.from_provider", lambda *args, **kwargs: mock_client)
    # Also mock the settings loader to avoid needing a real API key
    monkeypatch.setattr(
        "repository.openai_repository.Settings",
        lambda: MagicMock(OPENAI_API_KEY="test_key"),
    )

    petition_content = "My street has a huge pothole."

    # Act
    result = get_ai_category_response(petition_content)

    # Assert
    assert isinstance(result, AICategoryResponse)
    assert result.new_category == "路面不平整或掏空破洞"

    # Verify that the create method was called
    mock_client.chat.completions.create.assert_called_once()
    call_args, call_kwargs = mock_client.chat.completions.create.call_args

    # Check the arguments passed to the create method
    assert call_kwargs["model"] == "gpt-4o"
    assert "messages" in call_kwargs
    assert call_kwargs["response_model"] == AICategoryResponse


def test_get_ai_category_response_api_error_retries(monkeypatch):
    """
    Test that the function retries on openai.APIError and eventually succeeds.
    """
    # Arrange
    mock_client = MagicMock(spec=openai.OpenAI)
    # The mock response should be an instance of the Pydantic model
    mock_response = AICategoryResponse(new_category="路面不平整或掏空破洞")

    # Simulate one APIError followed by a successful response
    mock_client.chat.completions.create.side_effect = [
        openai.APIError("Service unavailable", body=None, request=MagicMock()),
        mock_response,
    ]

    # Monkeypatch the from_provider method to return our mock client
    monkeypatch.setattr("instructor.from_provider", lambda *args, **kwargs: mock_client)
    # Also mock the settings loader to avoid needing a real API key
    monkeypatch.setattr(
        "repository.openai_repository.Settings",
        lambda: MagicMock(OPENAI_API_KEY="test_key"),
    )

    petition_content = "My street has a huge pothole."

    # Act
    result = get_ai_category_response(petition_content)

    # Assert
    assert result is not None
    assert isinstance(result, AICategoryResponse)
    assert result.new_category == "路面不平整或掏空破洞"
    # Verify that create was called twice (1 failure, 1 success)
    assert mock_client.chat.completions.create.call_count == 2


def test_get_ai_category_response_all_retries_fail(monkeypatch):
    """
    Test that the function returns None if all retry attempts fail.
    """
    # Arrange
    mock_client = MagicMock(spec=openai.OpenAI)
    # Simulate an APIError on every call
    mock_client.chat.completions.create.side_effect = openai.APIError(
        "Service unavailable", body=None, request=MagicMock()
    )

    # Monkeypatch the from_provider method to return our mock client
    monkeypatch.setattr("instructor.from_provider", lambda *args, **kwargs: mock_client)
    # Also mock the settings loader to avoid needing a real API key
    monkeypatch.setattr(
        "repository.openai_repository.Settings",
        lambda: MagicMock(OPENAI_API_KEY="test_key"),
    )

    petition_content = "My street has a huge pothole."

    # Act
    result = get_ai_category_response(petition_content)

    # Assert
    assert result is None
    # Verify that create was called 3 times (the number of attempts)
    assert mock_client.chat.completions.create.call_count == 3

[tool.poetry]
name = "ner-worker"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.10"
elasticsearch = "^8.13.1"
python-dotenv = "^1.0.1"
openai = "^1.30.5"
opencc-python-reimplemented = "^0.1.7"
numpy = "<2"
pydantic = "^2.7.1"
pydantic-settings = "^2.3.4"
instructor = "^1.9.2"
tenacity = "^9.1.2"


[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
pytest-mock = "^3.14.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"


[tool.pytest.ini_options]
pythonpath = [
  "src"
]

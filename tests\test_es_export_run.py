import sys
from unittest.mock import MagicMock

# Mock the missing ner_model module to prevent ImportError during test collection
sys.modules["ner_model"] = MagicMock()
sys.modules["ner_model.module"] = MagicMock()
sys.modules["ner_model.module.ckiptransformer"] = MagicMock()

import argparse
from datetime import datetime

import pytest

from es_export_run import valid_date


def test_valid_date_success():
    """
    Tests that valid_date correctly parses a valid date string.
    """
    date_string = "2024-01-01"
    expected_date = datetime(2024, 1, 1)
    assert valid_date(date_string) == expected_date


def test_valid_date_failure():
    """
    Tests that valid_date raises ArgumentTypeError for an invalid date string.
    """
    date_string = "invalid-date"
    with pytest.raises(
        argparse.ArgumentTypeError, match="Not a valid date: 'invalid-date'."
    ):
        valid_date(date_string)


def test_main_success(mocker):
    """
    Tests the main function for a successful data export.
    """
    # Mock command-line arguments
    args = argparse.Namespace(
        start_date=datetime(2024, 1, 1), end_date=datetime(2024, 1, 31)
    )
    mocker.patch("argparse.ArgumentParser.parse_args", return_value=args)

    # Mock ElasticsearchRepository
    mock_repo_instance = mocker.MagicMock()
    mock_repo_instance.export_data_by_date_range.return_value = [
        {"case_id": "1", "petition_content": "Test content 1"},
        {"case_id": "2", "petition_content": "Test content 2"},
    ]
    mocker.patch(
        "es_export_run.ElasticsearchRepository", return_value=mock_repo_instance
    )

    # Mock file writing
    mock_open = mocker.patch("builtins.open", mocker.mock_open())
    mock_json_dump = mocker.patch("json.dump")

    # Mock sys.exit and print
    mocker.patch("sys.exit")
    mock_print = mocker.patch("builtins.print")

    # Run the main function
    from es_export_run import main

    main()

    # Assertions
    mock_repo_instance.export_data_by_date_range.assert_called_once_with(
        "2024-01-01",
        "2024-01-31",
        [
            "case_id",
            "case_source",
            "record_channel",
            "record_time",
            "main_category",
            "sub_category",
            "petition_content",
            "petition_subject",
        ],
    )
    expected_filename = "export_2024-01-01_to_2024-01-31.json"
    mock_open.assert_called_once_with(expected_filename, "w", encoding="utf-8")
    mock_json_dump.assert_called_once()
    assert mock_print.call_count > 0
    mock_print.assert_any_call(
        f"Successfully exported 2 records to {expected_filename}"
    )


def test_main_start_date_after_end_date(mocker):
    """
    Tests that the script exits with an error if the start date is after the end date.
    """
    args = argparse.Namespace(
        start_date=datetime(2024, 2, 1), end_date=datetime(2024, 1, 31)
    )
    mocker.patch("argparse.ArgumentParser.parse_args", return_value=args)
    mock_print = mocker.patch("builtins.print")

    from es_export_run import main

    with pytest.raises(SystemExit) as e:
        main()

    assert e.type == SystemExit
    assert e.value.code == 1
    mock_print.assert_called_once_with(
        "Error: Start date cannot be after end date.", file=sys.stderr
    )


def test_main_no_records_found(mocker):
    """
    Tests the main function when no records are found.
    """
    args = argparse.Namespace(
        start_date=datetime(2024, 1, 1), end_date=datetime(2024, 1, 31)
    )
    mocker.patch("argparse.ArgumentParser.parse_args", return_value=args)

    mock_repo_instance = mocker.MagicMock()
    mock_repo_instance.export_data_by_date_range.return_value = []
    mocker.patch(
        "es_export_run.ElasticsearchRepository", return_value=mock_repo_instance
    )

    mock_open = mocker.patch("builtins.open", mocker.mock_open())
    mock_exit = mocker.patch("sys.exit")
    mock_print = mocker.patch("builtins.print")

    from es_export_run import main

    main()

    mock_print.assert_any_call("No records found for the given date range.")
    mock_open.assert_not_called()
    mock_exit.assert_called_once_with(0)


def test_main_exception_handling(mocker):
    """
    Tests the main function's exception handling.
    """
    args = argparse.Namespace(
        start_date=datetime(2024, 1, 1), end_date=datetime(2024, 1, 31)
    )
    mocker.patch("argparse.ArgumentParser.parse_args", return_value=args)

    mocker.patch("es_export_run.ElasticsearchRepository").side_effect = Exception(
        "Test DB Error"
    )

    mock_exit = mocker.patch("sys.exit")
    mock_print = mocker.patch("builtins.print")

    from es_export_run import main

    main()

    mock_print.assert_called_once_with(
        "An error occurred: Test DB Error", file=sys.stderr
    )
    mock_exit.assert_called_once_with(1)

import json

if __name__ == "__main__":
    # with open("data/categories_2024_raw.json", encoding="utf-8") as f:
    #     data = json.load(f)

    # """
    # "aggregations": {
    # 	"main_categories": {
    # 		"doc_count_error_upper_bound": 0,
    # 		"sum_other_doc_count": 0,
    # 		"buckets": [
    # 			{
    # 				"key": "交通號誌、標誌、標線及大眾運輸",
    # 				"doc_count": 12965,
    # 				"sub_categories": {
    # 					"doc_count_error_upper_bound": 0,
    # 					"sum_other_doc_count": 0,
    # 					"buckets": [
    # 						{
    # 							"key": "公車問題及站牌、候車亭設施管理",
    # 							"doc_count": 4176
    # 						},
    # 						{
    # 							"key": "交通標誌、標線、反射鏡設置或移除",
    # 							"doc_count": 4155
    # 						}
    #                     ]
    #                     }
    #                 }
    #             ]
    #         }
    #     }
    # }
    # """
    # export_json = {}
    # for main_category in data["aggregations"]["main_categories"]["buckets"]:
    #     export_json[main_category["key"]] = [
    #         sub_category["key"]
    #         for sub_category in main_category["sub_categories"]["buckets"]
    #     ]

    # with open("data/categories_2024.json", "w", encoding="utf-8") as f:
    #     json.dump(export_json, f, ensure_ascii=False, indent=2)

    # ---------------------------------------

    with open("data/categories_2024.json", encoding="utf-8") as f:
        data = json.load(f)

    all_main_sub_categories = []
    for main_cat, sublist in data.items():
        for sub_cat in sublist:
            all_main_sub_categories.append(f"{main_cat}_{sub_cat}")

    with open("data/main_sub_categories_2024.json", "w", encoding="utf-8") as f:
        json.dump(all_main_sub_categories, f, ensure_ascii=False, indent=2)

    # ---------------------------------------

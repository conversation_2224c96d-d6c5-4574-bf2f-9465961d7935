import concurrent.futures
import datetime
import json
import logging
import os
from typing import List, Union

from elasticsearch import helpers
from ner_model.module.ckiptransformer import CkipTransformer

import settings
from repository.connection_handler import ElasticsearchConnector
from repository.utils.stopwords import set_stop_words


class ElasticsearchRepository:
    def __init__(self):
        logger_name = settings.logger_name
        self.logger = logging.getLogger(logger_name)
        self.es_connector = ElasticsearchConnector()
        self.es = self.es_connector.get_es()

        # Get the current date and year
        tz = datetime.timezone(datetime.timedelta(hours=+8))
        t_current_date = datetime.datetime.now(tz)
        current_year = t_current_date.year

        self.petition_case_index_base_name = "petition-case"
        self.petition_case_index_name = f"{self.petition_case_index_base_name}-{current_year}"  # 使用當前年分作為petition_case_index_name
        self.petition_case_alias_name = f"{self.petition_case_index_name}-alias"
        self.petition_case_latest_alias_name = (
            f"{self.petition_case_index_base_name}-latest-alias"
        )

        self.set_error_date = set()
        self.set_index_check_cache = set()

    def bulk_actions(
        self, lst_actions: List[dict], op_type: str = "index", str_record_date: str = ""
    ):
        lst_error_doc_id = []
        n_docs_success = 0
        n_docs_total = len(lst_actions)
        try:
            n_total_actions = len(lst_actions)
            n_chunk = 5000
            n_len_actions = 0

            for n_start in range(0, n_total_actions, n_chunk):
                lst_sub_actions = lst_actions[n_start : n_start + n_chunk]
                n_len_actions = len(lst_sub_actions)

                n_docs_success += n_len_actions
                helpers.bulk(self.es, lst_actions, stats_only=True)
                self.logger.info(
                    f"[{str_record_date}] Bulk [{op_type}] [{n_len_actions}] docs successfully."
                )

        except helpers.BulkIndexError as e:
            n_error_doc_id = len(e.errors)
            # e.errors contains the errors that occurred during the bulk operation
            for error in e.errors:
                # Each error is a tuple where the first element is the action that failed and the second element is the error information
                self.logger.error(
                    f"[{str_record_date}] Error indexing document: {error}\n"
                )
                doc_id = error[op_type]["_id"]
                lst_error_doc_id.append(doc_id)
                # self.logger.error(f"Error indexing document with ID {doc_id}")

            n_docs_success -= n_error_doc_id
            self.logger.warn(f"[{str_record_date}] [{n_error_doc_id}] docs failed")
            self.set_error_date.add(str_record_date)

        except Exception as e:
            self.logger.error(f"[{str_record_date}] Unexpected error: {repr(e)}")
            self.logger.warn(
                f"[{str_record_date}] [{n_docs_total - n_docs_success}] docs failed"
            )
            self.set_error_date.add(str_record_date)
        else:
            pass
            # self.logger.info(
            #     f"[{str_record_date}] Bulk [{op_type}] done. [{n_total_actions}] docs in total are processed."
            # )
        finally:
            n_docs_error = len(lst_error_doc_id)

            # f_success_rate = (
            #     (n_docs_success / n_docs_total) * 100 if n_docs_total != 0 else 0
            # )
            # f_error_rate = (
            #     (n_docs_error / n_docs_total) * 100 if n_docs_total != 0 else 0
            # )

            # self.logger.info(
            #     f"[{str_record_date}] total:[{n_docs_total}] / success:[{n_docs_success}] / error:[{n_docs_error}] / success_rate:[{f_success_rate:.2f}%] / error_rate:[{f_error_rate:.2f}%]"
            # )

            self.logger.info(
                f"[{str_record_date}] Bulk total:[{n_docs_total}] / success:[{n_docs_success}] / error:[{n_docs_error}]"
            )

            self.logger.info(
                f"[{str_record_date}] Bulk lst_error_doc_id: {lst_error_doc_id}"
            )
            return {
                "str_record_date": str_record_date,
                "n_docs_success": n_docs_success,
                "n_docs_error": n_docs_error,
                "n_docs_total": n_docs_total,
                "lst_error_doc_id": lst_error_doc_id,
            }

    def export_data_by_date_range(self, start_date: str, end_date: str, fields: list):
        """
        Export data from Elasticsearch within a given date range using the scroll API.
        """
        query = {
            "_source": fields,
            "query": {
                "range": {
                    "record_time": {
                        "gte": start_date,
                        "lte": end_date,
                        "format": "yyyy-MM-dd",
                    }
                }
            },
        }

        lst_alias_name = self.calc_alias_name(
            start_date.replace("-", "/"), end_date.replace("-", "/")
        )

        try:
            # Initial search request
            resp = self.es.search(
                index=lst_alias_name,
                body=query,
                scroll="2m",  # Keep the scroll window open for 2 minutes
                size=1000,  # Number of documents to return per scroll
            )

            # Keep track of the scroll ID
            scroll_id = resp.get("_scroll_id")

            # Start scrolling
            while scroll_id and len(resp["hits"]["hits"]):
                # Yield each hit
                for hit in resp["hits"]["hits"]:
                    yield hit["_source"]

                # Get the next batch of results
                resp = self.es.scroll(scroll_id=scroll_id, scroll="2m")
                scroll_id = resp.get("_scroll_id")

            # Clear the scroll
            if scroll_id:
                self.es.clear_scroll(scroll_id=scroll_id)

        except Exception as e:
            self.logger.error(f"Error exporting data from Elasticsearch: {e}")
            raise

    def export_data_by_category(
        self,
        start_date: str,
        end_date: str,
        record_channel: str,
        main_category: str,
        sub_category: str,
        fields: list,
        max_size: int = 50,
    ):
        """
        Export data from Elasticsearch filtered by date range, record_channel, main_category, and sub_category.

        Args:
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            record_channel: The record channel to filter by (e.g., "市政信箱")
            main_category: The main category to filter by
            sub_category: The sub category to filter by
            fields: List of fields to include in the export
            max_size: Maximum number of documents to return (default: 50)

        Yields:
            dict: Document source data
        """
        query = {
            "_source": fields,
            "query": {
                "bool": {
                    "filter": [
                        {
                            "range": {
                                "record_time": {
                                    "gte": start_date,
                                    "lte": end_date,
                                    "format": "yyyy-MM-dd",
                                }
                            }
                        },
                        {"term": {"record_channel": record_channel}},
                        {"term": {"main_category": main_category}},
                        {"term": {"sub_category": sub_category}},
                    ]
                }
            },
        }

        lst_alias_name = self.calc_alias_name(
            start_date.replace("-", "/"), end_date.replace("-", "/")
        )

        try:
            # Initial search request with limited size
            resp = self.es.search(
                index=lst_alias_name,
                body=query,
                scroll="2m",  # Keep the scroll window open for 2 minutes
                size=min(max_size, 1000),  # Use the smaller of max_size or 1000
            )

            # Keep track of the scroll ID and document count
            scroll_id = resp.get("_scroll_id")
            doc_count = 0

            # Start scrolling
            while scroll_id and len(resp["hits"]["hits"]) and doc_count < max_size:
                # Yield each hit up to the max_size limit
                for hit in resp["hits"]["hits"]:
                    if doc_count >= max_size:
                        break
                    yield hit["_source"]
                    doc_count += 1

                # If we've reached the limit, break out of the loop
                if doc_count >= max_size:
                    break

                # Get the next batch of results
                resp = self.es.scroll(scroll_id=scroll_id, scroll="2m")
                scroll_id = resp.get("_scroll_id")

            # Clear the scroll
            if scroll_id:
                self.es.clear_scroll(scroll_id=scroll_id)

        except Exception as e:
            self.logger.error(f"Error exporting category data from Elasticsearch: {e}")
            raise

    def search_to_be_ner_doc(
        self,
        n_es_return_docs_size: int,
        lst_es_source: list,
        str_start_date: str,
        str_end_date: str,
        str_es_scroll_time: str,
        lst_pos_fields: list,
    ):
        lst_search_exist_query = []
        for str_pos_field in lst_pos_fields:
            for str_ner in settings.lst_self_defined_ner:
                lst_search_exist_query.append(
                    {"exists": {"field": f"{str_pos_field}.{str_ner}"}}
                )

        dct_query = {
            "size": n_es_return_docs_size,
            "_source": lst_es_source,
            "query": {
                "bool": {
                    "filter": [
                        {
                            "range": {
                                "record_time": {
                                    "gte": str_start_date,
                                    "lte": str_end_date,
                                    "format": "yyyy/MM/dd HH:mm:ss||yyyy/MM/dd||epoch_millis",
                                }
                            }
                        },
                        {"bool": {"must_not": lst_search_exist_query}},
                    ]
                }
            },
            "sort": [{"record_time": {"order": "asc"}}],
        }

        lst_alias_name = self.calc_alias_name(str_start_date, str_end_date)

        # Search for es documents that haven't been done ner indexing
        es_search_result = self.search(
            dct_query=dct_query, alias_name=lst_alias_name, scroll=str_es_scroll_time
        )
        return es_search_result

    def process_ner_field(self, lst_es_hits_result: list):
        def func(ner_model: CkipTransformer, str_field_to_ner):
            self.logger.debug(f"Start NER processing [{str_field_to_ner}]...")

            # Process current batch of hits
            # lst_es_hits_result = es_search_result["hits"]["hits"]
            dct_organize_result = {"lst_ner_inputs": [], "lst_id": []}
            for dct_es_hits_result in lst_es_hits_result:
                dct_es_source = dct_es_hits_result["_source"]
                dct_organize_result["lst_ner_inputs"].append(
                    dct_es_source[str_field_to_ner]
                )
                dct_organize_result["lst_id"].append(dct_es_source["case_id"])

            n_retry = 0
            while n_retry < 3:
                try:
                    ner_results = ner_model.get_named_entities(
                        dct_organize_result["lst_ner_inputs"],
                        use_batch=False,
                        max_length=None,
                        use_delim=False,
                        show_progress=False,
                    )
                    # logger.debug(f"ner_results: {ner_results}")
                    break
                except Exception as e:
                    self.logger.error(f"Unexpected error: {repr(e)}")
                    if n_retry == 0:
                        self.logger.warn(
                            f"lst_ner_inputs: {dct_organize_result['lst_ner_inputs']}"
                        )

                    n_retry += 1
                    if n_retry >= 3:
                        return True, dct_organize_result["lst_id"]

            dct_actions = {}
            for ner_res_idx, dct_ner_res in ner_results.items():
                dct_update_doc = {}
                for ner in settings.lst_self_defined_ner:
                    if ner in dct_ner_res:
                        # if ner_res_idx == 0:
                        #     logger.debug(
                        #         f"type(dct_ner_res['ner']):{type(dct_ner_res[ner])}"
                        #     )

                        lst_ner_words = []
                        for ner_word in list(dct_ner_res[ner]):
                            if ner_word not in set_stop_words:
                                lst_ner_words.append(ner_word)
                        dct_update_doc[f"{str_field_to_ner}_pos.{ner}"] = lst_ner_words
                    else:
                        dct_update_doc[f"{str_field_to_ner}_pos.{ner}"] = ""

                # dct_esdoc = {
                #     "_id": dct_organize_result["lst_id"][ner_res_idx],
                #     "doc": dct_update_doc,
                # }
                dct_actions[dct_organize_result["lst_id"][ner_res_idx]] = dct_update_doc
                # if ner_res_idx == 0:
                #     logger.debug(f"dct_esdoc:{dct_esdoc}")

            self.logger.debug(f"Done NER processing [{str_field_to_ner}]")
            return False, dct_actions

        return func

    def update_ner_info_to_es(
        self,
        str_start_date,
        str_end_date,
        n_es_return_docs_size,
        lst_es_source,
        str_es_scroll_time,
        lst_pos_fields,
    ):
        str_year = str_start_date.split("/")[0]
        index_name = self.get_petition_case_index_name(str_year)
        alias_name = self.get_petition_case_alias(str_year)
        # if index_name not in self.set_index_check_cache:
        #     self.es_create_index(index_name, alias_name)

        es_search_result = self.search_to_be_ner_doc(
            n_es_return_docs_size,
            lst_es_source,
            str_start_date,
            str_end_date,
            str_es_scroll_time,
            lst_pos_fields,
        )

        new_scroll_id = es_search_result["_scroll_id"]
        scroll_id = None
        n_number_of_docs = len(es_search_result["hits"]["hits"])
        self.logger.info(
            f"[{str_start_date}] [{n_number_of_docs}] docs need to be processed ----------------"
        )

        # Initialize CkipTransformer in multiple threads
        dct_thread_param = {
            "lst_fields_to_ner": ["petition_content", "petition_subject"],
            "lst_ner_model": [],
        }
        for _ in range(len(dct_thread_param["lst_fields_to_ner"])):
            str_model_name = "bert_base"
            is_use_gpu = -1
            transformer = CkipTransformer(is_use_gpu, 128, str_model_name)
            dct_thread_param["lst_ner_model"].append(transformer)

        dct_total_error_doc_id = {}
        n_docs_success = 0
        n_docs_error = 0
        n_docs_total = 0

        while n_number_of_docs > 0:
            # while scroll_id != new_scroll_id:
            scroll_id = new_scroll_id

            lst_actions = []
            # Doing NER process in multiple threads
            with concurrent.futures.ThreadPoolExecutor() as executor:
                lst_future_thread_results = executor.map(
                    self.process_ner_field(es_search_result["hits"]["hits"]),
                    dct_thread_param["lst_ner_model"],
                    dct_thread_param["lst_fields_to_ner"],
                )

                # Parsing NER results and preparing bulk update documents
                dct_first_thread_result = None
                lst_future_thread_results = list(lst_future_thread_results)
                n_length_thread_results = len(lst_future_thread_results)
                if n_length_thread_results > 0:
                    dct_first_thread_result: dict = lst_future_thread_results[0]
                    is_error = dct_first_thread_result[0]
                    _lst_error_doc_id = None
                    if not is_error:
                        for case_id, dct_v in dct_first_thread_result[1].items():
                            dct_es_doc = {
                                "_op_type": "update",
                                "_index": alias_name,
                                "_id": case_id,
                                "doc": {},
                            }
                            dct_es_doc["doc"].update(dct_v)

                            for idx in range(1, n_length_thread_results):
                                dct_thread_result: dict = lst_future_thread_results[idx]
                                is_error = dct_thread_result[0]
                                if not is_error:
                                    dct_ner_result = dct_thread_result[1].pop(
                                        case_id, None
                                    )
                                    if dct_ner_result:
                                        dct_es_doc["doc"].update(dct_ner_result)
                                else:
                                    _lst_error_doc_id = dct_thread_result[1]
                                    break

                            if is_error:
                                break
                            lst_actions.append(dct_es_doc)
                    else:
                        _lst_error_doc_id = dct_first_thread_result[1]

                    if is_error:
                        if str_start_date not in dct_total_error_doc_id:
                            dct_total_error_doc_id[str_start_date] = _lst_error_doc_id
                        else:
                            dct_total_error_doc_id[str_start_date].append(
                                _lst_error_doc_id
                            )
                        continue

            # logger.debug(f"lst_actions: {lst_actions}")

            # Bulk update documents in Elasticsearch
            dct_bulk_response = self.bulk_actions(
                lst_actions,
                op_type="update",
                str_record_date=str_start_date,
            )

            # Update total statistics
            for k, v in dct_bulk_response.items():
                if dct_bulk_response["lst_error_doc_id"]:
                    if (
                        dct_bulk_response["str_record_date"]
                        not in dct_total_error_doc_id
                    ):
                        dct_total_error_doc_id[dct_bulk_response["str_record_date"]] = (
                            dct_bulk_response["lst_error_doc_id"]
                        )
                    else:
                        dct_total_error_doc_id[
                            dct_bulk_response["str_record_date"]
                        ].append(dct_bulk_response["lst_error_doc_id"])
            n_docs_success += dct_bulk_response["n_docs_success"]
            n_docs_error += dct_bulk_response["n_docs_error"]
            n_docs_total += dct_bulk_response["n_docs_total"]

            # Start scrolling to avoid deep pagination issue in elasticsearch
            es_search_result = self.es.scroll(scroll_id=scroll_id, scroll="2m")

            # Update the scroll ID
            new_scroll_id = es_search_result["_scroll_id"]

            # Get the number of results that returned in the last scroll
            n_number_of_docs = len(es_search_result["hits"]["hits"])

            if n_number_of_docs > 0:
                self.logger.info(
                    f"[{str_start_date}] [{n_number_of_docs}] docs need to be processed ----------------"
                )
            else:
                self.logger.info(f"[{str_start_date}] Finished ----------------")

        return {
            "str_start_date": str_start_date,
            "str_end_date": str_end_date,
            "dct_total_error_doc_id": dct_total_error_doc_id,
            "n_docs_success": n_docs_success,
            "n_docs_error": n_docs_error,
            "n_docs_total": n_docs_total,
        }

    def get_petition_case_index_name(self, year):
        return f"{self.petition_case_index_base_name}-{year}"

    def get_petition_case_alias(self, year):
        return f"{self.petition_case_index_base_name}-{year}-alias"

    def calc_alias_name(self, str_start_time: str, str_end_time: str):
        n_start_year = int(str_start_time.split("/")[0])
        n_end_year = int(str_end_time.split("/")[0])
        lst_alias_name = []
        for n_year in range(n_start_year, n_end_year + 1):
            alias_name = self.get_petition_case_alias(n_year)
            lst_alias_name.append(alias_name)

        return lst_alias_name

    def es_create_index(self, index_name, alias_name=None, is_delete_index=False):
        is_index_exist = self.es.indices.exists(index=index_name)

        self.set_index_check_cache.add(index_name)

        # Delete index
        if is_index_exist and is_delete_index:
            self.es.indices.delete(index=index_name)
            self.logger.info(f"Index '{index_name}' deleted successfully")
            is_index_exist = False

        if not is_index_exist:
            es_setting_file_path = os.path.join("es", "setting.json")
            with open(es_setting_file_path, mode="r", encoding="utf-8") as file:
                dct_settings = json.load(file)

            es_mapping_file_path = os.path.join("es", "mapping.json")
            with open(es_mapping_file_path, mode="r", encoding="utf-8") as file:
                dct_mapping = json.load(file)

            # Define settings
            dct_index_settings = {
                "settings": dct_settings,
                "mappings": dct_mapping,
            }

            if alias_name:
                dct_index_settings.update({"aliases": {alias_name: {}}})
                self.logger.info(
                    f"alias '{alias_name}' appended to index '{index_name}'"
                )

            # Create index
            self.es.indices.create(index=index_name, body=dct_index_settings)
            self.logger.info(f"index '{index_name}' created successfully")
            return True
        else:
            self.logger.info(f"index '{index_name}' already exists")
            return False

    def search(self, dct_query: dict, alias_name: Union[str, List[str]], scroll=None):
        return self.es.search(index=alias_name, body=dct_query, scroll=scroll)

    def get(self, alias_name: str, id):
        return self.es.get(index=alias_name, id=id)

    def index(self, alias_name: str, id, document):
        return self.es.index(index=alias_name, id=id, body=document)

    def delete(self, alias_name: str, id):
        return self.es.delete(index=alias_name, id=id)

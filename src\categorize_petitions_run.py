import argparse
import logging
import os

from dotenv import load_dotenv

from use_case.batch_categorize_petitions_use_case import (
    batch_categorize_petitions_use_case,
)
from use_case.categorize_petitions_use_case import categorize_petitions_use_case

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """
    Main function to run the petition categorization script.
    """
    parser = argparse.ArgumentParser(
        description="Categorize petitions from category_exports folder or a single JSON file."
    )
    parser.add_argument(
        "--mode",
        type=str,
        choices=["single", "batch"],
        default="batch",
        help="Mode: 'single' for single file processing, 'batch' for category_exports folder processing.",
    )
    parser.add_argument(
        "--input",
        type=str,
        default=None,
        help="Path to the input JSON file (only used in single mode).",
    )
    parser.add_argument(
        "--output",
        type=str,
        default=None,
        help="Path to the output JSON file (only used in single mode).",
    )
    parser.add_argument(
        "--category-exports-dir",
        type=str,
        default=os.path.join("data", "category_exports"),
        help="Path to the category_exports directory (used in batch mode).",
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        default=os.path.join("data", "output"),
        help="Path to the output directory for batch processing results.",
    )
    args = parser.parse_args()

    logger.info("Starting petition categorization process...")

    try:
        if args.mode == "single":
            if not args.input or not args.output:
                logger.error(
                    "Single mode requires both --input and --output arguments."
                )
                return

            logger.info(f"Single mode - Input file: {args.input}")
            logger.info(f"Single mode - Output file: {args.output}")
            categorize_petitions_use_case(args.input, args.output)
            logger.info(
                f"Successfully processed petitions. Output saved to {args.output}"
            )

        elif args.mode == "batch":
            logger.info(
                f"Batch mode - Category exports directory: {args.category_exports_dir}"
            )
            logger.info(f"Batch mode - Output directory: {args.output_dir}")
            batch_categorize_petitions_use_case(
                args.category_exports_dir, args.output_dir
            )
            logger.info("Successfully processed all category files.")

    except FileNotFoundError as e:
        logger.error(f"Error: File not found - {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    load_dotenv()
    main()

import asyncio
import datetime
import json
import logging

import httpx
import pandas as pd

import settings
from helper import camel_to_snake, convert_time_format_and_key_names

# import requests
# from requests.exceptions import HTTPError


class DataCollectorRepo:
    def __init__(self):
        # 測試站 (資料不完整): "https://59.120.137.46/IntelligentAnalysis?function=getPetitionCaseListV1"
        self.url = "https://taotalk.tycg.gov.tw/IntelligentAnalysis?function=getPetitionCaseListV1"
        logger_name = settings.logger_name
        self.logger = logging.getLogger(logger_name)
        self.lst_api_data = None
        self.df = None

    async def __call_api(self, record_date: str):
        self.logger.debug(f"record_date:{record_date}")
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.get(self.url, params={"recordDate": record_date})
            dct_response = response.json()  # <class 'dict'>
            return dct_response

    async def fetch_data(self, start_time: str, end_time: str):
        try:
            start_date = datetime.datetime.strptime(start_time, "%Y/%m/%d")
            end_date = datetime.datetime.strptime(end_time, "%Y/%m/%d")
            delta = datetime.timedelta(days=1)

            tasks = []
            while start_date <= end_date:
                tasks.append(self.__call_api(start_date.strftime("%Y/%m/%d")))
                start_date += delta

            lst_responses = await asyncio.gather(*tasks)
            self.logger.debug(f"len(lst_responses): {len(lst_responses)}")

            lst_organized = []
            for dct_response in lst_responses:
                if "caseObjectList" in dct_response:
                    self.logger.debug(
                        f"len(dct_response['caseObjectList']): {len(dct_response['caseObjectList'])}"
                    )
                    lst_organized.extend(dct_response["caseObjectList"])
            self.logger.debug(f"len(lst_organized): {len(lst_organized)}")
            self.lst_api_data = lst_organized

            # response = requests.get(
            #     f"{self.url}&recordDate={record_date}", verify=False
            # )  # recordDate=2024/03/30

            # self.logger.debug(f"response.text: {response.text}, {type(response.text)}")  # <class 'str'>
            # dct_response = response.json()  # <class 'dict'>
            # self.logger.debug(f"response.json(): {dct_response}")

            # self.lst_api_data: dict = dct_response["caseObjectList"]

            # If the response was successful, no Exception will be raised
            # response.raise_for_status()

        except Exception as err:
            self.logger.error(f"Other error occurred: {err}")
        else:
            self.logger.info("Fetched data successfully")
            return lst_organized

    def organize_data(self):
        if not self.lst_api_data:
            self.logger.warning("self.lst_api_data data is empty")
            return

        df = pd.DataFrame(self.lst_api_data)

        df.columns = df.columns.map(camel_to_snake)

        # Transform "recordTime"
        df["record_time"] = pd.to_datetime(df["record_time"], format="%Y/%m/%d/%H/%M")

        df.sort_values("record_time", inplace=True)

        self.logger.info("Dataframe loaded and sorted successfully")

        lst_datetime_fields = ["deadline"]
        df["process_list"] = df["process_list"].apply(
            lambda x: [
                convert_time_format_and_key_names(i, lst_datetime_fields) for i in x
            ]
        )

        df["recent_process"] = df["process_list"].apply(lambda x: x.pop() if x else {})

        lst_datetime_fields = []
        for col in df.columns:
            if "time" in col or col in lst_datetime_fields:
                df[col] = pd.to_datetime(
                    df[col], format="%Y/%m/%d/%H/%M", errors="coerce"
                ).dt.strftime("%Y/%m/%d %H:%M:%S")

                # Replace empty strings in column with None
                df[col] = df[col].replace("", None)

        def process_case_status_value(value: str):
            lst_case_status_allowed_value = [
                "案件已結案",
                "滿意度調查中",
                "案件辦理中",
                "",
            ]

            value = value.strip()
            if value in lst_case_status_allowed_value:
                return value
            else:
                return None

        df["case_status"] = df["case_status"].apply(process_case_status_value)

        def process_client_sex_value(value: str):
            lst_client_sex_allowed_value = ["男", "女", "其他", ""]

            value = value.strip()
            if value in lst_client_sex_allowed_value:
                return value
            else:
                return None

        df["client_sex"] = df["client_sex"].apply(process_client_sex_value)

        def process_client_age_value(value: str):
            lst_client_age_allowed_value = [
                "20 歲以下",
                "21-30 歲",
                "31-40 歲",
                "41-50 歲",
                "51 歲至 60 歲",
                "61 歲以上",
            ]

            value = value.strip()
            if value in lst_client_age_allowed_value:
                return value
            else:
                return None

        df["client_age"] = df["client_age"].apply(process_client_age_value)

        def process_recent_process_value(dct_value: dict):
            lst_close_method_allowed_value = ["回覆", "存查", ""]
            close_method = dct_value.get("close_method", "")
            close_method = close_method.strip()
            if close_method not in lst_close_method_allowed_value:
                dct_value["close_method"] = None

            lst_process_type_allowed_value = [
                "第一階段",
                "第二階段",
                "問卷不滿意續列管",
                "",
            ]
            process_type = dct_value.get("process_type", "")
            process_type = process_type.strip()
            if process_type not in lst_process_type_allowed_value:
                dct_value["process_type"] = None

            lst_qn_answer_allowed_value = [
                "尚未回答",
                "逾期未答",
                "非常滿意",
                "滿意",
                "普通",
                "不滿意",
                "非常不滿意",
                "",
            ]
            qn_answer = dct_value.get("qn_answer", "")
            qn_answer = qn_answer.strip()
            if qn_answer not in lst_qn_answer_allowed_value:
                dct_value["qn_answer"] = None

            lst_yes_no_allowed_value = ["Y", "N", ""]
            lst_columns_with_yes_no = ["is_extend", "is_over_due"]
            for key in lst_columns_with_yes_no:
                v = dct_value.get(key, "")
                v = v.strip().upper()
                if v not in lst_yes_no_allowed_value:
                    if v == "YES":
                        v = "Y"
                    elif v == "NO":
                        v = "N"
                    else:
                        v = None

                    dct_value[key] = v

            return dct_value

        df["recent_process"] = df["recent_process"].apply(process_recent_process_value)

        self.logger.info(f"Data organized successfully, len(df): {len(df)}")
        # self.logger.info(df.head())
        # df.to_json(
        #     "dummy-data.json",
        #     orient="records",
        #     force_ascii=False,
        #     date_format="%Y/%m/%d %H:%M:%S",
        #     indent=4,
        # )
        with open("dummy-data2.json", "w", encoding="utf8") as outfile:
            json.dump(
                df.to_dict(orient="records"), outfile, ensure_ascii=False, indent=4
            )
        self.df = df
        return df

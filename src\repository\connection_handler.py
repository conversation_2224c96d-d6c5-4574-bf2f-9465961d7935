import logging
import os
from time import sleep

from elasticsearch import ConnectionTimeout, Elasticsearch, TransportError

import settings


class ElasticsearchConnector:
    def __init__(self, host="localhost", port=9200):
        logger_name = settings.logger_name
        self.logger = logging.getLogger(logger_name)

        ES_HOST = os.environ.get("ES_HOST", "localhost")
        ES_PORT = os.environ.get("ES_PORT", 9200)
        ES_PASSWORD = os.environ.get("ES_PASSWORD", "bigdata")
        RUNTIME_ENV = os.environ["TYCG_BE_RUNTIME_ENV"]  # PROD or DEV
        IS_LOCAL_SSH_PORT_FORWARDED = (
            os.environ.get("IS_LOCAL_SSH_PORT_FORWARDED", "False") == "True"
        )

        if RUNTIME_ENV in ["PROD", "DEV"]:
            if not IS_LOCAL_SSH_PORT_FORWARDED:
                ES_CA_CERT = os.environ.get("ES_CA_CERT", "")
                ca_certs_path = os.path.join("es", "certs", "ca.crt")
                with open(ca_certs_path, "w") as f:
                    f.write(ES_CA_CERT)

                # Set the file permissions to allow read access for all users
                os.chmod(ca_certs_path, 0o644)
            else:
                if RUNTIME_ENV == "DEV":
                    ca_certs_path = "es/certs/dev/ca/ca.crt"
                elif RUNTIME_ENV == "PROD":
                    ca_certs_path = "es/certs/prod/ca/ca.crt"
        else:
            ca_certs_path = "es/certs/local/ca/ca.crt"

        self.logger.debug(f"ca_certs_path:{ca_certs_path}")
        self.logger.debug(
            f"os.path.exists(ca_certs_path): {os.path.exists(ca_certs_path)}"
        )
        self.logger.debug(f"ES_HOST:{ES_HOST}")
        self.logger.debug(f"ES_PORT:{ES_PORT}")
        self.logger.debug(f"ES_PASSWORD:{ES_PASSWORD}")
        self.logger.debug(f"ES_CERT:{ca_certs_path}")
        self.logger.debug(f"RUNTIME_ENV:{RUNTIME_ENV}")

        self.logger.info("Initializing Elasticsearch instance...")
        # Create a connection
        self.es = Elasticsearch(
            f"https://{ES_HOST}:{ES_PORT}",
            ca_certs=ca_certs_path,
            basic_auth=("elastic", ES_PASSWORD),
            request_timeout=60,
        )
        self.connection_attempts = 0

    def get_es(self):
        while True:
            try:
                self.logger.info("Checking Elasticsearch connection...")
                if self.es.ping():
                    self.logger.info("Elasticsearch is up!")
                    return self.es
                else:
                    if self.connection_attempts < 3:
                        self.connection_attempts += 1
                        sleep(2**self.connection_attempts)
                        continue
                    else:
                        raise ConnectionTimeout("Connection to Elasticsearch timed out")
            except (ConnectionTimeout, TransportError) as e:
                self.logger.info("Elasticsearch is down!")
                if self.connection_attempts < 3:
                    self.connection_attempts += 1
                    sleep(2**self.connection_attempts)
                    continue
                else:
                    raise e

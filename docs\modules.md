# 核心模組詳解 (Core Modules)

本文件旨在深入解析專案中的核心類別與其關鍵方法，以便於後續的維護與二次開發。

## 1. `ner_worker_run.py`

這是 NER (命名實體辨識) 資料擴充流程的進入點。

-   **主要職責**:
    -   解析命令列參數與環境變數，確定執行的日期範圍。
    -   實例化 `ElasticsearchRepository`。
    -   呼叫 Repository 的方法來執行主要的 NER 更新邏輯。
    -   記錄執行結果的統計數據 (成功、失敗、總數)。
    -   透過 Line Notify 發送最終的執行狀態通知。

## 2. `gpt_summary_run.py`

這是 GPT 摘要與統計報告生成流程的進入點。

-   **主要職責**:
    -   解析命令列參數與環境變數，確定執行的日期範圍或週次。
    -   實例化 `ElasticsearchSummarizer` 和 `GPTReportWriter`。
    -   編排主要的業務邏輯：
        1.  取得案件分類。
        2.  迭代每個分類，計算統計數據。
        3.  檢索文件。
        4.  呼叫 `GPTReportWriter` 生成摘要。
        5.  將結果寫入 Elasticsearch。
    -   記錄執行結果的統計數據。
    -   透過 Line Notify 發送最終的執行狀態通知。

## 3. `repository/elasticsearch_repo.py`

### `ElasticsearchRepository`

這個類別封裝了所有與 `petition-case` 索引相關的底層操作。

-   **`__init__(self)`**:

    -   初始化日誌。
    -   建立 `ElasticsearchConnector` 以取得 ES 客戶端實例。
    -   定義索引和別名的基本名稱。

-   **`update_ner_info_to_es(...)`**:

    -   **核心方法**。編排了從查詢、NER 處理到寫回的完整流程。
    -   內部使用 `concurrent.futures.ThreadPoolExecutor` 來平行化 NER 處理，顯著提升效能。
    -   使用 `scroll` API 來高效處理大量文件。

-   **`search_to_be_ner_doc(...)`**:

    -   建構一個複雜的 Elasticsearch `bool` 查詢，其 `must_not` 條件能精準地找出尚未被任何 NER 標籤處理過的文件。

-   **`process_ner_field(...)`**:

    -   這是一個高階函數 (Higher-Order Function)，它返回一個真正的處理函數。
    -   返回的函數負責接收一批文件，呼叫 `CkipTransformer` 模型進行 NER 分析，並將結果組織成以 `case_id` 為鍵的字典。

-   **`bulk_actions(...)`**:
    -   一個通用的批次操作方法，封裝了 `elasticsearch.helpers.bulk` 的呼叫。
    -   包含了完整的錯誤處理邏輯，能捕捉 `BulkIndexError` 並記錄失敗文件的 ID。

## 4. `repository/gpt_summary_repo.py`

這個檔案包含兩個核心類別。

### `GPTReportWriter`

負責所有與 LLM 互動的邏輯，是一個 Facade (外觀模式) 的實現。

-   **`__init__(self)`**:

    -   從環境變數讀取 Azure 和 OpenAI 的金鑰與端點。
    -   初始化兩個服務的客戶端實例，以便後續的備援切換。
    -   初始化 Token 計算器和錯誤記錄器。

-   **`summarize_documents(...)`**:

    -   **核心演算法**。實現了遞迴式摘要 (Recursive Summarization)。
    -   首先將大量文件分批 (batching)，對每個批次進行初步摘要。
    -   然後將初步摘要的結果再進行遞迴式的摘要，直到內容收斂為最終的報告格式。
    -   此方法會將中間產生的 prompt 和摘要結果寫入 `gpt_summary/` 資料夾下，便於追蹤和除錯。

-   **`_call_openai(...)`**:
    -   一個私有的輔助方法，封裝了對 `chat.completions.create` 的呼叫。
    -   實現了 API 服務的**備援機制**：優先使用 Azure，若失敗則自動切換到 OpenAI。
    -   實現了**重試機制** (最多 3 次)。
    -   處理 API 回應，更新 Token 計數器。
    -   呼叫 `opencc` 將模型可能輸出的簡體中文轉換為繁體中文。

### `ElasticsearchSummarizer`

這個類別封裝了所有與 `petition-summary` 索引以及在摘要流程中需要查詢 `petition-case` 的操作。

-   **`get_category_group(...)`**:

    -   使用 Elasticsearch 的 `terms` 聚合功能，高效地一次性查詢出指定時間範圍內所有案件的「主分類/次分類」組合。

-   **`get_case_count_by_category_group(...)`**:

    -   建構查詢以計算特定分類下的案件數量。
    -   支援 `is_return_query_only=True`，這在與 `msearch` API 結合使用時非常有用，可以先產生查詢本體，再一次性發送多個查詢。

-   **`msearch(...)`**:

    -   封裝了 `msearch` API 的呼叫，能將多個查詢請求合併為一次網路往返，大幅提升查詢效率。在計算當期與前期案件數時被使用。

-   **`check_if_summary_exists(...)`**:

    -   用於檢查特定分類的摘要是否已經存在於 `petition-summary` 索引中。這使得 `gpt_summary_run.py` 可以選擇性地跳過已完成的項目。

-   **`generate_id(...)`**:

    -   一個靜態方法，使用 `hashlib.sha256` 為 `petition-summary` 的文件產生一個唯一的、可重現的 ID。這是確保寫入操作冪等性的關鍵。

-   **`bulk_operation(...)`**:
    -   與 `ElasticsearchRepository` 中的 `bulk_actions` 類似，但特別為 `petition-summary` 的 `update/upsert` 操作進行了客製化。

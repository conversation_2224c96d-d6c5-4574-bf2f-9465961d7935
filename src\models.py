from __future__ import annotations

from enum import Enum
from typing import Any, Optional, Union

from pydantic import BaseModel, Field, field_validator


class PetitionInput(BaseModel):
    """
    Represents a single petition record read from the input file.
    """

    case_id: str
    record_channel: str
    record_time: str
    main_category: Union[str, list[str]]
    sub_category: Union[str, list[str]]
    petition_subject: str
    petition_content: str

    @field_validator("main_category", "sub_category", mode="before")
    @classmethod
    def single_to_list(cls, v):
        if isinstance(v, str):
            return [v]
        return v


class PetitionOutput(BaseModel):
    """
    Represents the final structured output for a single petition.
    """

    original_data: dict[str, Any]
    main_category: str
    sub_category: str
    ground_truth_category: Optional[list[str]]
    is_accurate: Optional[bool]


class AICategoryResponse(BaseModel):
    """
    Represents the structured response expected from the OpenAI API.
    """

    new_category: DetailedPetitionCategory = Field(
        description="The category of the petition."
    )


class PetitionCategory(str, Enum):
    """
    Enum for valid petition categories.
    """

    LIFE_THREATENING = "造成民眾生命危險"
    HEALTH_HAZARD = "影響民眾身體健康"
    VIOLENT_THREAT = "暴力威脅事件"
    WOMEN_CHILDREN_ISSUE = "涉及婦幼及學童議題"
    LIVELIHOOD_INTERRUPTION = "民生需求中斷"
    OTHER_EMERGENCY = "其他應緊急處理案件"
    TRAFFIC_VIOLATION_REPORT = "檢舉交通違規"
    TRAFFIC_TICKET_APPEAL = "交通罰單申訴"
    TOWING_DISPUTE = "車輛拖吊爭議"
    TRAFFIC_CONGESTION = "交通疏導或壅塞通報"
    TRAFFIC_CAMERA_REQUEST = "闖紅燈（超速）照相桿增設或維護"
    CCTV_ISSUE = "監視器問題"
    PUBLIC_SAFETY = "治安維護"
    INDECENT_BEHAVIOR = "妨害風化（俗）"
    POLICE_DISCIPLINE = "警政風紀"
    POLICE_OTHER = "警政及交通裁罰業務_其他"
    ROAD_OBSTRUCTION = "占用道路、騎樓及人行道"
    ABANDONED_VEHICLE_LICENSED = "有牌廢棄車查報"
    ABANDONED_VEHICLE_UNLICENSED = "無牌廢棄車查報"
    AD_VEHICLE_OCCUPANCY = "廣告車輛長期占用停車格"
    ILLEGAL_PARKING = "違規停車"
    ROADBLOCK_OTHER = "路霸排除_其他"
    UNEVEN_ROAD = "路面不平整或掏空破洞"
    OIL_STAIN_CLEANUP = "路面油漬清除"
    GUTTER_CLEANING = "道路側溝清淤或惡臭處理"
    GUTTER_COVER_REPAIR = "水溝溝蓋維修"
    ROAD_CONSTRUCTION_ISSUE = "道路施工時間、交通管制及安全管理問題"
    ROAD_FLOODING = "道路淹（積）水通報"
    CABLE_ISSUE = "電纜下地或纜線垂落"
    MANHOLE_COVER_NOISE = "孔蓋異音"
    ROAD_MAINTENANCE_OTHER = "道路、水溝維護_其他"
    STREET_LIGHT_MALFUNCTION = "路燈故障"
    STREET_LIGHT_REQUEST = "路燈新增或遷移申請"
    PARK_FACILITY_DAMAGE = "公園設施損壞"
    FALLEN_TREE = "路樹傾倒"
    PARK_MAINTENANCE = "公園、綠地及路樹養護"
    NEW_PARK_SUGGESTION = "新闢公園建議案"
    LIGHT_TREE_PARK_OTHER = "路燈、路樹及公園管理維護_其他"
    TRAFFIC_LIGHT_MALFUNCTION = "交通號誌(紅綠燈)故障或損壞傾斜"
    TRAFFIC_SIGN_DAMAGE = "交通標誌牌面、反光鏡損壞傾斜"
    TRAFFIC_LIGHT_ADJUSTMENT = "交通號誌增設、移除或紅綠燈秒數調整"
    TRAFFIC_SIGN_REQUEST = "交通標誌、標線、反光鏡設置或移除"
    ROADSIDE_PARKING_ISSUE = "路邊停車格問題"
    PARKING_FEE_ISSUE = "停車費問題"
    BUS_ISSUE = "公車問題"
    BUS_STOP_FACILITY = "站牌、候車亭設施管理"
    FREE_CITIZEN_BUS_ISSUE = "免費市民公車（樂活巴）問題"
    TAXI_ISSUE = "計程車問題及招呼站設施管理"
    BUS_TRACKING_SYSTEM_ISSUE = "公車動態系統問題"
    UBIKE_ISSUE = "公共自行車（UBike）租賃問題"
    MRT_OPERATION = "捷運營運及管理"
    MRT_CONSTRUCTION_ISSUE = "捷運建設工程相關問題"
    TRANSPORT_OTHER = "交通號誌、標誌、標線及大眾運輸_其他"
    DIRTY_SPOT_REPORT = "髒亂點查報"
    ANIMAL_CARCASS_REMOVAL = "犬貓屍體清除"
    ILLEGAL_ADVERTISEMENT = "違規張貼廣告物"
    WASTE_REMOVAL_APPOINTMENT = "廢棄物清運預約"
    WASTE_MANAGEMENT = "廢棄物管理（含清除、處理、再利用、輸出）"
    GARBAGE_TRUCK_ROUTE = "垃圾車清運動線及管理"
    RESIDENTIAL_NOISE = "住宅內人與動物噪音"
    VEHICLE_NOISE = "住家、改裝車噪音"
    COMMERCIAL_INDUSTRIAL_NOISE = "營業場所、工廠及施工噪音"
    AIR_POLLUTION = "空氣污染"
    WATER_POLLUTION = "工廠排放廢水、河川污染"
    GENERAL_POLLUTION = "綜合性環境污染"
    ENVIRONMENTAL_OTHER = "噪音、污染及環境維護_其他"
    SOCIAL_HOUSING = "社會住宅管理"
    ILLEGAL_CONSTRUCTION_REPORT = "一般違建查報"
    ILLEGAL_CONSTRUCTION_IN_PROGRESS = "興建中違建查報"
    ILLEGAL_CONSTRUCTION_DEMOLITION_ISSUE = "已查報違建拆除問題"
    ILLEGAL_SIGNAGE = "違規招牌或樹立廣告物查報"
    APARTMENT_MANAGEMENT_ISSUE = "公寓大廈管理問題"
    BUILDING_REGULATION_ISSUE = "建築法規問題"
    BUILDING_PUBLIC_SAFETY = "建築物公共安全問題"
    CONSTRUCTION_DAMAGE_TO_NEIGHBORS = "領有建造執照施工損鄰"
    BUILDING_MANAGEMENT_OTHER = "建築管理_其他"
    BEEHIVE_SNAKE_REMOVAL = "捕蜂、抓蛇"
    ANIMAL_RESCUE = "動物受困、受傷通報"
    ANIMAL_SHELTER_ADOPTION = "動物收容及認養問題"
    UNLEASHED_DOG = "遛狗未繫繩"
    IMPROPER_ANIMAL_CARE = "不當飼養"
    ILLEGAL_PET_BUSINESS = "非法業者及業者違規問題"
    ANIMAL_PROTECTION_OTHER = "動物保護及寵物管理_其他"
    TEACHER_RECRUITMENT = "教師介聘甄選"
    HIGH_SCHOOL_ISSUE = "高級中等學校問題"
    JUNIOR_HIGH_SCHOOL_ISSUE = "國中學校問題"
    ELEMENTARY_SCHOOL_ISSUE = "國小學校問題"
    CRAM_SCHOOL_ISSUE = "補教問題"
    KINDERGARTEN_ISSUE = "幼兒園問題"
    LIFELONG_LEARNING_ISSUE = "社區大學、樂齡學習等終身教育問題"
    SPECIAL_EDUCATION_ISSUE = "特殊教育問題"
    SCHOOL_SPORTS_ISSUE = "學校體育問題"
    SPORTS_EVENT_VENUE = "體育活動及場務管理"
    EDUCATION_SPORTS_OTHER = "教育及體育_其他"
    LABOR_LAW_CONSULTATION = "勞工法令諮詢"
    LABOR_DISPUTE_MEDIATION = "勞資糾紛協調"
    EMPLOYMENT_SERVICE_VOCATIONAL_TRAINING = "就業服務及職業訓練"
    MIGRANT_WORKER_AFFAIRS = "移工業務"
    LABOR_LAW_VIOLATION_REPORT = "檢舉公司（雇主）違反勞動法規"
    EMPLOYMENT_DISCRIMINATION = "就業歧視"
    DISABILITY_EMPLOYMENT = "身障就業"
    LABOR_ADMINISTRATION_OTHER = "勞動行政_其他"
    SOCIAL_ASSISTANCE = "社會救助（中、低收入戶、急難救助及馬上關懷等）"
    DISABILITY_WELFARE_REHAB_BUS = "身心障礙福利及復康巴士"
    SENIOR_WELFARE_LONG_TERM_CARE = "銀髮族福利、長期照顧及日間照顧"
    CHILD_YOUTH_WELFARE = "兒少福利、弱勢兒少生活扶助、緊急生活扶助及醫療補助"
    HOUSING_RENTAL_SUBSIDY = "住宅租金補貼"
    FAMILY_SERVICE_CENTER = "家庭服務中心"
    DOMESTIC_VIOLENCE_PREVENTION = "家庭暴力、性侵害、兒少保護及性騷擾等防治工作"
    CIVIL_ORGANIZATION_GUIDANCE = "人民團體組織輔導"
    SOCIAL_ASSISTANCE_WELFARE_OTHER = "社會救助及社會福利_其他"
    WOMEN_NEW_IMMIGRANT_WELFARE = "婦女、新住民福利、特殊境遇及補助"
    WOMEN_CHILDREN_CENTER_MANAGEMENT = "婦女館、親子館管理及托育服務"
    WOMEN_EMPOWERMENT_GENDER_EQUALITY = "婦女培力、性別平等業務"
    CHILDCARE_SUBSIDY_0_2 = "0–2 歲育兒經濟補助"
    EARLY_INTERVENTION_SERVICE_SUBSIDY = "兒童早期療育服務及補助"
    WOMEN_CHILD_NEW_IMMIGRANT_OTHER = "婦幼、新住民福利及健康照顧_其他"
    CULTURAL_HERITAGE_ISSUE = "文化資產問題"
    ARTS_PERFORMANCE_EVENT = "藝文展演活動"
    LIBRARY_READING_ROOM_MANAGEMENT = "圖書館、閱覽室及館舍管理"
    ARTS_VENUE_MANAGEMENT = "藝文館舍管理"
    CULTURAL_AFFAIRS_LIBRARY_OTHER = "文化藝術及圖書管理_其他"
    UTILITY_ISSUE = "水、電、瓦斯等公用事業問題"
    BUSINESS_REGISTRATION_ISSUE = "工商登記問題"
    MARKET_VENDOR_MANAGEMENT = "市場攤販管理"
    ILLEGAL_FACTORY_REPORT = "檢舉工廠違規營業"
    ILLEGAL_STORE_REPORT = "檢舉商店違規營業"
    ILLEGAL_HOTEL_BNB_REPORT = "檢舉旅館、民宿違規營業"
    TAX_ISSUE = "稅務問題"
    BUSINESS_ECONOMY_TAX_OTHER = "工商、經濟及稅務_其他"
    HOUSEHOLD_REGISTRATION_SERVICE = "戶政服務"
    FUNERAL_SERVICE = "殯葬禮儀"
    RELIGIOUS_AFFAIRS = "宗教事務"
    MILITARY_SERVICE_ISSUE = "兵役問題"
    CIVIL_AFFAIRS_OTHER = "民政業務_其他"
    THANK_YOU_LETTER = "感謝函"
    SERVICE_ATTITUDE_ISSUE = "服務態度問題"
    ADMINISTRATIVE_EFFICIENCY_ISSUE = "行政效率問題"
    PROFESSIONAL_KNOWLEDGE_ISSUE = "專業知識問題"
    GOVERNMENT_WEBSITE_APP_ISSUE = "市府網站或 APP 管理問題"
    SERVICE_QUALITY_OTHER = "感謝函、服務品質及網站／APP 管理問題_其他"
    GENERAL_CARD_APPLICATION = "一般卡申辦問題"
    SENIOR_CARD_APPLICATION = "敬老卡申辦問題"
    DISABILITY_CARD_APPLICATION = "愛心卡、愛心陪伴卡申辦問題"
    VOLUNTEER_CARD_APPLICATION = "志工卡申辦問題"
    STUDENT_CARD_APPLICATION = "學生卡申辦問題"
    MOBILE_COBRANDED_CARD_APPLICATION = "行動卡、聯名卡申辦問題"
    CARD_USAGE_ISSUE = "卡片感應及使用問題"
    CITIZEN_CARD_BENEFIT_SUGGESTION = "市民卡優惠及加值服務建議"
    CITIZEN_CARD_OTHER = "市民卡業務_其他"
    FOOD_SAFETY_HYGIENE = "食品安全衛生"
    MEDICAL_MANAGEMENT = "醫療管理"
    DRUG_COSMETIC_MANAGEMENT = "藥品及化妝品管理"
    DISEASE_CONTROL_VACCINATION = "傳染病防治及預防接種"
    TOBACCO_HAZARD_CONTROL = "菸害防制"
    SUICIDE_PREVENTION_MENTAL_HEALTH = "自殺防治及心理健康"
    HEALTH_ADMINISTRATION_OTHER = "衛生行政_其他"
    LAND_EXPROPRIATION = "土地徵收"
    LAND_READJUSTMENT = "土地重劃"
    LAND_SURVEYING = "土地測量"
    CADASTRAL_RESURVEY = "地籍圖重測"
    REAL_ESTATE_TRANSACTION = "不動產交易"
    LAND_BUILDING_REGISTRATION = "土地及建物登記"
    ILLEGAL_LAND_USE_REPORT = "檢舉土地違規使用"
    LAND_ADMINISTRATION_OTHER = "地政服務_其他"
    FIRE_SAFETY_EQUIPMENT_INSPECTION = "消防設備、安全檢查"
    GAS_CYLINDER_STORAGE_ISSUE = "瓦斯桶儲放問題"
    FIRE_HYDRANT_ISSUE = "消防栓（設置、移位、告示牌）"
    FIRE_LANE_OBSTRUCTION = "防火巷違建、堆放雜物"
    FIRE_ADMINISTRATION_OTHER = "消防行政_其他"
    BRIBERY = "行、收賄"
    ADMINISTRATIVE_MALFEASANCE = "行政違失"
    OTHER_MALFEASANCE = "其他瀆職情形"
    OTHER_REPORT = "其他檢舉案件"
    OTHER_SUGGESTION_CONSULTATION_PETITION = "其他建議、諮詢或陳情"


class DetailedPetitionCategory_2025(str, Enum):
    """
    Enum for detailed petition categories.
    """

    # --- 緊急案件 (Emergency Cases) ---
    EMERGENCY_LIFE_THREATENING = "緊急案件_造成民眾生命危險"
    EMERGENCY_HEALTH_HAZARD = "緊急案件_影響民眾身體健康"
    EMERGENCY_VIOLENT_THREAT = "緊急案件_暴力威脅事件"
    EMERGENCY_WOMEN_CHILDREN_ISSUE = "緊急案件_涉及婦幼及學童議題"
    EMERGENCY_LIVELIHOOD_INTERRUPTION = "緊急案件_民生需求中斷"
    EMERGENCY_OTHER = "緊急案件_其他應緊急處理案件"

    # --- 警政及交通裁罰業務 (Police and Traffic Enforcement) ---
    POLICE_TRAFFIC_VIOLATION_REPORT = "警政及交通裁罰業務_檢舉交通違規"
    POLICE_TRAFFIC_TICKET_APPEAL = "警政及交通裁罰業務_交通罰單申訴"
    POLICE_TOWING_DISPUTE = "警政及交通裁罰業務_車輛拖吊爭議"
    POLICE_TRAFFIC_CONGESTION_REPORT = "警政及交通裁罰業務_交通疏導或壅塞通報"
    POLICE_TRAFFIC_CAMERA_REQUEST = "警政及交通裁罰業務_闖紅燈（超速）照相桿增設或維護"
    POLICE_MONITORING_CAMERA_ISSUE = "警政及交通裁罰業務_監視器問題"
    POLICE_SECURITY_MAINTENANCE = "警政及交通裁罰業務_治安維護"
    POLICE_PUBLIC_MORALS_OFFENSE = "警政及交通裁罰業務_妨害風化（俗）"
    POLICE_DISCIPLINE = "警政及交通裁罰業務_警政風紀"
    POLICE_OTHER = "警政及交通裁罰業務_其他"

    # --- 路霸排除 (Roadblock Removal) ---
    ROADBLOCK_OBSTRUCTION = "路霸排除_占用道路、騎樓及人行道"
    ROADBLOCK_LICENSED_ABANDONED_VEHICLE = "路霸排除_有牌廢棄車查報"
    ROADBLOCK_UNLICENSED_ABANDONED_VEHICLE = "路霸排除_無牌廢棄車查報"
    ROADBLOCK_AD_VEHICLE_PARKING = "路霸排除_廣告車輛長期占用停車格"
    ROADBLOCK_ILLEGAL_PARKING = "路霸排除_違規停車"
    ROADBLOCK_OTHER = "路霸排除_其他"

    # --- 道路、水溝維護 (Road and Gutter Maintenance) ---
    MAINTENANCE_ROAD_SURFACE = "道路、水溝維護_路面不平整或掏空破洞"
    MAINTENANCE_OIL_SPILL_CLEANUP = "道路、水溝維護_路面油漬清除"
    MAINTENANCE_GUTTER_CLEANING = "道路、水溝維護_道路側溝清淤或惡臭處理"
    MAINTENANCE_GUTTER_COVER_REPAIR = "道路、水溝維護_水溝溝蓋維修"
    MAINTENANCE_ROAD_CONSTRUCTION_ISSUE = (
        "道路、水溝維護_道路施工時間、交通管制及安全管理問題"
    )
    MAINTENANCE_ROAD_FLOODING = "道路、水溝維護_道路淹（積）水通報"
    MAINTENANCE_DANGLING_CABLES = "道路、水溝維護_電纜下地或纜線垂落"
    MAINTENANCE_MANHOLE_COVER_NOISE = "道路、水溝維護_孔蓋異音"
    MAINTENANCE_ROAD_OTHER = "道路、水溝維護_其他"

    # --- 路燈、路樹及公園管理維護 (Streetlight, Tree, and Park Maintenance) ---
    MAINTENANCE_STREETLIGHT_MALFUNCTION = "路燈、路樹及公園管理維護_路燈故障"
    MAINTENANCE_STREETLIGHT_REQUEST = "路燈、路樹及公園管理維護_路燈新增或遷移申請"
    MAINTENANCE_PARK_FACILITY_DAMAGE = "路燈、路樹及公園管理維護_公園設施損壞"
    MAINTENANCE_FALLEN_TREE = "路燈、路樹及公園管理維護_路樹傾倒"
    MAINTENANCE_PARK_GREENERY_CARE = "路燈、路樹及公園管理維護_公園、綠地及路樹養護"
    MAINTENANCE_NEW_PARK_PROPOSAL = "路燈、路樹及公園管理維護_新闢公園建議案"
    MAINTENANCE_LIGHT_TREE_PARK_OTHER = "路燈、路樹及公園管理維護_其他"

    # --- 交通號誌、標誌、標線及大眾運輸 (Traffic Signals, Signs, Markings, and Public Transport) ---
    TRANSPORT_TRAFFIC_LIGHT_MALFUNCTION = (
        "交通號誌、標誌、標線及大眾運輸_交通號誌(紅綠燈)故障或損壞傾斜"
    )
    TRANSPORT_SIGN_REFLECTOR_DAMAGE = (
        "交通號誌、標誌、標線及大眾運輸_交通標誌牌面、反光鏡損壞傾斜"
    )
    TRANSPORT_TRAFFIC_LIGHT_ADJUSTMENT = (
        "交通號誌、標誌、標線及大眾運輸_交通號誌增設、移除或紅綠燈秒數調整"
    )
    TRANSPORT_SIGN_MARKING_REQUEST = (
        "交通號誌、標誌、標線及大眾運輸_交通標誌、標線、反光鏡設置或移除"
    )
    TRANSPORT_PARKING_SPACE_ISSUE = "交通號誌、標誌、標線及大眾運輸_路邊停車格問題"
    TRANSPORT_PARKING_FEE_ISSUE = "交通號誌、標誌、標線及大眾運輸_停車費問題"
    TRANSPORT_BUS_ISSUE = "交通號誌、標誌、標線及大眾運輸_公車問題"
    TRANSPORT_BUS_STOP_FACILITY = "交通號誌、標誌、標線及大眾運輸_站牌、候車亭設施管理"
    TRANSPORT_FREE_CITIZEN_BUS = (
        "交通號誌、標誌、標線及大眾運輸_免費市民公車（樂活巴）問題"
    )
    TRANSPORT_TAXI_ISSUE = "交通號誌、標誌、標線及大眾運輸_計程車問題及招呼站設施管理"
    TRANSPORT_BUS_DYNAMIC_SYSTEM = "交通號誌、標誌、標線及大眾運輸_公車動態系統問題"
    TRANSPORT_UBIKE_ISSUE = "交通號誌、標誌、標線及大眾運輸_公共自行車（UBike）租賃問題"
    TRANSPORT_MRT_OPERATION = "交通號誌、標誌、標線及大眾運輸_捷運營運及管理"
    TRANSPORT_MRT_CONSTRUCTION = "交通號誌、標誌、標線及大眾運輸_捷運建設工程相關問題"
    TRANSPORT_OTHER = "交通號誌、標誌、標線及大眾運輸_其他"

    # --- 噪音、污染及環境維護 (Noise, Pollution, and Environmental Maintenance) ---
    ENV_MESSY_SPOT_REPORT = "噪音、污染及環境維護_髒亂點查報"
    ENV_ANIMAL_CARCASS_REMOVAL = "噪音、污染及環境維護_犬貓屍體清除"
    ENV_ILLEGAL_ADVERTISING = "噪音、污染及環境維護_違規張貼廣告物"
    ENV_WASTE_COLLECTION_APPOINTMENT = "噪音、污染及環境維護_廢棄物清運預約"
    ENV_WASTE_MANAGEMENT = (
        "噪音、污染及環境維護_廢棄物管理（含清除、處理、再利用、輸出）"
    )
    ENV_GARBAGE_TRUCK_ROUTE = "噪音、污染及環境維護_垃圾車清運動線及管理"
    ENV_RESIDENTIAL_ANIMAL_NOISE = "噪音、污染及環境維護_住宅內人與動物噪音"
    ENV_RESIDENTIAL_VEHICLE_NOISE = "噪音、污染及環境維護_住家、改裝車噪音"
    ENV_COMMERCIAL_INDUSTRIAL_NOISE = "噪音、污染及環境維護_營業場所、工廠及施工噪音"
    ENV_AIR_POLLUTION = "噪音、污染及環境維護_空氣污染"
    ENV_WATER_POLLUTION = "噪音、污染及環境維護_工廠排放廢水、河川污染"
    ENV_COMPREHENSIVE_POLLUTION = "噪音、污染及環境維護_綜合性環境污染"
    ENV_OTHER = "噪音、污染及環境維護_其他"

    # --- 建築管理 (Building Management) ---
    BUILDING_SOCIAL_HOUSING_MANAGEMENT = "建築管理_社會住宅管理"
    BUILDING_GENERAL_ILLEGAL_CONSTRUCTION_REPORT = "建築管理_一般違建查報"
    BUILDING_ONGOING_ILLEGAL_CONSTRUCTION_REPORT = "建築管理_興建中違建查報"
    BUILDING_ILLEGAL_CONSTRUCTION_DEMOLITION = "建築管理_已查報違建拆除問題"
    BUILDING_ILLEGAL_SIGNBOARD_REPORT = "建築管理_違規招牌或樹立廣告物查報"
    BUILDING_APARTMENT_MANAGEMENT = "建築管理_公寓大廈管理問題"
    BUILDING_REGULATION_ISSUE = "建築管理_建築法規問題"
    BUILDING_PUBLIC_SAFETY = "建築管理_建築物公共安全問題"
    BUILDING_CONSTRUCTION_DAMAGE_TO_NEIGHBORS = "建築管理_領有建造執照施工損鄰"
    BUILDING_OTHER = "建築管理_其他"

    # --- 動物保護及寵物管理 (Animal Protection and Pet Management) ---
    ANIMAL_BEE_SNAKE_REMOVAL = "動物保護及寵物管理_捕蜂、抓蛇"
    ANIMAL_RESCUE_REPORT = "動物保護及寵物管理_動物受困、受傷通報"
    ANIMAL_SHELTER_ADOPTION = "動物保護及寵物管理_動物收容及認養問題"
    ANIMAL_DOG_OFF_LEASH = "動物保護及寵物管理_遛狗未繫繩"
    ANIMAL_IMPROPER_KEEPING = "動物保護及寵物管理_不當飼養"
    ANIMAL_ILLEGAL_BREEDER_VIOLATION = "動物保護及寵物管理_非法業者及業者違規問題"
    ANIMAL_OTHER = "動物保護及寵物管理_其他"

    # --- 教育及體育 (Education and Sports) ---
    EDU_TEACHER_RECRUITMENT = "教育及體育_教師介聘甄選"
    EDU_HIGH_SCHOOL_ISSUE = "教育及體育_高級中等學校問題"
    EDU_JUNIOR_HIGH_SCHOOL_ISSUE = "教育及體育_國中學校問題"
    EDU_ELEMENTARY_SCHOOL_ISSUE = "教育及體育_國小學校問題"
    EDU_CRAM_SCHOOL_ISSUE = "教育及體育_補教問題"
    EDU_KINDERGARTEN_ISSUE = "教育及體育_幼兒園問題"
    EDU_LIFELONG_LEARNING = "教育及體育_社區大學、樂齡學習等終身教育問題"
    EDU_SPECIAL_EDUCATION = "教育及體育_特殊教育問題"
    EDU_SCHOOL_SPORTS = "教育及體育_學校體育問題"
    EDU_SPORTS_EVENTS_VENUE_MANAGEMENT = "教育及體育_體育活動及場務管理"
    EDU_OTHER = "教育及體育_其他"

    # --- 勞動行政 (Labor Administration) ---
    LABOR_LAW_CONSULTATION = "勞動行政_勞工法令諮詢"
    LABOR_DISPUTE_MEDIATION = "勞動行政_勞資糾紛協調"
    LABOR_EMPLOYMENT_SERVICE_TRAINING = "勞動行政_就業服務及職業訓練"
    LABOR_MIGRANT_WORKER_AFFAIRS = "勞動行政_移工業務"
    LABOR_VIOLATION_REPORT = "勞動行政_檢舉公司（雇主）違反勞動法規"
    LABOR_EMPLOYMENT_DISCRIMINATION = "勞動行政_就業歧視"
    LABOR_DISABILITY_EMPLOYMENT = "勞動行政_身障就業"
    LABOR_OTHER = "勞動行政_其他"

    # --- 社會救助及社會福利 (Social Assistance and Welfare) ---
    WELFARE_SOCIAL_ASSISTANCE = (
        "社會救助及社會福利_社會救助（中、低收入戶、急難救助及馬上關懷等）"
    )
    WELFARE_DISABILITY_BENEFITS_BUS = "社會救助及社會福利_身心障礙福利及復康巴士"
    WELFARE_SENIOR_BENEFITS_LONG_TERM_CARE = (
        "社會救助及社會福利_銀髮族福利、長期照顧及日間照顧"
    )
    WELFARE_CHILD_YOUTH_BENEFITS = (
        "社會救助及社會福利_兒少福利、弱勢兒少生活扶助、緊急生活扶助及醫療補助"
    )
    WELFARE_RENTAL_SUBSIDY = "社會救助及社會福利_住宅租金補貼"
    WELFARE_FAMILY_SERVICE_CENTER = "社會救助及社會福利_家庭服務中心"
    WELFARE_PROTECTION_SERVICES = (
        "社會救助及社會福利_家庭暴力、性侵害、兒少保護及性騷擾等防治工作"
    )
    WELFARE_CIVIL_ORGANIZATION_GUIDANCE = "社會救助及社會福利_人民團體組織輔導"
    WELFARE_OTHER = "社會救助及社會福利_其他"

    # --- 婦幼、新住民福利及健康照顧 (Women, Children, New Immigrant Welfare, and Health Care) ---
    WOMEN_CHILDREN_NEW_IMMIGRANT_WELFARE = (
        "婦幼、新住民福利及健康照顧_婦女、新住民福利、特殊境遇及補助"
    )
    WOMEN_CHILDREN_NEW_IMMIGRANT_CENTER_CARE = (
        "婦幼、新住民福利及健康照顧_婦女館、親子館管理及托育服務"
    )
    WOMEN_CHILDREN_NEW_IMMIGRANT_EMPOWERMENT = (
        "婦幼、新住民福利及健康照顧_婦女培力、性別平等業務"
    )
    WOMEN_CHILDREN_NEW_IMMIGRANT_CHILDCARE_SUBSIDY = (
        "婦幼、新住民福利及健康照顧_0–2 歲育兒經濟補助"
    )
    WOMEN_CHILDREN_NEW_IMMIGRANT_EARLY_INTERVENTION = (
        "婦幼、新住民福利及健康照顧_兒童早期療育服務及補助"
    )
    WOMEN_CHILDREN_NEW_IMMIGRANT_OTHER = "婦幼、新住民福利及健康照顧_其他"

    # --- 文化藝術及圖書管理 (Cultural Arts and Library Management) ---
    CULTURE_CULTURAL_HERITAGE = "文化藝術及圖書管理_文化資產問題"
    CULTURE_ARTS_EVENTS = "文化藝術及圖書管理_藝文展演活動"
    CULTURE_LIBRARY_MANAGEMENT = "文化藝術及圖書管理_圖書館、閱覽室及館舍管理"
    CULTURE_ARTS_VENUE_MANAGEMENT = "文化藝術及圖書管理_藝文館舍管理"
    CULTURE_OTHER = "文化藝術及圖書管理_其他"

    # --- 工商、經濟及稅務 (Commerce, Economy, and Taxation) ---
    BUSINESS_UTILITIES_ISSUE = "工商、經濟及稅務_水、電、瓦斯等公用事業問題"
    BUSINESS_REGISTRATION_ISSUE = "工商、經濟及稅務_工商登記問題"
    BUSINESS_MARKET_VENDOR_MANAGEMENT = "工商、經濟及稅務_市場攤販管理"
    BUSINESS_ILLEGAL_FACTORY_REPORT = "工商、經濟及稅務_檢舉工廠違規營業"
    BUSINESS_ILLEGAL_STORE_REPORT = "工商、經濟及稅務_檢舉商店違規營業"
    BUSINESS_ILLEGAL_HOTEL_REPORT = "工商、經濟及稅務_檢舉旅館、民宿違規營業"
    BUSINESS_TAX_ISSUE = "工商、經濟及稅務_稅務問題"
    BUSINESS_OTHER = "工商、經濟及稅務_其他"

    # --- 民政業務 (Civil Affairs) ---
    CIVIL_HOUSEHOLD_REGISTRATION = "民政業務_戶政服務"
    CIVIL_FUNERAL_SERVICES = "民政業務_殯葬禮儀"
    CIVIL_RELIGIOUS_AFFAIRS = "民政業務_宗教事務"
    CIVIL_MILITARY_SERVICE = "民政業務_兵役問題"
    CIVIL_OTHER = "民政業務_其他"

    # --- 感謝函、服務品質及網站／APP 管理問題 (Thank You Letters, Service Quality, and Website/APP Management) ---
    SERVICE_THANK_YOU_LETTER = "感謝函、服務品質及網站／APP 管理問題_感謝函"
    SERVICE_ATTITUDE_ISSUE = "感謝函、服務品質及網站／APP 管理問題_服務態度問題"
    SERVICE_EFFICIENCY_ISSUE = "感謝函、服務品質及網站／APP 管理問題_行政效率問題"
    SERVICE_PROFESSIONALISM_ISSUE = "感謝函、服務品質及網站／APP 管理問題_專業知識問題"
    SERVICE_WEBSITE_APP_ISSUE = (
        "感謝函、服務品質及網站／APP 管理問題_市府網站或 APP 管理問題"
    )
    SERVICE_OTHER = "感謝函、服務品質及網站／APP 管理問題_其他"

    # --- 市民卡業務 (Citizen Card Services) ---
    CARD_GENERAL_APPLICATION = "市民卡業務_一般卡申辦問題"
    CARD_SENIOR_APPLICATION = "市民卡業務_敬老卡申辦問題"
    CARD_COMPASSIONATE_APPLICATION = "市民卡業務_愛心卡、愛心陪伴卡申辦問題"
    CARD_VOLUNTEER_APPLICATION = "市民卡業務_志工卡申辦問題"
    CARD_STUDENT_APPLICATION = "市民卡業務_學生卡申辦問題"
    CARD_MOBILE_COBRANDED_APPLICATION = "市民卡業務_行動卡、聯名卡申辦問題"
    CARD_USAGE_ISSUE = "市民卡業務_卡片感應及使用問題"
    CARD_BENEFITS_SUGGESTION = "市民卡業務_市民卡優惠及加值服務建議"
    CARD_OTHER = "市民卡業務_其他"

    # --- 衛生行政 (Health Administration) ---
    HEALTH_FOOD_SAFETY = "衛生行政_食品安全衛生"
    HEALTH_MEDICAL_MANAGEMENT = "衛生行政_醫療管理"
    HEALTH_PHARMACEUTICALS_COSMETICS = "衛生行政_藥品及化妝品管理"
    HEALTH_DISEASE_CONTROL_VACCINATION = "衛生行政_傳染病防治及預防接種"
    HEALTH_TOBACCO_HAZARD_CONTROL = "衛生行政_菸害防制"
    HEALTH_SUICIDE_PREVENTION_MENTAL_HEALTH = "衛生行政_自殺防治及心理健康"
    HEALTH_OTHER = "衛生行政_其他"

    # --- 地政服務 (Land Administration Services) ---
    LAND_EXPROPRIATION = "地政服務_土地徵收"
    LAND_READJUSTMENT = "地政服務_土地重劃"
    LAND_SURVEYING = "地政服務_土地測量"
    LAND_CADASTRAL_RESURVEY = "地政服務_地籍圖重測"
    LAND_REAL_ESTATE_TRANSACTION = "地政服務_不動產交易"
    LAND_REGISTRATION = "地政服務_土地及建物登記"
    LAND_ILLEGAL_USE_REPORT = "地政服務_檢舉土地違規使用"
    LAND_OTHER = "地政服務_其他"

    # --- 消防行政 (Fire Administration) ---
    FIRE_EQUIPMENT_SAFETY_INSPECTION = "消防行政_消防設備、安全檢查"
    FIRE_GAS_CYLINDER_STORAGE = "消防行政_瓦斯桶儲放問題"
    FIRE_HYDRANT_ISSUE = "消防行政_消防栓（設置、移位、告示牌）"
    FIRE_ALLEY_OBSTRUCTION = "消防行政_防火巷違建、堆放雜物"
    FIRE_OTHER = "消防行政_其他"

    # --- 政風行政 (Government Ethics Administration) ---
    ETHICS_BRIBERY = "政風行政_行、收賄"
    ETHICS_ADMINISTRATIVE_MALFEASANCE = "政風行政_行政違失"
    ETHICS_OTHER_MALPRACTICE = "政風行政_其他瀆職情形"

    # --- 其他類別 (Other Categories) ---
    OTHER_REPORTS = "其他類別_其他檢舉案件"
    OTHER_SUGGESTIONS = "其他類別_其他建議、諮詢或陳情"


class DetailedPetitionCategory(str, Enum):
    """
    Enum for detailed petition categories for 2024
    （案件類別、子類別完整對照表）
    """

    # ────────────────── 交通號誌、標誌、標線及大眾運輸 ──────────────────
    TRAFFIC_BUS_STOP_AND_SHELTER = (
        "交通號誌、標誌、標線及大眾運輸_公車問題及站牌、候車亭設施管理"
    )
    TRAFFIC_SIGN_MARKING_MIRROR_SETUP = (
        "交通號誌、標誌、標線及大眾運輸_交通標誌、標線、反射鏡設置或移除"
    )
    TRAFFIC_MISC = "交通號誌、標誌、標線及大眾運輸_其他"
    TRAFFIC_SIGNAL_ADDITION_TIMING = (
        "交通號誌、標誌、標線及大眾運輸_交通號誌增設或紅綠燈秒數調整"
    )
    TRAFFIC_ROADSIDE_PARKING = "交通號誌、標誌、標線及大眾運輸_路邊停車格問題"
    TRAFFIC_UBIKE = "交通號誌、標誌、標線及大眾運輸_公共自行車(U-Bike)租賃問題"
    TRAFFIC_SIGNAL_FAILURE = (
        "交通號誌、標誌、標線及大眾運輸_交通號誌(紅綠燈)故障或損壞傾斜"
    )
    TRAFFIC_SIGN_MIRROR_DAMAGE = (
        "交通號誌、標誌、標線及大眾運輸_交通標誌牌面、反射鏡損壞傾斜"
    )
    TRAFFIC_MRT_OPERATION = "交通號誌、標誌、標線及大眾運輸_捷運營運及管理"
    TRAFFIC_MRT_CONSTRUCTION = "交通號誌、標誌、標線及大眾運輸_捷運建設工程相關問題"
    TRAFFIC_BUS_REALTIME_SYSTEM = "交通號誌、標誌、標線及大眾運輸_公車動態系統問題"
    TRAFFIC_PARKING_FEE = "交通號誌、標誌、標線及大眾運輸_停車費問題"
    TRAFFIC_TAXI_AND_STANDS = (
        "交通號誌、標誌、標線及大眾運輸_計程車問題及招呼站設施管理"
    )
    TRAFFIC_FREE_CITIZEN_BUS = "交通號誌、標誌、標線及大眾運輸_免費市民公車(樂活巴)問題"

    # ──────────────────────── 路霸排除 ────────────────────────
    ROADBLOCK_ROAD_ARCADE_SIDEPATH = "路霸排除_占用道路、騎樓及人行道"
    ROADBLOCK_ABANDONED_NO_PLATE = "路霸排除_無牌廢棄車查報"
    ROADBLOCK_ABANDONED_WITH_PLATE = "路霸排除_有牌廢棄車查報"
    ROADBLOCK_MISC = "路霸排除_其他"
    ROADBLOCK_AD_VEHICLE_OCCUPANCY = "路霸排除_廣告車輛長期占用停車格"

    # ──────────────────── 噪音、污染及環境維護 ───────────────────
    NOISE_DIRTY_SPOT = "噪音、污染及環境維護_髒亂點查報"
    NOISE_HOME_MODIFIED_CAR = "噪音、污染及環境維護_住家、改裝車噪音"
    NOISE_ENV_POLLUTION = "噪音、污染及環境維護_綜合性環境污染"
    NOISE_MISC = "噪音、污染及環境維護_其他"
    NOISE_COMMERCIAL_FACTORY_CONSTRUCTION = (
        "噪音、污染及環境維護_營業場所、工廠及施工噪音"
    )
    NOISE_AIR_POLLUTION = "噪音、污染及環境維護_空氣污染"
    NOISE_ANIMAL_INDOOR = "噪音、污染及環境維護_住宅內人與動物噪音"
    NOISE_GARBAGE_TRUCK_ROUTE = "噪音、污染及環境維護_垃圾車清運動線及管理"
    NOISE_FACTORY_WASTEWATER_RIVER = "噪音、污染及環境維護_工廠排放廢水、河川污染"
    NOISE_ILLEGAL_ADS = "噪音、污染及環境維護_違規張貼廣告物"
    NOISE_BULK_WASTE_COLLECTION = "噪音、污染及環境維護_廢棄物清運預約"
    NOISE_ANIMAL_CARCASS_REMOVAL = "噪音、污染及環境維護_犬貓屍體清除"

    # ───────────────────────── 其他類別 ─────────────────────────
    OTHER_SUGGESTION_CONSULTATION = "其他類別_其他建議、諮詢或陳情"
    OTHER_REPORT = "其他類別_其他檢舉案件"

    # ──────────────────── 警政及交通裁罰業務 ────────────────────
    POLICE_MISC = "警政及交通裁罰業務_其他"
    POLICE_DISCIPLINE = "警政及交通裁罰業務_警政風紀"
    POLICE_TRAFFIC_TICKET_APPEAL = "警政及交通裁罰業務_交通罰單申訴"
    POLICE_TRAFFIC_GUIDANCE_CONGESTION = "警政及交通裁罰業務_交通疏導或壅塞通報"
    POLICE_PUBLIC_SECURITY = "警政及交通裁罰業務_治安維護"
    POLICE_OBSCENITY = "警政及交通裁罰業務_妨害風化(俗)"
    POLICE_SPEED_CAMERA_SETUP = "警政及交通裁罰業務_闖紅燈(超速)照相桿增設或維護"
    POLICE_CCTV_ISSUE = "警政及交通裁罰業務_監視器問題"
    POLICE_TOWING_DISPUTE = "警政及交通裁罰業務_車輛拖吊爭議"

    # ──────────────────────── 建築管理 ────────────────────────
    BUILDING_APARTMENT_MANAGEMENT = "建築管理_公寓大廈管理問題"
    BUILDING_ILLEGAL_CONSTRUCTION = "建築管理_一般違建查報"
    BUILDING_PUBLIC_SAFETY = "建築管理_建築物公共安全問題"
    BUILDING_MISC = "建築管理_其他"
    BUILDING_UNDER_CONSTRUCTION_VIOLATION = "建築管理_興建中違建查報"
    BUILDING_REGULATION_ISSUE = "建築管理_建築法規問題"
    BUILDING_ILLEGAL_SIGNBOARD = "建築管理_違規招牌或樹立廣告物查報"
    BUILDING_SOCIAL_HOUSING = "建築管理_社會住宅管理"
    BUILDING_CONSTRUCTION_DAMAGE = "建築管理_領有建造執照施工損鄰"
    BUILDING_ILLEGAL_REMOVAL_ISSUE = "建築管理_已查報違建拆除問題"

    # ─────────────────── 道路、水溝維護 ────────────────────
    ROAD_SURFACE_DAMAGE = "道路、水溝維護_路面不平整或掏空破洞"
    ROAD_MAINTENANCE_MISC = "道路、水溝維護_其他"
    ROAD_CONSTRUCTION_SCHEDULE_MANAGEMENT = (
        "道路、水溝維護_道路施工時間、交通管制及安全管理問題"
    )
    ROAD_SIDE_DITCH_CLEANING = "道路、水溝維護_道路側溝清淤或惡臭處理"
    ROAD_DRAIN_COVER_REPAIR = "道路、水溝維護_水溝溝蓋維修"
    ROAD_FLOODING = "道路、水溝維護_道路淹(積)水通報"
    ROAD_MANHOLE_NOISE = "道路、水溝維護_孔蓋異音"
    ROAD_CABLE_UNDERGROUND = "道路、水溝維護_電纜下地或纜線垂落"
    ROAD_OIL_STAIN_CLEAN = "道路、水溝維護_路面油漬清除"

    # ──────────── 路燈、路樹及公園管理維護 ────────────
    LIGHT_TREE_PARK_MISC = "路燈、路樹及公園管理維護_其他"
    PARK_GREENERY_TREE_MAINTENANCE = "路燈、路樹及公園管理維護_公園、綠地及路樹養護"
    STREET_LIGHT_FAILURE = "路燈、路樹及公園管理維護_路燈故障"
    PARK_FACILITY_DAMAGE = "路燈、路樹及公園管理維護_公園設施損壞"
    STREET_TREE_FALL = "路燈、路樹及公園管理維護_路樹傾倒"
    STREET_LIGHT_NEW_RELOCATION = "路燈、路樹及公園管理維護_路燈新增或遷移申請"
    PARK_NEW_SUGGESTION = "路燈、路樹及公園管理維護_新闢公園建議案"

    # ───────────────────── 衛生行政 ─────────────────────
    HEALTH_FOOD_SAFETY = "衛生行政_食品安全衛生"
    HEALTH_DRUG_COSMETIC = "衛生行政_藥品及化妝品管理"
    HEALTH_MEDICAL_MANAGEMENT = "衛生行政_醫療管理"
    HEALTH_TOBACCO_CONTROL = "衛生行政_菸害防制"
    HEALTH_MISC = "衛生行政_其他"
    HEALTH_DISEASE_PREVENTION = "衛生行政_傳染病防治及預防接種"
    HEALTH_SUICIDE_MENTAL_HEALTH = "衛生行政_自殺防治及心理健康"

    # ──────────────────── 教育及體育 ───────────────────
    EDU_ELEMENTARY_SCHOOL = "教育及體育_國小學校問題"
    EDU_JUNIOR_HIGH_SCHOOL = "教育及體育_國中學校問題"
    EDU_MISC = "教育及體育_其他"
    EDU_SENIOR_HIGH_SCHOOL = "教育及體育_高級中等學校問題"
    EDU_CRAM_SCHOOL = "教育及體育_補教問題"
    EDU_SPORTS_EVENTS_FACILITY = "教育及體育_體育活動及場務管理"
    EDU_KINDERGARTEN = "教育及體育_幼兒園問題"
    EDU_SPECIAL_EDUCATION = "教育及體育_特殊教育問題"
    EDU_TEACHER_TRANSFER = "教育及體育_教師介聘甄選"
    EDU_COMMUNITY_LIFELONG = "教育及體育_社區大學、樂齡學習等終身教育問題"
    EDU_SCHOOL_SPORTS = "教育及體育_學校體育問題"

    # ───────────────────── 勞動行政 ─────────────────────
    LABOR_LAW_VIOLATION_REPORT = "勞動行政_檢舉公司(雇主)違反勞動法規"
    LABOR_LEGAL_CONSULT = "勞動行政_勞工法令諮詢"
    LABOR_MISC = "勞動行政_其他"
    LABOR_DISPUTE_MEDIATION = "勞動行政_勞資糾紛協調"
    LABOR_MIGRANT_LABOR = "勞動行政_移工業務"
    LABOR_EMPLOYMENT_TRAINING = "勞動行政_就業服務及職業訓練"
    LABOR_EMPLOYMENT_DISCRIMINATION = "勞動行政_就業歧視"
    LABOR_DISABLED_EMPLOYMENT = "勞動行政_身障就業"

    # ──────────────────── 工商、經濟及稅務 ───────────────────
    BIZ_ILLEGAL_SHOP = "工商、經濟及稅務_檢舉商店違規營業"
    BIZ_UTILITY = "工商、經濟及稅務_水、電、瓦斯等公用事業問題"
    BIZ_TAX_ISSUE = "工商、經濟及稅務_稅務問題"
    BIZ_MISC = "工商、經濟及稅務_其他"
    BIZ_ILLEGAL_FACTORY = "工商、經濟及稅務_檢舉工廠違規營業"
    BIZ_MARKET_VENDOR = "工商、經濟及稅務_市場攤販管理"
    BIZ_BUSINESS_REGISTRATION = "工商、經濟及稅務_工商登記問題"
    BIZ_ILLEGAL_HOTEL = "工商、經濟及稅務_檢舉旅館、民宿違規營業"

    # ────────────── 社會救助及社會福利 ──────────────
    WELFARE_MISC = "社會救助及社會福利_其他"
    WELFARE_DISABILITY_SERVICE = "社會救助及社會福利_身心障礙福利及復康巴士"
    WELFARE_SENIOR_LONG_TERM_CARE = "社會救助及社會福利_銀髮族福利、長期照顧及日間照顧"
    WELFARE_RELIEF_ASSISTANCE = (
        "社會救助及社會福利_社會救助(中、低收入戶、急難救助及馬上關懷等)"
    )
    WELFARE_WOMEN_CHILD_SUPPORT = (
        "社會救助及社會福利_婦女福利、特殊境遇家庭扶助、生育津貼及育兒津貼"
    )
    WELFARE_PARENT_CHILD_CENTER = (
        "社會救助及社會福利_婦女(幼)館、親子館及公設民營托嬰中心管理"
    )
    WELFARE_CHILD_EARLY_TREATMENT = "社會救助及社會福利_兒少福利、兒童早療補助、弱勢兒少生活扶助、緊急生活扶助及醫療補助"
    WELFARE_DOMESTIC_VIOLENCE = (
        "社會救助及社會福利_家庭暴力、性侵害、兒少保護及性騷擾等防治工作"
    )
    WELFARE_RENT_SUBSIDY = "社會救助及社會福利_住宅租金補貼問題"
    WELFARE_NGO_SUPPORT = "社會救助及社會福利_人民團體組織輔導"
    WELFARE_FAMILY_SERVICE_CENTER = "社會救助及社會福利_家庭服務中心"

    # ──────────────────────── 地政服務 ────────────────────────
    LAND_ILLEGAL_USE_REPORT = "地政服務_檢舉土地違規使用"
    LAND_MISC = "地政服務_其他"
    LAND_TRANSACTION = "地政服務_不動產交易"
    LAND_EXPROPRIATION = "地政服務_土地徵收"
    LAND_REGISTRATION = "地政服務_土地及建物登記"
    LAND_REPLOT = "地政服務_土地重劃"
    LAND_SURVEY = "地政服務_土地測量"
    LAND_CADASTRE_REMEASURE = "地政服務_地籍圖重測"

    # ─────────────────────── 消防行政 ───────────────────────
    FIRE_EQUIPMENT_CHECK = "消防行政_消防設備、安全檢查"
    FIRE_ALLEY_ILLEGAL_STRUCTURE = "消防行政_防火巷違建、堆放雜物"
    FIRE_MISC = "消防行政_其他"
    FIRE_GAS_CYLINDER_STORAGE = "消防行政_瓦斯桶儲放問題"
    FIRE_HYDRANT = "消防行政_消防栓(設置、移位、告示牌)"

    # ────────── 感謝函、服務品質及網站、APP管理問題 ──────────
    THANK_YOU_LETTER = "感謝函、服務品質及網站、APP管理問題_感謝函"
    WEBSITE_APP_MANAGEMENT = "感謝函、服務品質及網站、APP管理問題_市府網站或APP管理問題"
    SERVICE_ATTITUDE = "感謝函、服務品質及網站、APP管理問題_服務態度問題"
    ADMIN_EFFICIENCY = "感謝函、服務品質及網站、APP管理問題_行政效率問題"
    PROFESSIONAL_KNOWLEDGE = "感謝函、服務品質及網站、APP管理問題_專業知識問題"
    SERVICE_MISC = "感謝函、服務品質及網站、APP管理問題_其他"

    # ────────────────── 動物收容、保護及捕捉 ──────────────────
    ANIMAL_MISC = "動物收容、保護及捕捉_其他"
    ANIMAL_SHELTER_ADOPTION = "動物收容、保護及捕捉_動物收容及認養問題"
    ANIMAL_RESCUE_INJURY = "動物收容、保護及捕捉_動物受困、受傷通報"
    ANIMAL_BEE_SNAKE_CAPTURE = "動物收容、保護及捕捉_捕蜂、抓蛇"

    # ──────────────────── 文化藝術及圖書管理 ───────────────────
    CULTURE_LIBRARY_MANAGEMENT = "文化藝術及圖書管理_圖書館、閱覽室及館舍管理"
    CULTURE_ART_EVENT = "文化藝術及圖書管理_藝文展演活動"
    CULTURE_MISC = "文化藝術及圖書管理_其他"
    CULTURE_VENUE_MANAGEMENT = "文化藝術及圖書管理_藝文館舍管理"
    CULTURE_HERITAGE = "文化藝術及圖書管理_文化資產問題"

    # ──────────────────────── 民政業務 ────────────────────────
    CIVIL_HOUSEHOLD_SERVICE = "民政業務_戶政服務"
    CIVIL_MISC = "民政業務_其他"
    CIVIL_RELIGIOUS_AFFAIRS = "民政業務_宗教事務"
    CIVIL_FUNERAL_SERVICE = "民政業務_殯葬禮儀"
    CIVIL_MILITARY_SERVICE = "民政業務_兵役問題"

    # ──────────────────────── 政風行政 ────────────────────────
    AUDIT_ADMIN_MISCONDUCT = "政風行政_行政違失"
    AUDIT_MISC = "政風行政_其他瀆職情形"
    AUDIT_BRIBERY = "政風行政_行、收賄"

    # ──────────────────────── 市民卡業務 ───────────────────────
    CITIZEN_CARD_MISC = "市民卡業務_其他"
    CITIZEN_CARD_DISCOUNT_TOPUP = "市民卡業務_市民卡優惠及加值服務建議"
    CITIZEN_CARD_USAGE_ISSUE = "市民卡業務_卡片感應及使用問題"
    CITIZEN_CARD_STUDENT_APPLICATION = "市民卡業務_學生卡申辦問題"
    CITIZEN_CARD_GENERAL_APPLICATION = "市民卡業務_一般卡申辦問題"
    CITIZEN_CARD_MOBILE_JOINT = "市民卡業務_行動卡、聯名卡申辦問題"
    CITIZEN_CARD_VOLUNTEER_APPLICATION = "市民卡業務_志工卡申辦問題"
    CITIZEN_CARD_SENIOR_APPLICATION = "市民卡業務_敬老卡申辦問題"

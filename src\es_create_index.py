import argparse
import os
import sys

from dotenv import load_dotenv

from helper import initLogger

# from repository.elasticsearch_repo import ElasticsearchRepository
from repository.gpt_summary_repo import ElasticsearchSummarizer


def parse_input_args():
    parser = argparse.ArgumentParser()
    # Add the argument
    parser.add_argument(
        "-env",
        "--environment",
        choices=["LOCAL", "DEV", "PROD"],
        help="Specify the environment to use: LOCAL, DEV, or PROD",
    )

    args = parser.parse_args()
    logger.info(f"args: {args}")

    dct_input_args = {k: v for k, v in vars(args).items() if v}

    return dct_input_args


if __name__ == "__main__":
    # ================== initialize logger ====================
    logDirPath = "logs"
    logger = initLogger(logDirPath, "DEBUG")
    logger.info("logger being initialized successfully")

    dct_input_args = parse_input_args()
    logger.info(f"dct_input_args: {dct_input_args}")
    os.environ["IS_LOCAL_SSH_PORT_FORWARDED"] = "True"
    os.environ["TYCG_BE_RUNTIME_ENV"] = dct_input_args["environment"]

    if dct_input_args["environment"] == "DEV":
        load_dotenv("es/.env.dev")
    elif dct_input_args["environment"] == "PROD":
        load_dotenv("es/.env.prod")

    # ================== Initialize ES repository ====================
    try:
        # es_repo = ElasticsearchRepository()
        es_repo = ElasticsearchSummarizer()
    except Exception as e:
        print(f"{e}")
        sys.exit(1)

    index_base_name = input("Type base index name to continue: ")
    str_new_index_name = input(
        f"Type new index name(Will be appended to index_base_name. e.g. {index_base_name}-The name you type) to continue: "
    )
    str_new_index_version = input("Type new index version: ")
    str_new_index = f"{index_base_name}-{str_new_index_name}-{str_new_index_version}"

    str_alias_name = f"{index_base_name}-{str_new_index_name}-alias"

    is_point_to_latest_alias = input("Do you want to point to latest alias? (y/n): ")
    if is_point_to_latest_alias.lower() == "y":
        str_latest_alias_name = f"{index_base_name}-latest-alias"
    else:
        str_latest_alias_name = None

    logger.info(f"index_base_name: {index_base_name}")
    logger.info(f"str_new_index: {str_new_index}")
    logger.info(f"str_alias_name: {str_alias_name}")
    logger.info(f"str_latest_alias_name: {str_latest_alias_name}")

    input_confirm = input("Do you want to continue? (y/n): ")
    if input_confirm.lower() != "y":
        logger.info("Exiting...")
        sys.exit(0)

    es_repo.es_create_index(str_new_index, is_delete_index=False)
    es_repo.put_alias(str_new_index, str_alias_name)
    if str_latest_alias_name:
        es_repo.put_alias(str_new_index, str_latest_alias_name)

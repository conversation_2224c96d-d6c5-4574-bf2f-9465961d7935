# NER Worker & GPT Summarizer

本專案是一個圍繞 Elasticsearch 的資料處理管道，旨在對市民陳情案件進行 AI 加值分析。主要包含兩個核心元件：

1.  **NER Worker**: 對案件內容進行命名實體辨識 (Named Entity Recognition)，擴充資料維度。
2.  **GPT Summarizer**: 根據案件分類，利用大型語言模型 (LLM) 生成每週的摘要報告與統計數據。

詳細的技術文件請參考 `docs` 資料夾：

-   [**架構設計 (Architecture Design)**](docs/architecture.md)
-   [**資料模型 (Data Models)**](docs/database.md)
-   [**核心模組詳解 (Core Modules)**](docs/modules.md)

---

## 技術棧 (Tech Stack)

-   **程式語言 (Programming Language)**: Python 3.10
-   **核心框架與函式庫 (Core Frameworks & Libraries)**:
    -   **Elasticsearch**: 作為核心資料庫，用於儲存、搜尋和分析案件資料。
    -   **OpenAI API**: 用於與大型語言模型 (LLM) 互動，生成摘要報告。
    -   **OpenCC (opencc-python-reimplemented)**: 用於繁簡中文轉換，確保文字資料的一致性。
    -   **Numpy**: 用於高效的數值計算與資料處理。
-   **環境與依賴管理 (Environment & Dependency Management)**:
    -   **Poetry**: 用於管理 Python 套件依賴與虛擬環境。
    -   **python-dotenv**: 用於管理 `.env` 檔案中的環境變數，實現設定與程式碼分離。
-   **容器化 (Containerization)**:
    -   **Docker**: 提供容器化的執行環境 (Dockerfile)，確保開發與生產環境的一致性。

## 環境設定 (Setup)

### 1. 安裝依賴

本專案使用 [Poetry](https://python-poetry.org/) 管理依賴。請先確保您已安裝 Poetry。

```bash
# 安裝專案依賴
poetry install
```

### 2. 環境變數設定

專案的設定透過 `.env` 檔案進行管理。請將 `src/.env.example` (如果有的話，若無請手動建立) 複製為 `src/.env`，並填入必要的環境變數。

**核心環境變數:**

-   `TYCG_BE_RUNTIME_ENV`: 執行環境，可為 `PROD`, `DEV`, 或 `LOCAL`。
-   `ELASTICSEARCH_HOSTS`: Elasticsearch 主機位置 (例如: `["http://localhost:9200"]`)。
-   `ELASTICSEARCH_USER`: Elasticsearch 使用者名稱。
-   `ELASTICSEARCH_PASSWORD`: Elasticsearch 密碼。
-   `AZURE_OPENAI_API_KEY`: Azure OpenAI API 金鑰。
-   `AZURE_OPENAI_ENDPOINT`: Azure OpenAI 端點。
-   `OPENAI_API_KEY`: OpenAI API 金鑰 (作為備援)。
-   `LINE_NOTIFY_TOKEN_ADMIN`: 用於發送系統通知的 Line Notify Token。
-   `LINE_NOTIFY_TOKEN_USER`: 用於發送一般通知的 Line Notify Token。

---

## 執行方式 (Usage)

### 1. 執行 NER Worker

此 Worker 會查詢 Elasticsearch 中尚未被處理的案件，並進行 NER 標記。

可以透過兩種方式指定日期範圍：

**方式一：透過環境變數**

在 `.env` 檔案中設定：

```
DATA_API_START_DATE="2024/01/01"
DATA_API_END_DATE="2024/01/31"
```

然後執行：

```bash
poetry run python src/ner_worker_run.py
```

**方式二：透過命令列參數 (會覆蓋環境變數)**

```bash
poetry run python src/ner_worker_run.py -s 2024/01/01 -e 2024/01/31
```

**方式三：指定特定日期列表 (JSON 格式)**

在 `.env` 檔案中設定：

```
DATA_API_DATE='["2024/01/15", "2024/01/20"]'
```

然後執行：

```bash
poetry run python src/ner_worker_run.py
```

### 3. 執行 AI 案件分類 (Petition Categorization)

此腳本會讀取指定的 JSON 檔案，利用 OpenAI API 對每個案件進行分類，並將結果（包含原始資料、AI 分類、真實分類、準確度）寫入一個新的 JSON 檔案。

**執行方式:**

```bash
poetry run python src/categorize_petitions_run.py --input /path/to/your/input.json --output /path/to/your/output.json
```

**參數說明:**

-   `--input`: **[必需]** 輸入的 JSON 檔案路徑。檔案內容應為一個 JSON 陣列，其中每個物件需包含 `"petition_content"` (陳情內容) 和 `"sub_category"` (作為真實分類的參考) 欄位。
-   `--output`: **[必需]** 輸出的 JSON 檔案路徑，用於儲存處理完畢的結果。

**環境變數:**

-   `OPENAI_API_KEY`: 執行此腳本前，請務必在 `.env` 檔案中設定您的 OpenAI API 金鑰。

### 2. 執行 GPT Summarizer

此 Worker 會根據時間範圍與分類，生成摘要報告。

**執行上週的摘要:**

若無任何參數，預設會自動計算上週的日期範圍並執行。

```bash
poetry run python src/gpt_summary_run.py
```

**執行特定週次的摘要:**

在 `.env` 檔案中設定 `DATA_API_WEEK` (JSON 格式)：

```
# 處理 2024 年的第 49 週和第 51 週
DATA_API_WEEK='[49, 51]'
```

然後執行：

```bash
poetry run python src/gpt_summary_run.py
```

**執行特定日期範圍的摘要:**

```bash
poetry run python src/gpt_summary_run.py -s 2024/12/01 -e 2024/12/31
```

**僅更新統計數據，不重新生成摘要:**

有時候可能只需要重新計算案件數、增長率等數據，而不需要重新花費成本呼叫 GPT。

在 `.env` 檔案中設定：

```
UPDATE_STATS_ONLY="True"
```

然後正常執行即可。

import time

from models import DetailedPetitionCategory, PetitionOutput
from repository.file_repository import (
    get_ground_truth,
    get_petition_data,
    write_petition_data,
)
from repository.openai_repository import TokenUsage, get_ai_category_response


def validate_category_with_retry(
    ai_response, max_retries: int = 3
) -> tuple[str, str, bool, int]:
    """
    Validates AI response category with retry mechanism.

    Args:
        ai_response: The AI response object
        max_retries: Maximum number of retries (default: 3)

    Returns:
        Tuple of (main_category, sub_category, is_valid, validation_errors)
    """
    validation_errors = 0

    if not ai_response:
        return "analysis_failed", "analysis_failed", False, validation_errors

    for attempt in range(max_retries):
        try:
            new_category = ai_response.new_category
            validated_category = DetailedPetitionCategory(new_category)
            lst_category = validated_category.value.split("_")
            main_category = lst_category[0]
            sub_category = lst_category[1]
            return main_category, sub_category, True, validation_errors
        except ValueError as e:
            validation_errors += 1
            print(
                f"Validation attempt {attempt + 1} failed for category: {ai_response.new_category if ai_response else 'None'} - Error: {e}"
            )

            if attempt < max_retries - 1:
                # Wait a bit before retry
                time.sleep(0.5)
            else:
                print(
                    f"All {max_retries} validation attempts failed for category: {ai_response.new_category if ai_response else 'None'}"
                )
                return "analysis_failed", "analysis_failed", False, validation_errors

    return "analysis_failed", "analysis_failed", False, validation_errors


def categorize_petitions_use_case(input_path: str, output_path: str) -> None:
    """
    Orchestrates the petition categorization process.

    Args:
        input_path: The path to the input JSON file.
        output_path: The path to the output JSON file.
    """
    petition_data = get_petition_data(input_path)
    output_data = []

    # Initialize total token usage tracking
    total_usage = TokenUsage()

    # Initialize validation error tracking
    total_validation_errors = 0

    # TODO 先跑200筆試試看
    for i, petition in enumerate(petition_data[:100]):
        print(f"Processing petition {i}/{min(5, len(petition_data))}...")

        ground_truth = get_ground_truth(petition.sub_category)
        ai_response, usage = get_ai_category_response(
            petition.petition_subject, petition.petition_content
        )

        # Accumulate token usage
        total_usage.prompt_tokens += usage.prompt_tokens
        total_usage.completion_tokens += usage.completion_tokens
        total_usage.total_tokens += usage.total_tokens
        total_usage.total_cost_usd += usage.total_cost_usd

        # Use retry mechanism for validation
        main_category, sub_category, is_valid, validation_errors = (
            validate_category_with_retry(ai_response)
        )
        total_validation_errors += validation_errors

        if is_valid:
            is_accurate = sub_category in ground_truth
        else:
            is_accurate = False

        output_data.append(
            PetitionOutput(
                original_data=petition.model_dump(),
                main_category=main_category,
                sub_category=sub_category,
                ground_truth_category=ground_truth,
                is_accurate=is_accurate,
            )
        )

    write_petition_data(output_path, output_data)

    # Print cost summary
    print("\n" + "=" * 50)
    print("OpenAI API Usage Summary")
    print("=" * 50)
    print(f"Total petitions processed: {len(output_data)}")
    print(f"Total validation errors: {total_validation_errors}")
    print(f"Prompt tokens: {total_usage.prompt_tokens:,}")
    print(f"Completion tokens: {total_usage.completion_tokens:,}")
    print(f"Total tokens: {total_usage.total_tokens:,}")
    print(f"Total cost: ${total_usage.total_cost_usd:.4f} USD")
    print("=" * 50)

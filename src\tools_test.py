import argparse
import datetime
import os
import sys

from dotenv import load_dotenv

from helper import initLogger
from repository.gpt_summary_repo import ElasticsearchSummarizer


def parse_input_args():
    parser = argparse.ArgumentParser()
    # Add the argument
    parser.add_argument(
        "-env",
        "--environment",
        choices=["LOCAL", "DEV", "PROD"],
        help="Specify the environment to use: LOCAL, DEV, or PROD",
    )
    parser.add_argument(
        "-idx",
        "--index",
        type=str,
        help="Specify the environment to use: LOCAL, DEV, or PROD",
    )

    args = parser.parse_args()
    logger.info(f"args: {args}")

    dct_input_args = {k: v for k, v in vars(args).items() if v}

    return dct_input_args


def update_index_setting_by_reindex(es_repo: ElasticsearchSummarizer):
    # Get the current date and year
    tz = datetime.timezone(datetime.timedelta(hours=+8))
    t_current_date = datetime.datetime.now(tz)
    current_year = t_current_date.year

    dct_indices_alias = es_repo.list_indices_and_aliases()
    # logger.info(f"dct_indices_alias:{dct_indices_alias['indices']}")

    print(f"Current indices: {dct_indices_alias['indices']}")
    str_original_index = input(f"Choose old index to continue: ")
    print(
        f"Alias name for old index : {dct_indices_alias['aliases'][str_original_index]['aliases']}"
    )

    index_base_name = input(f"Type base index name to continue: ")
    str_new_index_name = input(
        f"Type new index name(Will be appended to index_base_name. e.g. {index_base_name}-The name you type) to continue: "
    )
    str_new_index_version = input(f"Type new index version: ")
    str_new_index = f"{index_base_name}-{str_new_index_name}-{str_new_index_version}"

    str_alias_name = f"{index_base_name}-{str_new_index_name}-alias"

    is_point_to_latest_alias = input(f"Do you want to point to latest alias? (y/n): ")
    if is_point_to_latest_alias.lower() == "y":
        str_latest_alias_name = f"{index_base_name}-latest-alias"
    else:
        str_latest_alias_name = None

    # index_base_name = "petition-case"
    # index_base_name = dct_input_args["index"]
    # str_original_index = f"{index_base_name}-{current_year}"
    # str_new_index = f"{index_base_name}-{current_year}-v2"
    # str_alias_name = f"{index_base_name}-{current_year}-alias"

    logger.info(f"index_base_name: {index_base_name}")
    logger.info(f"str_original_index: {str_original_index}")
    logger.info(f"str_new_index: {str_new_index}")
    logger.info(f"str_alias_name: {str_alias_name}")
    logger.info(f"str_latest_alias_name: {str_latest_alias_name}")

    input_confirm = input("Do you want to continue? (y/n): ")
    if input_confirm.lower() != "y":
        logger.info("Exiting...")
        sys.exit(0)

    es_repo.es_create_index(str_new_index, is_delete_index=True)
    es_repo.reindex(str_original_index, str_new_index)
    es_repo.update_alias(str_original_index, str_new_index, alias_name=str_alias_name)
    if str_latest_alias_name:
        es_repo.put_alias(str_new_index, str_latest_alias_name)
    es_repo.delete_index(str_original_index)

    # es_repo.es_create_index(str_original_index, alias_name=alias_name)
    # es_repo.reindex(str_new_index, str_original_index)
    # es_repo.delete_index(str_new_index)


def copy_data_to_another_index_by_reindex(es_repo: ElasticsearchSummarizer):
    # Use the _reindex API to transfer data
    dct_reindex_body = {
        "source": {
            "index": "petition-summary-2024-v2",
            "remote": {
                "host": "https://127.0.0.1:9200",
                "username": "elastic",
                "password": "source-password",
                "ca_certs": "es/certs/local/ca/ca.crt",
            },
            # "query": query
        },
        "dest": {
            "index": "petition-summary-2024",
        },
        "request_timeout": 1200,
    }

    print("Reindex...")
    response = es_repo.es.reindex(body=dct_reindex_body, wait_for_completion=True)
    print(response)


if __name__ == "__main__":

    # ================== initialize logger ====================
    logDirPath = "logs"
    logger = initLogger(logDirPath, "DEBUG")
    logger.info("logger being initialized successfully")

    dct_input_args = parse_input_args()
    logger.info(f"dct_input_args: {dct_input_args}")
    os.environ["IS_LOCAL_SSH_PORT_FORWARDED"] = "True"
    os.environ["TYCG_BE_RUNTIME_ENV"] = dct_input_args["environment"]

    if dct_input_args["environment"] == "DEV":
        load_dotenv("es/.env.dev")
    elif dct_input_args["environment"] == "PROD":
        load_dotenv("es/.env.prod")

    # ================== Initialize ES repository ====================
    try:
        es_repo = ElasticsearchSummarizer()
        update_index_setting_by_reindex(es_repo)
    except Exception as e:
        print(f"{e}")
        sys.exit(1)

import pytest
from pydantic import ValidationError

from settings import Settings


def test_settings_raises_error_if_api_key_is_missing(monkeypatch):
    """
    Test that the settings module raises a validation error
    if the OPENAI_API_KEY is not set either in the environment or a .env file.
    """
    # Ensure the environment variable is not set
    monkeypatch.delenv("OPENAI_API_KEY", raising=False)

    with pytest.raises(ValidationError) as excinfo:
        # Instantiate settings without loading any .env file
        Settings(_env_file=None)

    # Check that the error is about the missing field
    assert "Field required" in str(excinfo.value)
    assert "OPENAI_API_KEY" in str(excinfo.value)


def test_settings_loads_api_key_from_env(monkeypatch):
    """
    Test that the settings module correctly loads the OPENAI_API_KEY
    from an environment variable, ignoring any .env file.
    """
    # Set the environment variable
    monkeypatch.setenv("OPENAI_API_KEY", "test_key_from_env")

    # Instantiate without loading a .env file to isolate the test
    settings = Settings(_env_file=None)

    assert settings.OPENAI_API_KEY == "test_key_from_env"


def test_settings_loads_api_key_from_file(tmp_path, monkeypatch):
    """
    Test that the settings module correctly loads the OPENAI_API_KEY
    from a .env file.
    """
    # Ensure the environment variable is not set, so the file is used
    monkeypatch.delenv("OPENAI_API_KEY", raising=False)

    # Create a temporary .env file
    env_file = tmp_path / ".env"
    env_file.write_text("OPENAI_API_KEY=test_key_from_file")

    # Instantiate settings, loading from our temporary .env file
    settings = Settings(_env_file=env_file)

    assert settings.OPENAI_API_KEY == "test_key_from_file"
